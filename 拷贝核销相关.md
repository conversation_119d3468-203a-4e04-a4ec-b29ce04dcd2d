生效后会调用存盘监听

这个是生成cb表

```
snsoft.plat.bas.cor.service.impl.CorSourceSaveListener#afterSave
查找对应核销cb配置
snsoft.plat.bas.cor.service.impl.CorSaveListener#loopCor
```



这个是处理ct表：如何判断是新增插入还是删除

```
snsoft.plat.bas.cor.service.impl.CorServiceImpl#processTarget

//比较上层判断
snsoft.plat.bas.busi.service.impl.AbstractFunctionService#executeSave
```





让我帮您搜索关于拷贝核销流程的信息，尤其是涉及cb表和ct表的内容。

Search files...
根据搜索结果，我找到了关于拷贝核销流程的相关信息，尤其是关于cb表和ct表的内容。下面为您整理主要内容：

## 拷贝核销流程

### cb表和ct表的作用
- **cb表**：核销余额表，保存计划执行金额、已执行金额和待执行金额
- **ct表**：核销记录表，记录每次核销的金额明细

### 核销基本原理
1. cb表数据会被持续更新，记录总额和各种状态金额
2. ct表每次核销都会新增一条记录，类似日志记录

### 拷贝核销配置主要包括：

#### 1. 拷贝配置(datacopy)
配置文件示例：`cfg/res/datacopy/FT-DOCU.DocuAccOrdByPurShip.xml`

关键配置项：
```xml
<!-- 目标表=源表 -->
<Aname>copyfldinfo</Aname>
<Stdvalue><![CDATA[
    ft_docu_acd=ft_docu_psacd_cb:
        *,
        -$sys;
    ft_docu_acdg=ft_docu_psacd_cb:
        *,
        -$sys,
        fcy=fcying;
]]></Stdvalue>
```

其中涉及三个关键配置：
- `DocuAccOrdByPurShipCopyDataLoader`: 拷贝选中值校验、逻辑补充
- `DocuAccOrdByPurShipCopyDataListener`: 拷贝选中值数据加载
- 拷贝关系配置：`ft_docu_acd=ft_docu_psacd_cb:*,-$sys;`

#### 2. 核销配置(cor)

配置文件示例：`FT-DOCU.DocuAccOrdByPurShip.xml`

关键配置项：
```xml
<!-- 核销来源表 -->
<SourceTable>ft_psp_psst</SourceTable>
<CorRelaField>purshipssicode</CorRelaField>
<CorFields>fcy</CorFields>

<!-- 核销目标 -->
<Targets>
    <TargetTable>ft_docu_acdg</TargetTable>
    <TargetBean>FT-DOCU.DocuAccOrdCorTargetService</TargetBean>
    <Remark>目标</Remark>
</Targets>
```

### 拷贝核销金额处理流程

1. **金额计算**：
   - 计划执行金额(fcy) = 已执行金额(fcyed) + 待执行金额(fcying)
   - 初次拷贝时，fcyed为0，fcying等于fcy

2. **核销记录**：
   - 每次执行核销时，cb表更新fcyed和fcying值
   - 同时在ct表中插入一条记录，记录本次核销金额

3. **多次核销**：
   - 如果对同一来源进行多次核销，fcying会累加
   - 针对同一条记录修改金额时，只修改对应ct表记录
   - 只有再次拷贝新建才会产生新的ct记录

### 追加核销处理
可以通过特殊配置支持追加核销：
```xml
<Attrs>
    <!--累加字段信息-->
    <Aname>accfldinfo</Aname>
    <Stdvalue><![CDATA[ft_rpadj_offg:fcy=payfcy]]></Stdvalue>
</Attrs>
```

### 为什么使用cb表而不直接拷贝原表

1. cb表支持多次拷贝核销，记录核销状态
2. 跨服务场景下，不能直接访问上游表，需要通过cb表作为中间表
3. 避免每次全量拷贝，支持部分金额核销

这就是关于实现拷贝核销的步骤流程，尤其是涉及cb表和ct表的核心内容。



# 核销流程

## 生成CB 表

> 生成cb表的时机：如果是当前模块，比如资金模块里面，就直接配置存盘监听即可
>
> 注意一个是生成源source `CorSourceSaveListener`,一个是核销记录target `CorTargetSaveListener`
>
> ```
> <!--CorSourceSaveListener 配置当前单据下游核销-->
> <bean code="FT-CPS.110" sortidx="110" impl="#SN-PLAT.CorSourceSaveListener?[{corcodes:'FT-CPS.CpsUsePayAppCor'}]"/>
> <!--CorTargetSaveListener 配置当前单据上游核销-->
> <bean code="FT-CPS.120" sortidx="120" impl="#SN-PLAT.CorTargetSaveListener?[{corcodes:'FT-CPS.CpsUsePayAppCor'}]"/>
> ```
>
> 如果是跨服务，目前都是手动在上游模块直接写代码处理`snsoft.ft.tso.bas.service.impl.TStockGenDocuDeliveryCorServiceImpl#saveDocuDeliveryCorBalance`
>
> 

生效后会调用存盘监听

这个是生成cb表

```
snsoft.plat.bas.cor.service.impl.CorSourceSaveListener#afterSave
@Override
	public void afterSave(FunctionSaveListener.FunctionSaveShareEvent<V> se, SaveParams<V> params, SaveResults results, V record)
	{
      	//生效或者取消生效要生成/删除cb表
		int mode = getProcessMode(se, record);
		if (mode == -1 || mode == 1)
		{
			processCorBalance(se, record, mode);
		}
	}

查找对应核销cb配置
snsoft.plat.bas.cor.service.impl.CorSaveListener#loopCor
```

## 核销CB 表--通过CT表实现

```
<CorRelaField>salshipssicoder</CorRelaField>
<CorFields>fcy</CorFields>
<Flags>3</Flags>
<Remark><![CDATA[
       1、议付交单对象核销；
       2、负责人：池启苹；
]]></Remark>
<Targets>
    <TargetTable>ft_docu_dlydg</TargetTable>
    <TargetBean>FT-DOCU.DocuDeliveryCorTargetService</TargetBean>
    <Remark>目标</Remark>
</Targets>
```

```
TGTICODE        VARCHAR2(32) not null,  联合主键TGTICODE+TGTTBL
TGTTBL          VARCHAR2(30) not null,  联合主键TGTICODE+TGTTBL
SALSHIPSSICODER VARCHAR2(32),     核销内码
FCY             NUMBER(18, 6),

CB表：
SALSHIPSSICODER VARCHAR2(32) not null---主键，也就是核销内码
CORFLAG         NUMBER(5),
FCY             NUMBER(18, 6),
FCYED           NUMBER(18, 6),
FCYING          NUMBER(18, 6),
```

```
select *
from FT_DOCU_DLYDG_CT
where SALSHIPSSICODER = '683bf4330663187f680a8ce8';

select FCY, fcyed, fcying, SALSHIPSSICODER
from FT_DOCU_DLYDG_cb
where SALSHIPSSICODER = '683bf4330663187f680a8ce8';

select FCY
from FT_DOCU_DLYDG
where SALSHIPSSICODER = '683bf4330663187f680a8ce8'
```

比如议付交单拷贝新建的时候，会对CB表的fcy，fcyed，fcying 进行计算扣减，

* ct表是插入删除还是新增，根据TargetEntity里面3个字段，实际上应该也是根据record 主表的mode进行判断的？？

  ```
  public class TargetEntity{
  private Collection<String>	appendTgticodes;
  private Collection<String>	modifyTgticodes;
  private Collection<String>	deleteTgticodes;
  }
  
  snsoft.plat.bas.cor.service.impl.CorServiceImpl#processTarget
  
  //比较上层判断
  snsoft.plat.bas.busi.service.impl.AbstractFunctionService#executeSave
  ```

* 拷贝新建的时候，如果界面是不可改变fcying金额的，就默认全额核销剩余金额，fcy=fcyed+fcying；核销内码是`SALSHIPSSICODER`;插入的ct表有一个是TGTICODE 是目标表内码，也就是议付交单明细表，明细内码，还有一个是核销码，就是`SALSHIPSSICODER（CB表的）`;核销的金额就是本次拷贝过来的金额，也就是明细表金额g.fcy，会根据这个核销码去cb表找到记录去将金额扣减，同时插入ct表记录，就是下面sqlmapper的数据

*  修改金额的时候也是类似，但是这个时候ct表也是修改，cb表也是修改
* 删除的时候，ct表直接删除，cb表修改

```
<CorRelaField>salshipssicoder</CorRelaField>  根据这个字段核销，也就是说会用这个字段去找cb表的记录，去将查出来的金额进行扣减，同时插入ct表记录，就是下面sqlmapper的数据
```

```
<CorRelaField>salshipssicoder</CorRelaField>  根据这个字段核销

Map<String,Object> keyMap = new HashMap<>();
keyMap.put("tgticodes", tgticodes);---这个是明细表内码

select
g.dlydgicode tgticode,'ft_docu_dlydg' tgttbl,g.salshipssicoder,g.fcy as fcy
from
ft_docu_dlyd m join ft_docu_dlydg g on m.dlydicode=g.dlydicode
where
g.dlydgicode in (:tgticodes) and g.salshipssicoder is not null
```