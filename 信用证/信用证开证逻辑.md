# confirmLcAppInfo

我来帮您梳理 `confirmLcAppInfo` 方法的逻辑。首先让我查看这个方法的完整实现。

现在让我查看一些相关的常量和类定义，以便更好地理解这个方法：

基于我对代码的详细分析，现在我来为您梳理 `confirmLcAppInfo` 方法的逻辑：

## `confirmLcAppInfo` 方法逻辑梳理

这个方法是处理**信用证开证申请确认**的核心方法，支持多种确认类型的业务处理。

### 方法概述
- **方法名**: `confirmLcAppInfo`
- **参数**: `ConfirmLcAppInfoReq req` - 包含确认类型、操作人、源单据内码等信息
- **返回值**: `FTAcsResponse<String>` - 统一的响应格式
- **功能**: 根据不同的确认类型，处理信用证开证申请和预排额度申请的确认逻辑

### 详细逻辑流程

#### 1. 初始化和参数提取
````
List<LcApp> lcAppList = new ArrayList<>();
List<LcPreCreditApp> lcPreCreditAppList = new ArrayList<>();
//确认类型
String confirmtype = req.getLcAppConfirmParams().getConfirmtype();
````

- 创建用于日志记录的列表
- 提取确认类型，这是整个方法的核心分支判断依据

#### 2. 主要分支逻辑

方法根据确认类型分为两大分支：

##### 2.1 信用证相关处理分支
**支持的确认类型**: `"1,2,3,6"` (开证确认、改证确认、撤证确认、闭卷确认)

````
//信用证相关处理逻辑
if (Arrays.asList(LC_CONFIRM_APP_STATUS.split(",")).contains(confirmtype))
{
    LcApp lcApp = lcAppForOutService.queryByInnerCode(req.getLcAppConfirmParams().getSrcicode());
    String operator = FundAcsUtils.elinkUserCodeExchange(req.getLcAppConfirmParams().getOperator(), true);
````

**处理步骤**：
1. 根据内码查询信用证开证申请
2. 转换操作人编码
3. 根据确认类型执行不同的业务逻辑

###### 2.1.1 开证确认 (confirmtype = "1")
````
//开证确认
case "1" ->
{
    List<String> errOpenMsg = new ArrayList<>();
    if (StringUtils.isEmpty(req.getLcAppConfirmParams().getBankacccode()))
    {
        errOpenMsg.add(ResBundle.factory.getNewRes()
                .getInfo(FTPlatConstants.COLNOTNULL_COL, ResBundle.factory.getNewRes().getFixTitle(getResKeyValue("bankacccode", ConfirmLcAppInfo.class))));
    }
````

**校验规则**：

- 开证银行账户不能为空
- 信用证号不能为空
- 开证日期不能为空
- 状态不能已经是"70"（生效）
- 银行账户必须有对应的开证银行

**业务处理**：
- 更新状态为"70"（生效）
- 设置银行账户、信用证号、开证日期、开证银行
- 设置预排额度处理标志为true

###### 2.1.2 改证确认 (confirmtype = "2")
**校验规则**：
- 改证日期不能为空
- 状态不能已经是"70"（生效）

**业务处理**：
- 更新状态为"70"（生效）
- 设置改证日期

###### 2.1.3 撤证确认 (confirmtype = "3")
**校验规则**：
- 撤证日期不能为空
- 状态不能已经是"95"（撤证）

**业务处理**：
- 更新状态为"95"（撤证）
- 设置撤证日期

###### 2.1.4 闭卷确认 (confirmtype = "6")
**校验规则**：
- 状态不能已经是"75"（完结）

**业务处理**：
- 更新状态为"75"（完结）

##### 2.2 预排额度相关处理分支
**支持的确认类型**: `"4,5"` (预排额度确认、预排额度失效)

````
} else if (Arrays.asList(LC_CONFIRM_PRE_CREDIT_STATUS.split(",")).contains(confirmtype))
{
    //预排额度相关处理逻辑
    LcPreCreditApp lcPreCreditApp = lcPreCreditAppService.queryCascadeByInnerCode(req.getLcAppConfirmParams().getSrcicode());
````

###### 2.2.1 预排额度确认 (confirmtype = "4")
**校验规则**：
- 排证银行不能为空
- 状态不能已经是"70"（生效）

**业务处理**：
- 设置排证银行
- 更新状态为"70"（生效）

###### 2.2.2 预排额度失效 (confirmtype = "5")
**业务处理**：
- 更新状态为"90"（作废）

#### 3. 预排额度联动处理

````
//同时校验【信用证开证申请】关联的【预排额度申请】中"是否一次性开证"值，如为'是'，则更新【预排额度申请】单据状态为"75：完结"，否则无需处理；
if (preCreditFlag && StringUtils.isNotBlank(lcApp.getPcaicode()))
{
    LcPreCreditApp preCreditApp = lcPreCreditAppService.queryByInnerCode(lcApp.getPcaicode());
    if (ObjectUtils.isNotEmpty(preCreditApp) && LcOpenConstants.DT_YN_Y.equals(preCreditApp.getIsonelc()))
    {
        // 更新预排额度申请状态为完结
    }
}
````

**联动逻辑**：
- 当开证确认或改证确认时，检查关联的预排额度申请
- 如果预排额度申请的"是否一次性开证"为"是"，则将其状态更新为"75"（完结）

#### 4. 状态常量说明

| 状态码 | 含义 | 使用场景                                 |
| ------ | ---- | ---------------------------------------- |
| 70     | 生效 | 开证确认、改证确认、预排额度确认后的状态 |
| 75     | 完结 | 闭卷确认、一次性开证的预排额度完结状态   |
| 90     | 作废 | 预排额度失效后的状态                     |
| 95     | 撤证 | 撤证确认后的状态                         |

#### 5. 错误处理和日志记录

方法使用了统一的错误处理机制：
- 参数校验失败时返回错误信息
- 业务规则校验失败时返回相应的错误码
- 所有操作都会记录到日志中

### 方法特点

1. **多类型支持**: 一个方法处理6种不同的确认类型
2. **严格校验**: 每种确认类型都有对应的业务规则校验
3. **状态管理**: 精确控制单据的状态流转
4. **联动处理**: 信用证申请与预排额度申请之间的联动
5. **事务安全**: 使用函数式编程确保事务一致性
6. **日志记录**: 完整的操作日志记录机制

这个方法是信用证业务流程中的关键环节，确保了各种确认操作的正确性和数据一致性。

## 流程图



```
flowchart TD
    A[开始: confirmLcAppInfo] --> B[初始化变量和提取确认类型]
    B --> C{确认类型判断}
    
    C -->|"1,2,3,6"| D[信用证相关处理分支]
    C -->|"4,5"| E[预排额度相关处理分支]
    C -->|其他| F[返回错误: 无效确认类型]
    
    %% 信用证相关处理分支
    D --> D1[查询信用证开证申请]
    D1 --> D2{是否找到记录?}
    D2 -->|否| D3[返回错误: 未找到申请]
    D2 -->|是| D4{确认类型细分}
    
    %% 开证确认
    D4 -->|"1"开证确认| G[开证确认处理]
    G --> G1[校验: 银行账户不为空]
    G1 --> G2[校验: 信用证号不为空]
    G2 --> G3[校验: 开证日期不为空]
    G3 --> G4[校验: 状态不为70生效]
    G4 --> G5[校验: 银行账户有效性]
    G5 --> G6{校验通过?}
    G6 -->|否| G7[返回校验错误信息]
    G6 -->|是| G8[更新状态为70生效<br/>设置银行账户、信用证号、开证日期]
    G8 --> G9[设置预排额度处理标志=true]
    
    %% 改证确认
    D4 -->|"2"改证确认| H[改证确认处理]
    H --> H1[校验: 改证日期不为空]
    H1 --> H2[校验: 状态不为70生效]
    H2 --> H3{校验通过?}
    H3 -->|否| H4[返回校验错误信息]
    H3 -->|是| H5[更新状态为70生效<br/>设置改证日期]
    H5 --> H6[设置预排额度处理标志=true]
    
    %% 撤证确认
    D4 -->|"3"撤证确认| I[撤证确认处理]
    I --> I1[校验: 撤证日期不为空]
    I1 --> I2[校验: 状态不为95撤证]
    I2 --> I3{校验通过?}
    I3 -->|否| I4[返回校验错误信息]
    I3 -->|是| I5[更新状态为95撤证<br/>设置撤证日期]
    
    %% 闭卷确认
    D4 -->|"6"闭卷确认| J[闭卷确认处理]
    J --> J1[校验: 状态不为75完结]
    J1 --> J2{校验通过?}
    J2 -->|否| J3[返回校验错误信息]
    J2 -->|是| J4[更新状态为75完结]
    
    %% 预排额度相关处理分支
    E --> E1[查询预排额度申请]
    E1 --> E2{是否找到记录?}
    E2 -->|否| E3[返回错误: 未找到申请]
    E2 -->|是| E4{确认类型细分}
    
    %% 预排额度确认
    E4 -->|"4"预排额度确认| K[预排额度确认处理]
    K --> K1[校验: 排证银行不为空]
    K1 --> K2[校验: 状态不为70生效]
    K2 --> K3{校验通过?}
    K3 -->|否| K4[返回校验错误信息]
    K3 -->|是| K5[设置排证银行<br/>更新状态为70生效]
    
    %% 预排额度失效
    E4 -->|"5"预排额度失效| L[预排额度失效处理]
    L --> L1[更新状态为90作废]
    
    %% 汇聚到保存操作
    G9 --> M[保存信用证申请更新]
    H6 --> M
    I5 --> M
    J4 --> M
    K5 --> N[保存预排额度申请更新]
    L1 --> N
    
    %% 预排额度联动处理
    M --> O{是否需要预排额度联动?}
    O -->|是| P[查询关联的预排额度申请]
    O -->|否| S[返回成功响应]
    P --> Q{是否一次性开证?}
    Q -->|是| R[更新预排额度状态为75完结]
    Q -->|否| S
    R --> S
    N --> S
    
    %% 错误处理汇聚
    D3 --> T[记录日志并返回错误响应]
    F --> T
    G7 --> T
    H4 --> T
    I4 --> T
    J3 --> T
    E3 --> T
    K4 --> T
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe
    classDef process fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef error fill:#ffebee
    classDef success fill:#e8f5e8
    
    class A,S startEnd
    class B,D,E,G,H,I,J,K,L,M,N,P,R process
    class C,D2,D4,E2,E4,G6,H3,I3,J2,K3,O,Q decision
    class D3,E3,F,G7,H4,I4,J3,K4,T error
    class S success
```

![mermaid-202578 192202](E:\Apple\TyporaMD\Img\mermaid-202578 192202.png)

# checkRetractResLcAppInfo

## rbefstatus 状态字段用处

> 我看了下详设，那个rbefstatus状态是用在信用证预排额度 这里，信用证开证这里只有闭卷撤回的时候才会备份状态
>
> 嗯  信用证开证这里 看着是本来就没有在已送接口撤回和闭卷申请存这个状态，只有在预排额度是这样用的  因为接口共用同一个`checkRetractResLcAppInfo`，且状态都是91申请处理中，所以要区分一下，但是信用证开证这里不是同个接口，好像也不用这个状态来区分



## `checkRetractResLcAppInfo` 方法逻辑梳理

这个方法是处理**信用证申请撤回确认**的核心方法，用于处理资金系统对撤回申请的确认结果。

### 方法概述

- **方法名**: `checkRetractResLcAppInfo`
- **参数**: `RetractLcAppInfoReq req` - 包含撤回确认类型、操作人、撤回结果、源单据内码等信息
- **返回值**: `FTAcsResponse<String>` - 统一的响应格式
- **功能**: 根据资金系统的撤回确认结果，更新相应单据的状态

### 详细逻辑流程

#### 1. 初始化和参数提取



```
List<LcApp> lcAppList = new ArrayList<>();
List<LcPreCreditApp> lcPreCreditAppList = new ArrayList<>();
Function<RetractLcAppInfoReq,FTAcsResponse<String>> func = (_r) -> {
    try
    {
        if (Arrays.asList(LC_RETRACT_APP_STATUS.split(",")).contains(req.getLcAppRevocationParams().getConfirmtype()))
```

- 创建用于日志记录的列表
- 根据撤回确认类型进行分支处理

#### 2. 主要分支逻辑

方法根据撤回确认类型分为两大分支：

##### 2.1 信用证申请撤回分支

**支持的撤回确认类型**: `"1,2,3,4"` (开证申请撤回、改证申请撤回、撤证申请撤回、闭卷申请撤回)

```
LcApp lcApp = lcAppForOutService.queryCascadeByInnerCode(req.getLcAppRevocationParams().

getSrcicode());

if (ObjectUtils.isEmpty(lcApp))

{

  return new FTAcsResponse<>(FTAcsConstant.FT_CODE_ERROR, ResBundle.factory.getNewRes().getInfo

  (LcErrConstants.FT_LC_00000021, req.getLcAppRevocationParams().getSrcicode()), "");

}
```

**处理步骤**：

1. 根据内码查询信用证开证申请
2. 校验单据是否存在
3. 在微事务中处理撤回逻辑

###### 2.1.1 开证/改证/撤证申请撤回确认 (confirmtype = "1", "2", "3")

```
//开证申请撤回确认,改证申请撤回确认，撤证申请撤回确认
case "1", "2", "3" ->
{
    if (!LcOpenConstants.STATUS_91.equals(lcApp.getStatus()))
    {
        throw new PlatException(ResBundle.factory.getNewRes().getInfo(LcErrConstants.FT_LC_00000028));
    }
    LcAppForOutService.CheckRecallSuccessfulParams params = new LcAppForOutService.CheckRecallSuccessfulParams();
    params.setLcaicode(new String[] { req.getLcAppRevocationParams().getSrcicode() });
    params.setRetractresult(req.getLcAppRevocationParams().getRetractresult());
    lcAppForOutService.checkRecallSuccessful(params);
}
```

**业务逻辑**：

- **状态校验**: 必须是"91"（申请处理中）状态才能撤回
- **撤回处理**: 调用 `checkRecallSuccessful` 方法处理撤回结果
- 撤回结果
  - `"S"` (资金确认撤回): 撤回成功
  - `"E"` (资金驳回): 撤回失败

###### 2.1.2 闭卷申请撤回确认 (confirmtype = "4")

```
//闭卷申请撤回是否成功
case "4" ->
{
    LcAppForOutService.CheckClosedBookRevokeeParams closedBookRevokeeParams = new LcAppForOutService.CheckClosedBookRevokeeParams();
    closedBookRevokeeParams.setLcaicode(new String[] { req.getLcAppRevocationParams().getSrcicode() });
    closedBookRevokeeParams.setRetractresult(req.getLcAppRevocationParams().getRetractresult());
    lcAppForOutService.checkClosedBookRevoke(closedBookRevokeeParams);
}
```

**业务逻辑**：

- 调用 `checkClosedBookRevoke` 方法处理闭卷撤回结果

##### 2.2 预排额度申请撤回分支

**支持的撤回确认类型**: `"5"` (预排额度申请撤回确认)

```
} else if (Arrays.asList(LC_RETRACT_PRE_CREDIT_STATUS.split(",")).contains(req.getLcAppRevocationParams().getConfirmtype()))
{
    //提交前校验
    LcPreCreditApp lcPreCreditApp = lcPreCreditAppService.queryCascadeByInnerCode(req.getLcAppRevocationParams().getSrcicode());
    if (ObjectUtils.isEmpty(lcPreCreditApp))
    {
        return new FTAcsResponse<>(FTAcsConstant.FT_CODE_ERROR, ResBundle.factory.getNewRes().getInfo(LcErrConstants.FT_LC_00000022, req.getLcAppRevocationParams().getSrcicode()), "");
    }
```

**处理步骤**：

1. 根据内码查询预排额度申请
2. 校验单据是否存在
3. 校验状态必须是"91"（申请处理中）

###### 2.2.1 预排额度撤回结果处理

```
//预排额度数据处理
if (LcOpenConstants.RETRACT_S.equals(req.getLcAppRevocationParams().getRetractresult()))
{
    lcPreCreditApp.setSaveMode(DXConst.OP_UPDATE);
    lcPreCreditApp.setStatus(LcOpenConstants.STATUS_62.equals(lcPreCreditApp.getRbefstatus()) ? LcOpenConstants.STATUS_90 : LcOpenConstants.STATUS_75);
    lcPreCreditApp.setModifydate(DateUtils.getServerDate());
    lcPreCreditApp.setModifier(operator);
    lcPreCreditApp.addStoredColumns(new String[] { "modifier", "modifydate", "status" });
    SaveParams<LcPreCreditApp> saveParams = new SaveParams<>(lcPreCreditApp);
    lcPreCreditAppService.saveLcPreCreditApp(saveParams);
} else
{
    lcPreCreditApp.setSaveMode(DXConst.OP_UPDATE);
    lcPreCreditApp.setStatus(lcPreCreditApp.getRbefstatus());
    lcPreCreditApp.setModifydate(DateUtils.getServerDate());
    lcPreCreditApp.setModifier(operator);
    lcPreCreditApp.addStoredColumns(new String[] { "modifier", "modifydate", "status" });
    SaveParams<LcPreCreditApp> saveParams = new SaveParams<>(lcPreCreditApp);
    lcPreCreditAppService.saveLcPreCreditApp(saveParams);
}
```

**状态更新逻辑**：

| 撤回结果     | 撤回前状态      | 撤回后状态       | 说明                 |
| ------------ | --------------- | ---------------- | -------------------- |
| S (确认撤回) | 62 (接口已接收) | 90 (作废)        | 撤回成功，单据作废   |
| S (确认撤回) | 其他状态        | 75 (完结)        | 撤回成功，单据完结   |
| E (资金驳回) | 任意状态        | 恢复到撤回前状态 | 撤回失败，恢复原状态 |

#### 3. 日志清理处理

```
//撤销时要删除日志
platClientService.deleteSheetExTask(LcApp.SheetCode, req.getLcAppRevocationParams().getSrcicode());
```



- 撤回成功时，删除相关的任务执行日志

#### 4. 撤回确认类型说明

| 确认类型 | 含义                 | 处理对象       |
| -------- | -------------------- | -------------- |
| 1        | 开证申请撤回确认     | 信用证开证申请 |
| 2        | 改证申请撤回确认     | 信用证开证申请 |
| 3        | 撤证申请撤回确认     | 信用证开证申请 |
| 4        | 闭卷申请撤回确认     | 信用证开证申请 |
| 5        | 预排额度申请撤回确认 | 预排额度申请   |

#### 5. 撤回结果说明

| 撤回结果 | 含义         | 处理逻辑               |
| -------- | ------------ | ---------------------- |
| S        | 资金确认撤回 | 撤回成功，更新单据状态 |
| E        | 资金驳回     | 撤回失败，恢复原状态   |

### 方法特点

1. **双分支处理**: 分别处理信用证申请和预排额度申请的撤回
2. **状态严格校验**: 只有"91"（申请处理中）状态的单据才能撤回
3. **事务安全**: 使用微事务确保数据一致性
4. **状态恢复机制**: 撤回失败时能恢复到原始状态
5. **日志管理**: 撤回成功时清理相关任务日志
6. **错误处理**: 完善的错误信息提示

这个方法是信用证撤回流程的关键环节，确保了撤回操作的正确性和数据一致性，同时提供了完善的状态管理和错误处理机制。

## 流程图

```
flowchart TD
    A[开始: checkRetractResLcAppInfo] --> B[初始化变量和提取撤回确认类型]
    B --> C{撤回确认类型判断}
    
    C -->|"1,2,3,4"| D[信用证申请撤回分支]
    C -->|"5"| E[预排额度申请撤回分支]
    C -->|其他| F[返回错误: 无效撤回确认类型]
    
    %% 信用证申请撤回分支
    D --> D1[查询信用证开证申请]
    D1 --> D2{是否找到记录?}
    D2 -->|否| D3[返回错误: 未找到申请]
    D2 -->|是| D4[开启微事务]
    D4 --> D5{撤回确认类型细分}
    
    %% 开证/改证/撤证申请撤回
    D5 -->|"1,2,3"开证/改证/撤证| G[开证/改证/撤证撤回处理]
    G --> G1[校验: 状态必须为91申请处理中]
    G1 --> G2{状态校验通过?}
    G2 -->|否| G3[抛出异常: 状态不为申请处理中]
    G2 -->|是| G4[调用checkRecallSuccessful方法]
    G4 --> G5[根据撤回结果更新状态]
    
    %% 闭卷申请撤回
    D5 -->|"4"闭卷申请| H[闭卷申请撤回处理]
    H --> H1[调用checkClosedBookRevoke方法]
    H1 --> H2[根据撤回结果更新状态]
    
    %% 预排额度申请撤回分支
    E --> E1[查询预排额度申请]
    E1 --> E2{是否找到记录?}
    E2 -->|否| E3[返回错误: 未找到申请]
    E2 -->|是| E4[校验: 状态必须为91申请处理中]
    E4 --> E5{状态校验通过?}
    E5 -->|否| E6[返回错误: 状态不为申请处理中]
    E5 -->|是| E7[开启微事务]
    E7 --> E8{撤回结果判断}
    
    %% 预排额度撤回结果处理
    E8 -->|S 资金确认撤回| I[撤回成功处理]
    E8 -->|E 资金驳回| J[撤回失败处理]
    
    I --> I1{撤回前状态判断}
    I1 -->|62 接口已接收| I2[更新状态为90作废]
    I1 -->|其他状态| I3[更新状态为75完结]
    
    J --> J1[恢复到撤回前状态rbefstatus]
    
    %% 汇聚到保存和日志清理
    G5 --> K[删除任务执行日志]
    H2 --> K
    I2 --> L[保存预排额度申请更新]
    I3 --> L
    J1 --> L
    L --> M[删除任务执行日志]
    
    K --> N[返回成功响应]
    M --> N
    
    %% 错误处理汇聚
    D3 --> O[记录日志并返回错误响应]
    E3 --> O
    E6 --> O
    F --> O
    G3 --> O
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe
    classDef process fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef error fill:#ffebee
    classDef success fill:#e8f5e8
    classDef transaction fill:#e8eaf6
    
    class A,N startEnd
    class B,D,E,G,H,I,J,K,L,M process
    class C,D2,D5,E2,E5,E8,G2,I1 decision
    class D3,E3,E6,F,G3,O error
    class N success
    class D4,E7 transaction
```

![mermaid-202578 194610](E:\Apple\TyporaMD\Img\mermaid-202578 194610.png)