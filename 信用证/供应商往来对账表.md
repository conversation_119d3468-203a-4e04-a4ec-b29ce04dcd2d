

# 窗口函数分析

```
sum() over(partition by … order by …)：求分组后的总和。
first_value() over(partition by … order by …)：求分组后的第一个。
last_value() over(partition by … order by …)：求分组后的最后一个。
count() over(partition by … order by …)：求分组后的总数。
max() over(partition by … order by …)：求分组后的最大值。
min() over(partition by … order by …)：求分组后的最小值。
avg() over(partition by … order by …)：求分组后的平均值。
lag() over(partition by … order by …)：取出前n行数据。
lead() over(partition by … order by …)：取出后n行数据。

```



```
sum(case when rn = 0 then fcying else nvl(payfcy, 0 - meetfcy) end) over (partition by corpbcode, bcode, ccode, fcode order by rn asc) fcying
这个窗口函数什么意思
```

这个窗口函数的作用是**对特定分组内的数据进行累加计算**，并根据`rn`的值动态调整参与计算的数值。具体解析如下：

------

### 1. **核心结构分析**

函数的主体结构是：
 `SUM(... CASE WHEN ...) OVER (PARTITION BY ... ORDER BY ...)`
 其中包含三个关键部分：

- **条件判断逻辑**（`CASE WHEN`）：动态选择参与计算的字段。
- **分组逻辑**（`PARTITION BY`）：定义数据分组的依据。
- **排序逻辑**（`ORDER BY`）：控制累加的顺序。

------

### 2. **条件判断逻辑的作用**

```
CASE WHEN rn = 0 THEN fcying ELSE NVL(payfcy, 0 - meetfcy) END
```

- **当 `rn = 0` 时**：直接取 `fcying` 字段的值，通常用于初始化累加起点（例如初始金额）。

- 

  当 `rn ≠ 0` 时

  ：

  - 若 `payfcy` 非空，则使用 `payfcy` 的值；
  - 若 `payfcy` 为空，则用 `0 - meetfcy` 替代（可能是某种补偿或逆向计算）。

------

### 3. **窗口函数的累加逻辑**

```
SUM(...) OVER (PARTITION BY corpbcode, bcode, ccode, fcode ORDER BY rn ASC)
```

- **分组依据（PARTITION BY）**：按 `corpbcode, bcode, ccode, fcode` 分组，每组独立计算。
- **排序（ORDER BY rn ASC）**：按 `rn` 升序排列，**默认窗口范围从分区第一行到当前行**。
   这意味着：每一行的累加结果 = 分区内从第一行到当前行的所有符合条件的值之和。

------

### 4. **实际应用场景示例**

假设数据表示某企业的资金流水，按业务单元（`corpbcode`等）分组：

- `rn=0` 的行代表初始资金（`fcying`）；
- `rn>0` 的行代表后续收支（`payfcy`为正收入，`meetfcy`可能为需扣除的费用）。
   通过此窗口函数，可以实时计算每个业务单元的资金余额：初始金额 + 后续收支的累计值。

------

### 5. **对比普通聚合函数的区别**

普通 `SUM()` 会将多行聚合成一行，而窗口函数保留所有原始行，**仅在每行附加累加结果**。例如：

- **普通聚合**：`SUM(payfcy)` → 返回分组后的总金额（单行）。
- **窗口函数**：`SUM(...) OVER(...)` → 每行显示截止到当前行的累计金额（多行）。

------

### 总结

该窗口函数的作用是：**按业务单元分组，按顺序累加初始资金与动态调整后的收支值**，常用于财务、库存等需要逐行累计的场景。关键点在于条件判断逻辑和窗口范围的定义。





### SQL窗口函数示例解析

以下通过具体数据案例演示该窗口函数的计算逻辑，结合字段含义和分组规则逐步说明累加过程。

------

#### **原始数据表结构**

假设存在表 `financial_data`，包含以下字段和示例数据：

| corpbcode | bcode | ccode | fcode | rn   | fcying | payfcy | meetfcy |
| --------- | ----- | ----- | ----- | ---- | ------ | ------ | ------- |
| A         | B1    | C1    | D1    | 0    | 10000  | NULL   | NULL    |
| A         | B1    | C1    | D1    | 1    | NULL   | 5000   | 2000    |
| A         | B1    | C1    | D1    | 2    | NULL   | NULL   | 3000    |
| X         | Y1    | Z1    | W1    | 0    | 8000   | NULL   | NULL    |
| X         | Y1    | Z1    | W1    | 1    | NULL   | NULL   | 1500    |

------

#### **窗口函数逻辑拆解**

1. **分组与排序**

   - `PARTITION BY corpbcode, bcode, ccode, fcode`：按这四个字段分组，每组独立计算。
   - `ORDER BY rn ASC`：组内按 `rn` 升序累加，窗口范围为从第一行到当前行。

2. **动态取值规则**

   - **当 `rn = 0` 时**：直接取 `fcying` 的值（初始金额）。

   - 

     当 `rn > 0` 时

     ：

     - 若 `payfcy` 非空，则用 `payfcy`；
     - 若 `payfcy` 为空，则用 `0 - meetfcy`（如扣减费用）。

------

#### **逐行计算过程**

以分组 `(A, B1, C1, D1)` 为例：

| rn   | 取值逻辑                          | 当前行值 | 累加值（fcying） |
| ---- | --------------------------------- | -------- | ---------------- |
| 0    | `CASE WHEN rn=0 THEN fcying`      | 10000    | 10000            |
| 1    | `NVL(payfcy, 0-meetfcy) = 5000`   | 5000     | 10000+5000=15000 |
| 2    | `NVL(payfcy, 0-meetfcy) = 0-3000` | -3000    | 15000-3000=12000 |

**结果输出：**

| corpbcode | bcode | ccode | fcode | rn   | fcying（累加值） |
| --------- | ----- | ----- | ----- | ---- | ---------------- |
| A         | B1    | C1    | D1    | 0    | 10000            |
| A         | B1    | C1    | D1    | 1    | 15000            |
| A         | B1    | C1    | D1    | 2    | 12000            |

同理，分组 `(X, Y1, Z1, W1)` 的计算：

- `rn=0` 时取 `fcying=8000`
- `rn=1` 时 `payfcy` 为空，取 `0-1500=-1500`，累加值为 `8000-1500=6500`

------

#### **应用场景说明**

此函数常用于财务或库存系统中的**余额累加**：

- **初始值**（`rn=0`）：如账户初始金额或库存初始量。
- **后续行**：记录资金收支（`payfcy`）或库存调整（`0-meetfcy`），通过窗口函数实时计算当前余额。

------

#### **关键点总结**

1. **条件分支**：`CASE WHEN` 动态选择参与计算的字段，`NVL` 处理空值。
2. **窗口范围**：默认从分组第一行到当前行，逐行累加。
3. **分组隔离**：不同 `corpbcode` 等分组独立计算，互不影响。

通过此案例可清晰理解窗口函数在分组内动态累加的逻辑。

# Gemini

好的，我们来详细拆解分析一下这段复杂的SQL代码。这是一段典型的用于生成ERP系统中“供应商往来对账报表”的查询。

**总体目标：**

该查询的核心目的是生成一份供应商的往来对账明细。它通过以下步骤实现：

1.  计算截至某个特定起始日期（`2024-03-01`）的每个供应商和币种组合的**期初余额**。
2.  列出在指定报告期内（`2024-03-01` 至 `2025-05-21`）发生的所有**交易**（主要是发票和付款）。
3.  为每个供应商/币种计算一个**滚动余额**（`fcying`），从期初余额开始，并随着每笔交易进行更新。

让我们逐层解析其结构：

```sql
SELECT -- 最外层 SELECT：最终展示所有计算好的列
       *
FROM   ( -- 子查询1 (派生表)：合并期初余额和交易记录，然后计算滚动余额
          SELECT srcsheettype,
                 corpbcode,
                 bcode,
                 ccode,
                 rptype,
                 purprjcode,
                 purprjicode,
                 purordcode,
                 purordicode,
                 puroutordcode,
                 invicoder,
                 invcoder,
                 Trunc(tcaptime) AS tcaptime, -- 交易日期 (移除了时间部分)
                 fcode, -- 币种代码
                 meetfcy, -- 很可能是“应付金额”或“发票金额”
                 payfcy, -- 很可能是“已付金额”
                 -- 核心计算：滚动总额/余额
                 Sum(CASE
                       WHEN rn = 0 THEN fcying -- 对于期初余额行 (rn=0)，取其预先计算好的 fcying
                       ELSE Nvl(payfcy, 0 - meetfcy) -- 对于交易行，计算变动：付款金额 - 发票金额
                                                     -- 如果 payfcy 不为空，则使用 payfcy。
                                                     -- 如果 payfcy 为空 (通常是发票)，则使用 (0 - meetfcy)，即 -发票金额。
                     END)
                   OVER (
                     PARTITION BY corpbcode, bcode, ccode, fcode -- 为每个供应商/币种计算滚动总额
                     ORDER BY rn ASC -- 按 rn 排序 (期初余额在前，然后是交易)
                   )             AS fcying, -- 这就是滚动余额
                 srcicode,
                 srccode,
                 (CASE
                    WHEN srcsheettype = '50' THEN NULL -- 如果是期初余额行，则将 srcsheetcode 置空
                    ELSE srcsheetcode
                  END)           AS srcsheetcode,
                 vprepare,
                 rn -- 行号 (期初余额为0，交易大于0)
          FROM   ( -- 子查询2：使用 UNION ALL 合并期初余额记录和交易记录
                    -- UNION ALL 的 A 部分：交易记录
                    SELECT srcsheettype,
                           corpbcode, -- 可能是公司/分支/客户代码
                           bcode,
                           ccode,
                           rptype, -- 报告类型
                           purprjcode, -- 采购项目代码
                           purprjicode,
                           purordcode, -- 采购订单代码
                           purordicode,
                           NULL AS puroutordcode, -- 硬编码为 NULL
                           invicoder, -- 发票代码
                           invcoder,
                           Trunc(tcaptime) AS tcaptime,
                           fcode, -- 币种
                           meetfcy, -- 此分组下的聚合“发票”金额
                           payfcy, -- 此分组下的聚合“付款”金额
                           fcying, -- 对于这些交易行，初始为 NULL
                           srcicode,
                           srccode,
                           srcsheetcode,
                           vprepare,
                           -- 为供应商/币种分组内的每笔交易分配一个行号，按各种代码和日期排序
                           Row_number()
                             OVER (
                               PARTITION BY corpbcode, bcode, ccode, fcode
                               ORDER BY corpbcode, bcode, ccode, fcode, Trunc(tcaptime), purprjcode, purprjicode, purordcode, purordicode, invicoder, invcoder, srccode, srcsheetcode) AS rn
                    FROM   ( -- 子查询3：从基础表中聚合报告期内的原始交易数据
                              SELECT Max(srcsheettype) AS srcsheettype,
                                     corpbcode,
                                     bcode,
                                     ccode,
                                     Max(rptype) AS rptype,
                                     purprjcode,
                                     purprjicode,
                                     purordcode,
                                     purordicode,
                                     NULL AS puroutordcode,
                                     invicoder,
                                     invcoder,
                                     Trunc(tcaptime) AS tcaptime,
                                     fcode,
                                     -- meetfcy: 当 realvdc = 1 时 'fcy' 的总和 (可能是应收/发票)
                                     Sum((CASE WHEN realvdc = 1 THEN fcy ELSE NULL END)) AS meetfcy,
                                     -- payfcy: 当 realvdc = -1 时 'fcy' 的总和 (可能是应付/付款)
                                     Sum((CASE WHEN realvdc = -1 THEN fcy ELSE NULL END)) AS payfcy,
                                     NULL AS fcying, -- 占位符
                                     (CASE WHEN Max(realvdc) = 1 THEN invicoder ELSE Max(srcicode) END) AS srcicode,
                                     srccode,
                                     srcsheetcode,
                                     Max(vprepare) AS vprepare
                              FROM   ft_fund.ft_lrp_lrp -- 基础交易表
                              WHERE  cuicode = 'C000000001' -- 特定客户/内部公司
                                     AND Trunc(tcaptime) >= To_date('2024-03-01', 'yyyy-mm-dd') -- 报告开始日期
                                     AND Trunc(tcaptime) <= To_date('2025-05-21', 'yyyy-mm-dd') -- 报告结束日期
                                     AND batype = 'A' -- 业务账户类型?
                                     AND ( realvdc = 1 OR sdate IS NOT NULL ) -- realvdc=1 可能表示已实现的票据，sdate 用于计划性条目
                                     AND realdc = -1 -- realdc 可能表示借贷方向。-1 通常意味着应付款/负债。
                                     AND lccode IS NULL -- 排除某些分类账代码
                                     AND ( ( realvdc = 1 ) OR ( realvdc = -1 ) ) -- 交易要么是发票要么是付款
                                     AND rptype NOT IN ( 'S1009.001', 'S1210.001' ) -- 排除特定的报告类型
                              GROUP  BY corpbcode, bcode, ccode, fcode, Trunc(tcaptime), purprjcode, purprjicode, purordcode, purordicode, invicoder, invcoder, srccode, srcsheetcode
                              HAVING ( Sum((CASE WHEN realvdc = 1 THEN fcy ELSE NULL END)) != 0 -- 必须有发票金额
                                        OR Sum((CASE WHEN realvdc = -1 THEN fcy ELSE NULL END)) != 0 ) -- 或者有付款金额
                              ORDER  BY corpbcode, bcode, ccode, fcode, Trunc(tcaptime), purprjcode, purprjicode, purordcode, purordicode, invicoder, invcoder, srccode, srcsheetcode ASC
                           )
                    UNION ALL -- 合并交易数据和期初余额数据
                    -- UNION ALL 的 B 部分：期初余额记录
                    (SELECT '50' AS srcsheettype, -- 期初余额的特殊类型
                            corpbcode,
                            bcode,
                            ccode,
                            NULL AS rptype,
                            NULL AS purprjcode,
                            NULL AS purprjicode,
                            NULL AS purordcode,
                            NULL AS purordicode,
                            NULL AS puroutordcode,
                            NULL AS invicoder,
                            NULL AS invcoder,
                            NULL AS tcaptime, -- 期初余额没有特定的交易时间
                            fcode,
                            NULL AS meetfcy, -- 此余额行本身没有发票金额
                            NULL AS payfcy, -- 此余额行本身没有付款金额
                            Nvl(fcying, 0) AS fcying, -- 这是计算出来的期初余额金额
                            NULL AS srcicode,
                            NULL AS srccode,
                            NULL AS srcsheetcode,
                            NULL AS vprepare,
                            0 AS rn -- 关键：为期初余额行设置 rn=0，使其首先排序
                     FROM   ( -- 子查询4：计算期初余额金额
                               WITH ft_lrp_lrp_fcy -- CTE：计算报告开始日期之前的总余额
                                    AS (SELECT corpbcode,
                                               bcode,
                                               ccode,
                                               fcode,
                                               -- 期初余额：(付款 - 发票) 或 (发票 - 付款) 的总和，取决于符号约定
                                               -- realvdc=1 (发票) -> 结果为 -fcy
                                               -- realvdc=-1 (付款) -> 结果为 +fcy
                                               -- 因此，负数结果意味着“公司欠款”或“负债”
                                               Sum(realvdc * -1 * fcy) AS fcying
                                        FROM   ft_fund.ft_lrp_lrp
                                        WHERE  cuicode = 'C000000001'
                                               AND Trunc(tcaptime) < To_date('2024-03-01', 'yyyy-mm-dd') -- 报告开始日期之前的交易
                                               AND batype = 'A'
                                               AND ( realvdc = 1 OR sdate IS NOT NULL )
                                               AND realdc = -1
                                               AND lccode IS NULL
                                               AND ( ( realvdc = 1 ) OR ( realvdc = -1 ) )
                                               AND rptype NOT IN ( 'S1009.001', 'S1210.001' )
                                        GROUP  BY corpbcode, bcode, ccode, fcode)
                               SELECT e.corpbcode, -- 获取所有相关的供应商/币种组合
                                      e.bcode,
                                      e.ccode,
                                      e.fcode,
                                      d.fcying -- 来自 CTE 的计算出的期初余额
                               FROM   ( -- 子查询5：获取在报告期内有活动或在报告期前有余额的供应商/币种的唯一组合。
                                         SELECT DISTINCT corpbcode, bcode, ccode, fcode
                                         FROM   ft_fund.ft_lrp_lrp
                                         WHERE  cuicode = 'C000000001' -- 条件与主交易查询匹配
                                                AND Trunc(tcaptime) >= To_date('2024-03-01', 'yyyy-mm-dd')
                                                AND Trunc(tcaptime) <= To_date('2025-05-21', 'yyyy-mm-dd')
                                                AND batype = 'A'
                                                AND ( realvdc = 1 OR sdate IS NOT NULL )
                                                AND realdc = -1
                                                AND lccode IS NULL
                                                AND ( ( realvdc = 1 ) OR ( realvdc = -1 ) )
                                                AND rptype NOT IN ( 'S1009.001', 'S1210.001' )
                                         GROUP  BY corpbcode, bcode, ccode, fcode, Trunc(tcaptime), purprjcode, purprjicode, purordcode, purordicode, invicoder, invcoder, srccode, srcsheetcode
                                         HAVING ( Sum((CASE WHEN realvdc = 1 THEN fcy ELSE NULL END)) != 0
                                                   OR Sum((CASE WHEN realvdc = -1 THEN fcy ELSE NULL END)) != 0 )
                                         UNION -- 确保获取所有组合，即使只有期初余额存在
                                         SELECT DISTINCT corpbcode, bcode, ccode, fcode
                                         FROM   ft_fund.ft_lrp_lrp b
                                         WHERE  cuicode = 'C000000001' -- 条件与期初余额 CTE 匹配
                                                AND Trunc(tcaptime) < To_date('2024-03-01', 'yyyy-mm-dd')
                                                AND batype = 'A'
                                                AND ( realvdc = 1 OR sdate IS NOT NULL )
                                                AND realdc = -1
                                                AND lccode IS NULL
                                                AND ( ( realvdc = 1 ) OR ( realvdc = -1 ) )
                                                AND rptype NOT IN ( 'S1009.001', 'S1210.001' )
                                         GROUP  BY corpbcode, bcode, ccode, fcode
                                         HAVING Sum(realvdc * -1 * fcy) <> 0 -- 在报告开始前有非零余额
                                       ) e
                                      LEFT JOIN ft_lrp_lrp_fcy d -- 左连接以获取实际的期初余额金额
                                        ON Coalesce(e.corpbcode, 'null') = Coalesce(d.corpbcode, 'null')
                                           AND Coalesce(e.bcode, 'null') = Coalesce(d.bcode, 'null')
                                           AND Coalesce(e.ccode, 'null') = Coalesce(d.ccode, 'null')
                                           AND Coalesce(e.fcode, 'null') = Coalesce(d.fcode, 'null')
                            )
                   )
          )
-- 没有最终的 WHERE 子句或 ORDER BY，因此结果按子查询1的计算方式呈现。
```

-----

### 关键部分和逻辑解析：

1.  **最内层的交易聚合 (子查询3):**

      * 从 `ft_fund.ft_lrp_lrp` 表中筛选出指定日期范围（`2024-03-01` 至 `2025-05-21`）内特定 `cuicode` 的交易。
      * `realdc = -1` 很可能表示应付款（公司欠款）。
      * `realvdc = 1` 被视为“发票”或应付款的增加（对应 `meetfcy`）。
      * `realvdc = -1` 被视为“付款”或应付款的减少（对应 `payfcy`）。
      * 它按多个字段对交易进行分组，有效地为这些标识符的每个唯一组合（例如，每天、每张发票、每个订单）创建一条摘要行。
      * `HAVING` 子句确保只包括那些实际有发票金额或付款金额的记录。

2.  **期初余额计算 (UNION ALL 的 B 部分, 子查询4 和 CTE `ft_lrp_lrp_fcy`):**

      * CTE `ft_lrp_lrp_fcy` 计算 **`2024-03-01` 之前** 的净余额。
          * `Sum(realvdc * -1 * fcy)` 的逻辑意味着：
              * 如果 `realvdc = 1` (发票): `1 * -1 * fcy = -fcy`。如果负余额表示欠款，这是合理的。
              * 如果 `realvdc = -1` (付款): `-1 * -1 * fcy = +fcy`。这会减少欠款金额。
          * 因此，来自此 CTE 的 `fcying` 是期初余额。负值表示公司欠供应商的钱。
      * 子查询5 (`SELECT ... FROM (SELECT DISTINCT ... UNION SELECT DISTINCT ...) e`) 用于获取所有在报告期内有交易或之前有非零余额的 `corpbcode, bcode, ccode, fcode` 组合的完整列表。这确保了为所有相关实体生成期初余额行。
      * 然后，这些不同的组合与 CTE 的结果进行 `LEFT JOIN`，以附加计算出的期初余额。
      * 这些行被赋予 `rn = 0` 和 `srcsheettype = '50'`，以将它们标识为期初余额记录。

3.  **UNION ALL (子查询2):**

      * 它合并了：
          * 来自报告期的聚合交易记录（每条记录通过 `ROW_NUMBER()` 获得 `rn > 0`）。
          * 期初余额记录（每条记录有 `rn = 0`）。

4.  **滚动余额计算 (子查询1):**

      * 这是对账功能的魔法发生的地方。
      * `Sum(...) OVER (PARTITION BY corpbcode, bcode, ccode, fcode ORDER BY rn ASC) AS fcying`:
          * 它为每个供应商/币种（`PARTITION BY`）处理记录。
          * 它按 `rn` 对它们进行排序（`rn=0` 在前，然后是后续交易的 `rn=1`, `rn=2` 等）。
          * `CASE WHEN rn = 0 THEN fcying`: 对于第一行（期初余额），它取其预先计算的 `fcying`。
          * `ELSE Nvl(payfcy, 0 - meetfcy)`: 对于交易行：
              * 如果是付款（`payfcy` 不为空），则使用 `payfcy`（一个正值）。
              * 如果是发票（`payfcy` 为空，`meetfcy` 不为空），则使用 `0 - meetfcy`（一个负值）。
          * 然后 `Sum()` 累积地添加这些值。
              * **示例**：
                  * 期初余额 (`rn=0`): `fcying = -1000` (公司欠款 1000)
                  * 发票 (`rn=1`): `meetfcy = 500`, `payfcy = NULL`。变动是 `0 - 500 = -500`。滚动总额 = `-1000 + (-500) = -1500`。
                  * 付款 (`rn=2`): `meetfcy = NULL`, `payfcy = 300`。变动是 `300`。滚动总额 = `-1500 + 300 = -1200`。
      * 这正确地计算了应付给供应商或供应商应付给公司的滚动余额。

5.  **最外层 SELECT:**

      * 简单地从子查询1中选择所有计算出的列。

-----

**总结来说：**

这段SQL查询是生成详细供应商对账单的一种稳健方法。它首先为每个供应商和币种建立一个期初余额。然后，它列出在指定期间内的所有后续发票和付款。关键的是，它在每笔交易后计算滚动余额，允许用户查看欠款金额如何随时间演变。`realvdc`（`1` 代表类似发票的条目，`-1` 代表类似付款的条目）与 `realdc = -1` 筛选器（可能用于应付账款）的结合使用，是借贷逻辑的关键。`rn`（行号）对于在窗口函数中对期初余额和后续交易进行排序和区分至关重要。





# DS

这个SQL查询是用于生成供应商往来对账报表的复杂逻辑，涉及多个层级的分组、聚合、窗口函数和联合查询。以下是逐层拆解和分析：

---

### **1. 整体结构**
```sql
SELECT * 
FROM (
  -- 内层子查询（含UNION ALL和窗口函数）
  SELECT 
    srcsheettype, corpbcode, ..., 
    SUM(CASE ...) OVER (...) AS fcying 
  FROM (
    -- 核心数据查询（交易明细 + 期初余额）
    (SELECT ... FROM ft_lrp_lrp GROUP BY ...)
    UNION ALL
    (SELECT '50' AS srcsheettype, ..., 0 AS rn FROM ...)
  ) 
)
```
- **外层**：通过窗口函数计算动态余额（`fcying`）。
- **内层**：通过`UNION ALL`合并两个数据集：
  1. **交易明细**：指定时间范围内的应付/付款交易。
  2. **期初余额**：时间范围前的初始余额。

---

### **2. 交易明细部分**
```sql
SELECT 
  MAX(srcsheettype) AS srcsheettype,
  corpbcode, bcode, ccode, 
  purprjcode, purprjicode, ..., 
  SUM(CASE WHEN realvdc=1 THEN fcy END) AS meetfcy,  -- 应付增加
  SUM(CASE WHEN realvdc=-1 THEN fcy END) AS payfcy    -- 应付减少
FROM ft_fund.ft_lrp_lrp 
WHERE 
  cuicode = 'C000000001' 
  AND tcaptime BETWEEN '2024-03-01' AND '2025-05-21'
  AND batype = 'A' 
  AND realdc = -1 
  AND rptype NOT IN ('S1009.001', 'S1210.001')
GROUP BY corpbcode, bcode, ccode, fcode, tcaptime, ... 
HAVING SUM(meetfcy) !=0 OR SUM(payfcy)!=0
```
- **目的**：提取指定时间范围内的应付交易记录。
- **关键字段**：
  - `realvdc=1`：应付增加（如发票确认）。
  - `realvdc=-1`：应付减少（如付款）。
  - `meetfcy`和`payfcy`分别统计应付和付款金额。
- **过滤条件**：
  - `cuicode`：特定客户/供应商。
  - `batype='A'`：交易类型为“活动”状态。
  - `realdc=-1`：贷方交易（应付为贷方科目）。

---

### **3. 期初余额部分**
```sql
SELECT 
  '50' AS srcsheettype, 
  corpbcode, bcode, ccode, fcode, 
  SUM(realvdc * -1 * fcy) AS fcying  -- 期初余额
FROM ft_lrp_lrp 
WHERE 
  tcaptime < '2024-03-01' 
  -- 其他条件与主查询一致
GROUP BY corpbcode, bcode, ccode, fcode
```
- **目的**：计算时间范围开始前（`tcaptime < '2024-03-01'`）的应付余额。
- **逻辑**：`realvdc * -1 * fcy`将应付增加和减少转换为余额方向。
  - `realvdc=1`（应付增加） → `-1 * fcy`（贷方余额）。
  - `realvdc=-1`（应付减少） → `1 * fcy`（借方冲减）。

---

### **4. 联合查询与窗口函数**
```sql
UNION ALL
(SELECT ..., 0 AS rn FROM ...)  -- 期初余额作为rn=0的行

-- 外层窗口函数计算余额
SUM(CASE WHEN rn=0 THEN fcying ELSE NVL(payfcy, 0 - meetfcy) END) OVER (
  PARTITION BY corpbcode, bcode, ccode, fcode 
  ORDER BY rn ASC
) AS fcying
```
- **联合结果**：将期初余额（`rn=0`）与交易明细（`rn>=1`）合并。
- **余额计算逻辑**：
  - 对每个分组（`corpbcode, bcode, ccode, fcode`）按`rn`排序。
  - 初始余额（`rn=0`）作为起点。
  - 后续每行累加：`payfcy`（付款）或 `0 - meetfcy`（应付增加的反向，可能为负值）。

---

### **5. 关键业务逻辑**
- **余额方向**：
  - 应付增加（`meetfcy`）记为贷方正数。
  - 应付减少（`payfcy`）记为借方负数。
  - 最终余额 `fcying` 反映贷方剩余应付。
- **数据分区**：
  - 按供应商、部门、科目、币种等维度分组，确保独立计算每个账户的余额。

---

### **6. 潜在问题与优化**
- **COALESCE匹配问题**：
  ```sql
  LEFT JOIN ... ON COALESCE(e.corpbcode, 'null') = COALESCE(d.corpbcode, 'null')
  ```
  使用`'null'`替代实际NULL可能导致错误匹配，建议改用`IS NULL`逻辑。
  
- **性能优化**：
  - 确保`tcaptime`、`corpbcode`等字段有索引。
  - 窗口函数可能在大数据量下较慢，需测试分区键的基数。

---

### **7. 结果字段说明**
| 字段           | 说明                   |
| -------------- | ---------------------- |
| `srcsheettype` | 单据类型（'50'为期初） |
| `corpbcode`    | 公司代码               |
| `bcode`        | 部门/业务单元代码      |
| `ccode`        | 科目代码               |
| `fcode`        | 币种代码               |
| `meetfcy`      | 应付金额（外币）       |
| `payfcy`       | 付款金额（外币）       |
| `fcying`       | 当前余额（外币）       |
| `tcaptime`     | 交易日期               |

---

### **总结**
该SQL通过以下步骤生成对账报表：
1. **提取交易明细**：按时间范围过滤并聚合应付/付款。
2. **计算期初余额**：联合到结果集中作为初始行（`rn=0`）。
3. **动态累计余额**：通过窗口函数逐行计算余额变化。

需重点关注期初余额计算的准确性、联合查询的性能优化，以及余额方向的业务逻辑一致性。



#   cluade3.7

> 我需要分析一段相当复杂的SQL查询，这是一个ERP系统中用于供应商往来对账的报表。让我逐步分析这个SQL的结构和功能。
>
> 首先，整个查询是一个嵌套的SELECT语句，共有三层主要的嵌套。我从内到外分析：
>
> 第三层（最内层）：
>
> sql
>
> ​    srcsheetcode,
>
> ​    max(vprepare) vprepare
>
> from ft_fund.ft_lrp_lrp
>
> where cuicode = 'C000000001'
>
>  and trunc(tcaptime) >= to_date('2024-03-01', 'yyyy-mm-dd')
>
>  and trunc(tcaptime) <= to_date('2025-05-21', 'yyyy-mm-dd')
>
>  and batype = 'A'
>
>  and (realvdc = 1 or sdate is not null)
>
>  and realdc = -1
>
>  and lccode is null
>
>  and ((realvdc = 1) or (realvdc = -1))
>
>  and rptype not in ('S1009.001', 'S1210.001')
>
> group by corpbcode, bcode, ccode, fcode, trunc(tcaptime), purprjcode, purprjicode, purordcode, purordicode, invicoder, invcoder, srccode, srcsheetcode
>
> having (sum((case when realvdc = 1 then fcy else null end)) != 0 or sum((case when realvdc = -1 then fcy else null end)) != 0)
>
> 这个查询从ft_fund.ft_lrp_lrp表中获取数据，设置了多个过滤条件，主要是：
>
> - 客户代码为'C000000001'
>
> - 时间范围从2024年3月1日到2025年5月21日
>
> - 业务类型('batype')为'A'
>
> - 有特定的条件组合(realvdc, sdate, realdc, lccode等)
>
> - 不包含特定类型的业务('S1009.001', 'S1210.001')
>
> 对结果进行分组和聚合计算，主要是计算了两个金额：
>
> - meetfcy: 当realvdc=1时的fcy总和（可能表示应付款）
>
> - payfcy: 当realvdc=-1时的fcy总和（可能表示已付款）
>
> 第二层：
>
> 这一层有两部分，通过UNION ALL连接：
>
> 第一部分是对第三层的结果添加了一个row_number()，按照多个字段进行分区和排序。
>
> 第二部分是一个单独的查询，它获取了期初的余额：
>
> sql
>
> ​    null purordicode,
>
> ​    null puroutordcode,
>
> ​    null invicoder,
>
> ​    null invcoder,
>
> ​    null tcaptime,
>
> ​    fcode,
>
> ​    null meetfcy,
>
> ​    null payfcy,
>
> ​    nvl(fcying, 0) fcying,
>
> ​    null srcicode,
>
> ​    null srccode,
>
> ​    null srcsheetcode,
>
> ​    null vprepare,
>
> ​    0 rn
>
> 这部分主要是通过一个复杂的WITH子查询计算了2024年3月1日之前的余额（fcying），并将其作为期初数据插入。
>
> 第一层（最外层）：
>
> 最外层查询主要是对第二层的结果进行处理，特别是计算了一个累计值：
>
> sql
>
> sum(case when rn = 0 then fcying else nvl(payfcy, 0 - meetfcy) end) over (partition by corpbcode, bcode, ccode, fcode order by rn asc) fcying
>
> 这个表达式计算了一个累计金额：
>
> - 当行号(rn)为0时（即期初数据），使用fcying的值
>
> - 否则，使用payfcy的值，或者如果payfcy为空，则使用0减去meetfcy的值
>
> 这个累计金额是按照供应商代码、业务代码等进行分区，并按照行号排序的。
>
> 总结，这个SQL查询的主要功能是：
>
> 1. 获取特定时间范围内（2024年3月1日到2025年5月21日）的供应商交易数据
>
> 1. 计算每个供应商、每个业务类型的应付款和已付款金额
>
> 1. 从期初余额开始，计算每个交易后的累计余额
>
> 1. 结果按照供应商代码、业务代码等进行分组，按时间顺序排列
>
> 这是一个典型的账务报表查询，用于显示供应商的对账明细，包括每笔交易的应付款、已付款和余额变动。

这个SQL查询非常复杂，我来逐层分析它的结构和功能：

## 整体结构

这是一个三层嵌套的查询，从内到外分析：

### 最内层查询

```sql
select max(srcsheettype) srcsheettype,
       corpbcode, bcode, ccode, /* 公司和供应商代码 */
       max(rptype) rptype, /* 单据类型 */
       /* 采购相关字段 */
       purprjcode, purprjicode, purordcode, purordicode,
       /* 金额计算 */
       sum((case when realvdc = 1 then fcy else null end)) meetfcy, /* 应付金额 */
       sum((case when realvdc = -1 then fcy else null end)) payfcy, /* 已付金额 */
       /* 其他字段 */
       ...
from ft_fund.ft_lrp_lrp
where cuicode = 'C000000001'
  and trunc(tcaptime) >= to_date('2024-03-01', 'yyyy-mm-dd')
  and trunc(tcaptime) <= to_date('2025-05-21', 'yyyy-mm-dd')
  /* 其他条件 */
  ...
group by corpbcode, bcode, ccode, fcode, trunc(tcaptime), ...
```

这部分查询从交易表中提取了指定时间范围内（2024-03-01至2025-05-21）的交易数据，并计算了每笔业务的应付款（meetfcy）和已付款（payfcy）。

### 中间层查询

中间层由两部分组成，通过UNION ALL连接：

1. 第一部分是对内层查询结果添加行号（rn）
2. 第二部分是计算期初余额的查询（标记为srcsheettype='50'，rn=0）：
   ```sql
   select '50' srcsheettype,
          corpbcode, bcode, ccode, fcode,
          null meetfcy, null payfcy,
          nvl(fcying, 0) fcying, /* 期初余额 */
          ...
          0 rn
   ```

这部分通过复杂的WITH子查询计算了2024-03-01之前的累计余额作为期初数据。

### 最外层查询

最外层查询的核心是计算累计余额：

```sql
sum(case when rn = 0 then fcying else nvl(payfcy, 0 - meetfcy) end) 
over (partition by corpbcode, bcode, ccode, fcode order by rn asc) fcying
```

这个表达式实现了：
- 当rn=0时（期初行），使用期初余额fcying
- 其他行，使用当期变动额（已付款payfcy或0减应付款meetfcy）
- 通过over子句按供应商分组并按行号累计

## 功能总结

这个SQL实现了一个完整的供应商对账报表，包括：

1. 获取期初余额（2024-03-01之前的累计数据）
2. 获取指定期间内的所有应付和已付交易
3. 计算每笔交易后的累计余额
4. 包含详细的业务信息（采购订单、项目、发票等）

这种报表结构常用于财务对账，帮助企业跟踪与供应商之间的往来款项情况，了解各个时点的债务状况。

