#  确认接口：开证、改证、撤证、预排额度、闭卷确认、预排额度失效

```
graph TD
    A[开始处理请求] --> B{检查confirmtype类型}
    
    B -->|属于LC_CONFIRM_APP_STATUS| C[查询LcApp对象]
    C --> D{对象是否存在？}
    D -->|不存在| E[返回错误响应]
    D -->|存在| F[获取操作员信息]
    
    F --> G{确认类型分支}
    G -->|1: 开证确认| H[校验必填字段]
    H --> I{校验通过？}
    I -->|否| J[返回错误响应]
    I -->|是| K[更新状态为70]
    K --> L[保存数据]
    L --> M[处理预排额度关联]
    M --> N[提交事务]
    
    G -->|2: 改证确认| O[校验必填字段]
    O --> P{校验通过？}
    P -->|否| Q[返回错误响应]
    P -->|是| R[更新状态为70]
    R --> S[保存数据]
    S --> M[处理预排额度关联]
    M --> T[提交事务]
    
    G -->|3: 撤证确认| U[校验必填字段]
    U --> V{校验通过？}
    V -->|否| W[返回错误响应]
    V -->|是| X[更新状态为95]
    X --> Y[保存数据]
    Y --> M[处理预排额度关联]
    M --> Z[提交事务]
    
    G -->|6: 闭卷确认| AA[校验状态]
    AA --> BB{状态允许？}
    BB -->|否| CC[返回错误响应]
    BB -->|是| DD[更新状态为75]
    DD --> EE[保存数据]
    EE --> FF[提交事务]
    
    B -->|属于LC_CONFIRM_PRE_CREDIT_STATUS| GG[查询LcPreCreditApp对象]
    GG --> HH{对象是否存在？}
    HH -->|不存在| II[返回错误响应]
    HH -->|存在| JJ[获取操作员信息]
    
    JJ --> KK{确认类型分支}
    KK -->|4: 预排额度确认| LL[校验必填字段]
    LL --> MM{校验通过？}
    MM -->|否| NN[返回错误响应]
    MM -->|是| OO[更新状态为70]
    OO --> PP[保存数据]
    PP --> QQ[提交事务]
    
    KK -->|5: 预排额度失效| RR[更新状态为90]
    RR --> SS[保存数据]
    SS --> TT[提交事务]
    
    B -->|其他类型| UU[返回错误响应]
    
    style G fill:#f9f,stroke:#333,stroke-width:2px
    style KK fill:#f9f,stroke:#333,stroke-width:2px
```

![image-20250422173119193](E:\Apple\TyporaMD\Img\image-20250422173119193.png)



```
flowchart TD
    A[开始: confirmLcAppInfo] --> B[初始化变量和提取确认类型]
    B --> C{确认类型判断}
    
    C -->|"1,2,3,6"| D[信用证相关处理分支]
    C -->|"4,5"| E[预排额度相关处理分支]
    C -->|其他| F[返回错误: 无效确认类型]
    
    %% 信用证相关处理分支
    D --> D1[查询信用证开证申请]
    D1 --> D2{是否找到记录?}
    D2 -->|否| D3[返回错误: 未找到申请]
    D2 -->|是| D4{确认类型细分}
    
    %% 开证确认
    D4 -->|"1"开证确认| G[开证确认处理]
    G --> G1[校验: 银行账户不为空]
    G1 --> G2[校验: 信用证号不为空]
    G2 --> G3[校验: 开证日期不为空]
    G3 --> G4[校验: 状态不为70生效]
    G4 --> G5[校验: 银行账户有效性]
    G5 --> G6{校验通过?}
    G6 -->|否| G7[返回校验错误信息]
    G6 -->|是| G8[更新状态为70生效<br/>设置银行账户、信用证号、开证日期]
    G8 --> G9[设置预排额度处理标志=true]
    
    %% 改证确认
    D4 -->|"2"改证确认| H[改证确认处理]
    H --> H1[校验: 改证日期不为空]
    H1 --> H2[校验: 状态不为70生效]
    H2 --> H3{校验通过?}
    H3 -->|否| H4[返回校验错误信息]
    H3 -->|是| H5[更新状态为70生效<br/>设置改证日期]
    H5 --> H6[设置预排额度处理标志=true]
    
    %% 撤证确认
    D4 -->|"3"撤证确认| I[撤证确认处理]
    I --> I1[校验: 撤证日期不为空]
    I1 --> I2[校验: 状态不为95撤证]
    I2 --> I3{校验通过?}
    I3 -->|否| I4[返回校验错误信息]
    I3 -->|是| I5[更新状态为95撤证<br/>设置撤证日期]
    
    %% 闭卷确认
    D4 -->|"6"闭卷确认| J[闭卷确认处理]
    J --> J1[校验: 状态不为75完结]
    J1 --> J2{校验通过?}
    J2 -->|否| J3[返回校验错误信息]
    J2 -->|是| J4[更新状态为75完结]
    
    %% 预排额度相关处理分支
    E --> E1[查询预排额度申请]
    E1 --> E2{是否找到记录?}
    E2 -->|否| E3[返回错误: 未找到申请]
    E2 -->|是| E4{确认类型细分}
    
    %% 预排额度确认
    E4 -->|"4"预排额度确认| K[预排额度确认处理]
    K --> K1[校验: 排证银行不为空]
    K1 --> K2[校验: 状态不为70生效]
    K2 --> K3{校验通过?}
    K3 -->|否| K4[返回校验错误信息]
    K3 -->|是| K5[设置排证银行<br/>更新状态为70生效]
    
    %% 预排额度失效
    E4 -->|"5"预排额度失效| L[预排额度失效处理]
    L --> L1[更新状态为90作废]
    
    %% 汇聚到保存操作
    G9 --> M[保存信用证申请更新]
    H6 --> M
    I5 --> M
    J4 --> M
    K5 --> N[保存预排额度申请更新]
    L1 --> N
    
    %% 预排额度联动处理
    M --> O{是否需要预排额度联动?}
    O -->|是| P[查询关联的预排额度申请]
    O -->|否| S[返回成功响应]
    P --> Q{是否一次性开证?}
    Q -->|是| R[更新预排额度状态为75完结]
    Q -->|否| S
    R --> S
    N --> S
    
    %% 错误处理汇聚
    D3 --> T[记录日志并返回错误响应]
    F --> T
    G7 --> T
    H4 --> T
    I4 --> T
    J3 --> T
    E3 --> T
    K4 --> T
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe
    classDef process fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef error fill:#ffebee
    classDef success fill:#e8f5e8
    
    class A,S startEnd
    class B,D,E,G,H,I,J,K,L,M,N,P,R process
    class C,D2,D4,E2,E4,G6,H3,I3,J2,K3,O,Q decision
    class D3,E3,F,G7,H4,I4,J3,K4,T error
    class S success
```

![mermaid-202578 192202](E:\Apple\TyporaMD\Img\mermaid-202578 192202.png)

# 撤回是否成功接口：开证、改证、撤证、预排额度、闭卷申请撤回是否成功接口

```
graph TD
    A[开始处理请求] --> B{检查confirmtype类型}
    
    B -->|属于LC_RETRACT_APP_STATUS| C[查询LcApp对象]
    C --> D{对象是否存在？}
    D -->|不存在| E[返回错误响应]
    D -->|存在| F[开启事务]
    
    F --> G{确认类型分支}
    G -->|1/2/3| H[检查状态是否为91]
    H --> I{状态是否为91？}
    I -->|否| J[抛出异常]
    I -->|是| K[调用checkRecallSuccessful]
    K --> L[删除日志]
    L --> M[提交事务]
    
    G -->|4| N[调用checkClosedBookRevoke]
    N --> O[删除日志]
    O --> P[提交事务]
    
    B -->|属于LC_RETRACT_PRE_CREDIT_STATUS| Q[查询LcPreCreditApp对象]
    Q --> R{对象是否存在？}
    R -->|不存在| S[返回错误响应]
    R -->|存在| T[检查状态是否为91]
    T --> U{状态是否为91？}
    U -->|否| V[返回错误响应]
    U -->|是| W[开启事务]
    
    W --> X{撤回结果分支}
    X -->|成功| Y[更新状态为90或75]
    Y --> Z[保存数据]
    Z --> AA[删除日志]
    AA --> AB[提交事务]
    
    X -->|失败| AC[恢复原状态]
    AC --> AD[保存数据]
    AD --> AE[删除日志]
    AE --> AF[提交事务]
    
    B -->|其他类型| AG[返回错误响应]
    
    style G fill:#f9f,stroke:#333,stroke-width:2px
    style X fill:#f9f,stroke:#333,stroke-width:2px
```

<img src="E:\Apple\TyporaMD\Img\image-20250422171936586.png" alt="image-20250422171936586" style="zoom: 80%;" />