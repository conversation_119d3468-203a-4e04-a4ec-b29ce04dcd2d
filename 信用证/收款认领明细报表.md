```
SELECT q.*, w.upcode upcode, w.paymode paymode
FROM (SELECT t.cuicode       cuicode,
             t.lrpicoder     lrpicoder,
             t.srcsheetcode  srcsheetcode,
             t.srccode       rclmcode,
             t.srcicode      rclmicode,
             t.ccode         ccode,
             t.fcode         fcode,
             t.rptype        rptype,
             b.rclmfcy       rclmfcy,
             t.fserate       fserate,
             t.sdate         recdate,
             t.purordcode    purordcode,
             t.purordicode   purordicode,
             t.purprjcode    purprjcode,
             t.purprjicode   purprjicode,
             t.salordicode   salordicode,
             t.salprjcode    salprjcode,
             t.salprjicode   salprjicode,
             t.salordcode    salordcode,
             t.vprepare      vprepare,
             t.salshipcoder  salshipcoder,
             t.salshipicoder salshipicoder,
             t.wcode         wcode,
             t.bcode         bcode,
             t.corpbcode     corpbcode,
             t.lrptcaptime   tcaptime,
             t.predate       predate,
             t.tsosicoder    tsosicoder,
             t.tsoscoder     tsoscoder,
             t.dlydicode     dlydicode,
             t.dlydcode      dlydcode
      FROM (SELECT lrpicoder, SUM(fcy) rclmfcy
            FROM ft_lrp_lrp
            WHERE srcsheetcode NOT IN ('FT-RPADJ.LrpOff', 'FT-RPADJ.LrpDeal')
              AND ((realdc = 1 AND srcsheettype <> '40') OR (realdc = -1 AND srcsheettype = '40'))
              AND realvdc = -1
              AND status = '70'
              AND batype = 'A'
            GROUP BY lrpicoder
            HAVING SUM(fcy) <> 0) b,
           ft_lrp_lrp t
      WHERE b.lrpicoder = t.lrpicode) q
         LEFT JOIN (SELECT *
                    FROM (SELECT '0005' paymode, m.rclmicode rclmicode, t.bankno upcode
                          FROM ft_rec_bno t,
                               ft_rec_rclm m
                          WHERE m.srcicode = t.bnoicode) g

                    UNION ALL

                    SELECT *
                    FROM (SELECT t.doctype paymode, m.rclmicode rclmicode, t.rdoccode upcode
                          FROM ft_rdoc_rdoc t,
                               ft_rec_rclm m
                          WHERE m.srcicode = t.rdocicode) g

                    UNION ALL

                    SELECT MAX(g.paymode) paymode, m.rclmicode rclmicode, t.dlydcode upcode
                    FROM ft_docu_dlyd t
                             INNER JOIN ft_rec_rclm m ON m.srcicode = t.dlydicode
                             LEFT JOIN ft_docu_dlyds g ON t.dlydicode = g.dlydicode
                    WHERE g.paymode IN ('0060', '0065', '0070', '0070')
                    GROUP BY m.rclmicode, t.dlydcode) w ON w.rclmicode = q.rclmicode
```

Thought for a few seconds

下面对这段 SQL 做逐步拆解与分析，并给出一些优化或注意点。

------

## 一、整体结构

```sql
SELECT
  q.*,
  w.upcode    AS upcode,
  w.paymode   AS paymode
FROM (
  -- 子查询 q，来源于主表 snds(ft_lrp_lrp) 的筛选与聚合
) q
LEFT JOIN (
  -- 子查询 w，通过三种不同表的数据 UNION ALL 得到付款方式与 upcode
) w
  ON w.rclmicode = q.rclmicode
WHERE
  snif('filter!=null','filter') = 0
  AND snif('paymodefilter!=null','paymode is not null') = 0;
```

- **主查询**：选取 `q` 子查询的所有列，再加上 `w` 子查询的两个字段 `upcode`、`paymode`。
- **LEFT JOIN**：以 `q.rclmicode = w.rclmicode` 为关联条件，保证主表的所有行都能保留，即使 `w` 没有匹配。
- **过滤条件**：两个 `snif` 函数分别对动态参数 `filter` 和 `paymodefilter` 做条件控制。

------

## 二、子查询 q（主表部分）

```sql
SELECT
  t.cuicode,
  t.lrpicoder,
  t.srcsheetcode,
  t.srccode    AS rclmcode,
  t.srcicode   AS rclmicode,
  t.ccode,
  t.fcode,
  t.rptype,
  b.rclmfcy,
  t.fserate,
  t.sdate      AS recdate,
  t.purordcode,
  t.purordicode,
  t.purprjcode,
  t.purprjicode,
  t.salordicode,
  t.salprjcode,
  t.salprjicode,
  t.salordcode,
  t.vprepare,
  t.salshipcoder,
  t.salshipicoder,
  t.wcode,
  t.bcode,
  t.corpbcode,
  t.lrptcaptime AS tcaptime,
  t.predate,
  t.tsosicoder,
  t.tsoscoder,
  t.dlydicode,
  t.dlydcode
FROM (
    -- b：先按 lrpicoder 聚合计算货币金额
    SELECT
      lrpicoder,
      SUM(fcy) AS rclmfcy
    FROM snds(ft_lrp_lrp)
    WHERE
      srcsheetcode NOT IN ('FT-RPADJ.LrpOff','FT-RPADJ.LrpDeal')
      AND (
        (realdc = 1 AND srcsheettype <> '40')
        OR (realdc = -1 AND srcsheettype = '40')
      )
      AND realvdc = -1
      AND status = '70'
      AND batype = 'A'
    GROUP BY lrpicoder
    HAVING SUM(fcy) <> 0
) b
-- 再与同表 t 做回表，拿到原始各种字段
JOIN snds(ft_lrp_lrp) t
  ON b.lrpicoder = t.lrpicode
```

- **表 ft_lrp_lrp**：看起来是某种“领料”（lrp）或“发料”明细表。
- **b 聚合逻辑**：
  - 先排除掉调整单类型（`FT-RPADJ.LrpOff`、`FT-RPADJ.LrpDeal`）。
  - 根据 `realdc` 和 `srcsheettype` 判断借贷方向，然后挑出 `realvdc = -1`、`status='70'`、`batype='A'` 的行。
  - 按 `lrpicoder` 聚合求和 `fcy`（外币金额），且过滤掉金额合计为零的记录。
- **回表 t**：使用聚合结果 `b.lrpicoder = t.lrpicode` 回到明细，保留明细的所有列。

------

## 三、子查询 w（付款方式 & upcode）

```sql
LEFT JOIN (
  -- 第一部分：从 ft_rec_bno + ft_rec_rclm 抓银行账号对应的收款单
  SELECT '0005' AS paymode,
         m.rclmicode,
         t.bankno   AS upcode
    FROM snds(ft_rec_bno) t
    JOIN snds(ft_rec_rclm) m
      ON m.srcicode = t.bnoicode
   WHERE snif('paymodefilter!=null','paymodefilter') = 0

  UNION ALL

  -- 第二部分：从 ft_rdoc_rdoc + ft_rec_rclm 抓回单类型对应 upcode
  SELECT t.doctype  AS paymode,
         m.rclmicode,
         t.rdoccode AS upcode
    FROM snds(ft_rdoc_rdoc) t
    JOIN snds(ft_rec_rclm) m
      ON m.srcicode = t.rdocicode
   WHERE snif('paymodefilter!=null','paymodefilter') = 0

  UNION ALL

  -- 第三部分：从 ft_docu_dlyd + ft_docu_dlyds + ft_rec_rclm 抓快递单对应 upcode
  SELECT MAX(g.paymode) AS paymode,
         m.rclmicode,
         t.dlydcode  AS upcode
    FROM snds(ft_docu_dlyd) t
    JOIN snds(ft_rec_rclm) m
      ON m.srcicode = t.dlydicode
    LEFT JOIN snds(ft_docu_dlyds) g
      ON t.dlydicode = g.dlydicode
   WHERE g.paymode IN ('0060','0065','0070','0070')
     AND snif('paymodefilter!=null','paymodefilter') = 0
   GROUP BY m.rclmicode, t.dlydcode
) w
  ON w.rclmicode = q.rclmicode
```

- **第一部分**：给所有在 `ft_rec_bno`（银行号表）里有匹配的收款单打上固定 `paymode='0005'`，并取 `bankno` 作为 `upcode`。
- **第二部分**：用单据类型 `doctype` 直接当作 `paymode`，取 `rdoccode` 作为 `upcode`。
- **第三部分**：针对快递单（`dlyd`），从 `ft_docu_dlyds` 表里挑选特定快递方式（0060/0065/0070），取 `MAX(paymode)` 以防多个匹配，然后把 `dlydcode` 当 `upcode`。

------

## 四、动态过滤：snif 函数

- `snif(expr, result)`：通常是一个自定义函数，用来实现“如果第一个参数为真，则忽略第二个参数，否则把第二个参数作为条件”。
- 最后 `WHERE`：
  - `snif('filter!=null','filter')=0`：如果外部传入 `filter` 参数不为空，则把参数值当作过滤条件，否则不加限制。
  - `snif('paymodefilter!=null','paymode is not null')=0`：如果 `paymodefilter` 不为空，则要求 `paymode IS NOT NULL`，否则不过滤。

------

## 五、性能与优化建议

1. **索引**
   - 确保 `ft_lrp_lrp(lrpicode)`、`ft_lrp_lrp(lrpicoder)`、`ft_rec_rclm(srcicode)`、`ft_rec_bno(bnoicode)`、`ft_rdoc_rdoc(rdocicode)`、`ft_docu_dlyd(dlydicode)` 等列都有合适的索引。
2. **子查询去重**
   - 第三部分里 `UNION ALL` 后再 `GROUP BY`，若同一个 `rclmicode`、`dlydcode` 多次出现，可考虑先在表内去重或限制行数。
3. **减少回表量**
   - 如果只需 `q` 中部分字段，可在回表时只选需要列，避免 SELECT *。
4. **逻辑清晰化**
   - 将三段 `UNION ALL` 拆成视图（或临时表），便于维护与调试。
5. **调试 snif**
   - 如果发现 `filter`、`paymodefilter` 参数逻辑不生效，可打印或临时去掉 `snif`，直接硬编码条件排查。

------

这样，你就能从整体到局部、从逻辑到性能，对这条 SQL 有比较全面的认识。若有更深的业务背景（如各表含义、字段含义），还可以进一步细化分析。