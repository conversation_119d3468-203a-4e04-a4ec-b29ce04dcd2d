### 1、保存设置 savemode

[保存的时候一定要设置 setUpadte  或者 saveMode]()

### 2、隐藏字段把单据号内码显示出来

隐藏字段把单据号  内码一定要出来



### 3、按钮入口：`CommandInvoker` 源码

```java
http://127.0.0.1:8081/XYFT/uiinvoke/00/zh_CN/theme0/sv-SN-EXT-CMD.CommandUIService.invokeCommand
snsoft.ext.btn.service.CommandUIService#invokeCommand
```

### 4、权限：设置隐藏或者不可点击

```java
<Control ctrlField="hidden" setValue="false" sheetcode="${sheetcode}" dataOpids="C">
    <Condition uiname="${mainTblname}" colname="status" matchValue="62" />
</Control>
    
    
<Control setValue="false" ctrlField="disabled" sheetcode="${sheetcode}" dataOpids="C"/> 
```



### 5、控制对话框里面业务员选择完才能选择客商！

```java
#new snsoft.ft.comm.busi.FTCheckAidsNotNullJSListener({checkAids[{tgtMuiName:'bwccode',aidCol:'ccode',checkCol:'bwccode.bcode'}]})

```

```java
//目标界面
public String	tgtMuiName;
//辅助录入列-多个逗号隔开
public String	aidCol;
//检查列-多个逗号隔开
public String	checkCol;
```

### 6、工作台初始化设置查询条件输入框编辑/只读

```java
	public void onDataLoad(DataSet dataSet, DataSetEvent e)
	{
		super.onDataLoad(dataSet, e);
		Table table = dataSet.getTables()[0];
		Table offgsTable = table.$get("_$fpcopytable");
		if (offgsTable != null){
			mainTable = offgsTable.getRootTable();
			Table offgTable = TableUtils.getTable(mainTable, "ft_rpadj_offg");
			window.console.log(offgTable.getValue("rptype"));
			Object reprpflag = mainTable.getDataSet().getValue("reprpflag");
			String rptype = JSString.obj2str(offgTable.getValue("rptype"),"");
			//主表“代收代付”非空，且〖预付款〗“收付项目”非‘货款’时，“收款方”可编辑，否则锁死
			boolean isEditCcode=false;
			// TODO  收付项目码表非贷款未确定？
			if (!"10".equals(rptype)&&$bool(reprpflag)){
				isEditCcode=true;
			}
			table.getQueryItemByName("ccode").setReadonly(isEditCcode);
			table.render(2);
		}
		
	}
```

### 7、监听：汇总主表字段

交单金额：只读，必录项，由 〖 交单明细 〗 页签“交单金额”汇总带出；

```java
<!--金额汇总-->
	<bean code="FT-DOCU.120" sortidx="120" impl="#SN-PLAT.SumListener?[{sumColumn:'fcy',addColumn:'fcy',tblname:'ft_docu_acdg'}]"/>
```



### 8、按钮操作以后界面刷新--用 mainTable.refreshTable

this.dataSet.refresh()会把数据刷没有掉

```java
@Override
	public void afterInvoke(InvokerEvent event)
	{
		//this.dataSet.refresh();
		this.mainTable.refreshTable();
	}
```

### 9、入口页勾选对话框弹框初始化默认值-dataSet.insertRow

> 对话框里面初始化数据以后要采用 dataSet.insertRow 才可设置 dataSet 数据

```java
DialogPane dialogPane = UIUtil.loadDialog("FT-DOCU.SignForDeliveryDlg", 0, null, new Size(600, 350), null, null);
dialogPane.title = cfg.title;
Table sfdTbale = (Table)dialogPane.getItemByName("signfordelivery");
sfdTbale.dataSet.insertRow(3);
sfdTbale.dataSet.setValue("senddate", initValue.$get("senddate"));
sfdTbale.dataSet.setValue("cscompany", initValue.$get("cscompany"));
dialogPane.showModal();
return dialogPane;
```

### 10、表格勾选多条一致性检查

```java
//检验 “收款方”、“币种”、“公司” 是否一致
String[] checkCols = {"ccode","fcode","corpbcode"};
FTUtils.checkGridErrRows(this.table, checkCols, rows,true,null);
```

### 11、存盘监听的应用场景

我的收款方式是如下需求，可以考虑存盘监听实现，而不用每种场景考虑

> 采用补数 SQL?

```java
1、货前后、收款方式：只读，显示名，取〖交单明细〗同名字段值，按“货前后”、“收款方式”维度汇总后去重显示；
2、交单金额：只读，取〖交单明细〗同名字段值，按“货前后”、“收款方式”维度汇总显示；
```

### 12、initvalue 没有用，采用 evalexpr 才有效果？？？

### 13、校验提交 table.checkNonBlankForSubmit

```java
<Invoker code="20">
   {
      beforeCheck:function(event){
         event.table.checkNonBlankForSubmit();
         return {};
      }
   }
</Invoker>
```

### 14、check 方法获取弹框 table 遍历对应的 dataset

在前端 check 方法中获取弹框的 table 然后遍历弹框的 dataset

```java
(InvokerEvent e, DialogPane d) -> {
Table fTable = (Table)d.getItemByName("financeCalculate");
int[] selectedRows = fTable.getSelectedRowNumbers();
```

### 15、前端 double 四舍五入并计算--FTUtils.mul

```java
double tempfee = FTUtils.mul(FTUtils.mul(fcy, diffDays), loanrate);
interestfcy = round(FTUtils.div(tempfee, 360),2);
dataSet.setValue("interestfcy", interestfcy);
```

### 16、前端时间计算比较

```java
Date rdate = dataSet.getValue("rdate");
Date caldate = dataSet.getValue("caldate");
rdate = new Date(rdate.getFullYear(), rdate.getMonth(), rdate.getDate());
caldate = new Date(caldate.getFullYear(), caldate.getMonth(), caldate.getDate());
int diff = (int) ((caldate.getTime() - rdate.getTime()) / (24 * 60 * 60 * 1000));
```

### 17、跳转第几行  fDataSet.gotoRow(i);

### 18、一次性校验多行 checkNonBlankForSubmitSelectRows

> 对于没有勾选的情况下，默认校验当前光标行--所以这种情况也符合详情页
>
> 对于勾选了的情况，就是校验勾选的所有行，一次性校验提示
>
> 

```java
TableUtils.checkNonBlankForSubmitSelectRows
```

### 19、record table 不要用宏定义

grid table 会自动给列长度 100    对于公共的要写宏定义

### 20、filterBean = FTPlatConstants.FTMixCodeFilterBean

```java
@SqlColumn(filterBean = FTPlatConstants.FTMixCodeFilterBean)
```

- 前端不配置别的：默认不会转大写，此时后端配置 FTMixCodeFilterBean：实际查询前端输入的不管大小写都会转大写，后端数据库也是不管大小写都转大写哦；可以逗号拼接可查询多个

  ```java
  select dlydicode from ft_docu_dlydg where upper(salshipcoder) like '%SSS01%'
  upper(salshipcoder)将数据库转大写哦；'%SSS01%'即使前端输入的是小写的sss01也会转大写匹配
  ```

- 前端配置了前模糊或者后模糊，以前端为准，此时后端全模糊失效，但是也会实际查询忽略大小写全部统一转大写匹配，且逗号拼接可查询多个

```java
<c name="salshipcoder" title="${RES.C}" sqltype="12"  prefixMatch="true" suffixMatch="true" toUpper="true" />
```

### 21、码表里面过滤某些值

> cmparams.JSONFILTER = "{n:'sheetcode', v: [''], op:'! in'}" 

```java
<!--来源单据类型-->
<c name="srcsheetcode" title="${RES.C}" sqltype="12" codedata="#sheettype" showname="true" aidInputerBtn="true" mutiple="true" cmparams.JSONFILTER="{n:'sheetcode',v:['FT-SSP.DomSalShip'],op:'in'}" />
```

### 22、权限--部门 人员  岗位

> 只需要配置部门 人员  岗位       通过岗位带出关系

![image-20240808185342389](E:\Apple\TyporaMD\Img\image-20240808185342389.png)

### 23、按钮隐藏控制不生效：

一个在按钮那边控制--optcontrlon，一个又在 JS 控制---冲突了？   但是按钮禁用不会。。

### 24.凭证用法

> 生成标记表--根据标记表生成凭证：需要配置凭证模板
>
> - 标记表需要配置 `cfg/sna/model/vminf/FT-LOAN.DocuDeliveryFinaLIVMark.xml`

- srcicode as sheetcode
- 插入主表字段 就是分组字段
- 注意把凭证也当做一个单据
- 根据 sheetcode  找到所有的接口类型  一个单据可能多个    
- 凭证模板---分录明细

![image-20240826153000021](E:\Apple\TyporaMD\Img\image-20240826153000021.png)

凭证模板核算字段，凭证模板核算字段与账。。

凭证字段



```java
数据源编号就是那边的sql查询
数据源定义：
    主子表+标记表
```

### 26、getCuInnerfld---DataColumn flag

```java
	/**
	 *   标志位
	 * <ul>
	 *    <li> bit       1 :     PrimaryKey,
	 *    <li> bit       2 :     NotNullable (  )
	 *	  <li> bit       4 :      auto inc
	 *    <li> bit       8 :     存盘 主字段  // 存盘时组织到 where .... 中
	 *    <li> bit      0x10  : 不能存空格或Null(目前未使用)
	 *    <li> bit      0x20  : 禁止存盘 (since 2010-11-23 )(DataSet中不加到 DataSetSave) 
	 *    <li> bit      0x40  : 禁止存盘 (since 2011-07-289)(DataSet加到 DataSetSave,JdbcUpdateRecord 判断) 
	 * </ul>
	 */
	public int flags;
```

> 一开始界面的 cuicode 的 flag = 0  导致获取不到这个 keyname 后面通过 primaryKey = "true" 设置 flag = 1

```java
				<c name="cuicode" sqltype="12" hidden="true" primaryKey="true" />

```

### 25、DataSetUtils.getColumnStrValues  逗号, 拼接内码

```java
 @Override
    public JSObject invoke(InvokerEvent event) {
        JSObject p = super.invoke(event);
        int[] rows = this.mainTable.getSelectedRowNumbers();
        String exraeicode = DataSetUtils.getColumnStrValues(mainDataSet, "exraeicode", rows);
        String sheetcode = DataSetUtils.getColumnStrValues(mainDataSet, "sheetcode", rows);
        p.$set("exraeicode", exraeicode);
        p.$set("sheetcode", sheetcode);
        p.$set("tcaptime", event.checkData.$get("tcaptime"));
        return p;
    }
```

### 26、视图没有主键 db.getPrimaryKeys

> 需要标注视图对应的表的主键

```java
String[] pknames = db.getPrimaryKeys(cfg.getTblname());
```

```java
	<CommandRef ref="{'SNA-VM.AutoVoucherCmdRefTplt':'createVoucher'}" >
			<property name="vmarkTblName" value="ft_loan_dlydfali_view" />
			<property name="srciodeFld" value="dlydfaliicode" />
			<property name="careYM" value="false" />
        	//需要标注视图对应的表的主键
			<property name="mainTblname" value="ft_loan_dlydfali" />
			<property name="tbname" value="lendtoolbar" />
			<property name="vksheetcode" value="FT-LOAN.DocuDeliveryFinaLI" />
			<property name="sheetcode" value="FT-LOAN.DocuDeliveryFinaLI"/>
		</CommandRef>
```

### 27、按钮配置传参到 js

```java
new snsoft.ft.loan.dlydfin.app.invoker.CreateDocuDeliveryFinaRedVoucherInvoker({redType:'redLiVoucher'})
    
private String redType;
	
	public CreateDocuDeliveryFinaRedVoucherInvoker(JSObject parameter)
	{
		super(parameter);
		redType = parameter.$get("redtype");
	}
```



### 28、界面穿透+前端调用后端接收返回值处理

> 前端打开交单融资申请单，先要根据后端判断，判断完会回传值给前端接收处理

```java
@Override
	public JSObject invoke(InvokerEvent event)
	{
		return $o("dlydicode", this.mainDataSet.get("dlydicode"));
	}

	@Override
	public void afterInvoke(InvokerEvent event)
	{
		JSObject invokeRtnVal = event.invokeRtnVal;
		String dlydfaicode = invokeRtnVal.$get("dlydfaicode");
		if (!$bool(dlydfaicode)){
			return;
		}
		SheetService sheetService = RInvoke.newBean(SheetService.class);
		BusiObject busiObject = sheetService.getBusiObject("FT-LOAN.DocuDeliveryFinaApp");
		JSObject data = $o("sheetcode", busiObject.sheetcode);
		data.$set(busiObject.innerfld, dlydfaicode);
		ViewDetailListener.invokeViewDetail(data, busiObject.outerfld);
	}
```

后端返回 Map 接收

```java
if (CollectionUtils.isNotEmpty(deliveryFinaAppList))
		{
			return MapUtils.toMap("dlydfaicode", deliveryFinaAppList.get(0).getDlydfaicode());
		}
		return MapUtils.toMap("dlydfaicode", "2");
```

### 29、获取码表名称

```java
snsoft.dx.codedata.CodeDataUtils#getCodeName
```

### 30、前端不写 invoker  后端获取默认的 mains 传参

> 在生成下游单据之前进行数据校验，采用 CmdCommInvoker 默认的传参

```java
<ClientInvokers>
    <Invoker code="10">
    new snsoft.ft.comm.cmdreg.CmdCommInvoker({time:5})
    </Invoker>
    <Invoker code="20">
    new snsoft.ft.comm.cmdreg.SheetDataCopyInvoker({copyMode:'30',commands:[{command:'createDocuDeliveryFina',cpid:'FT-LOAN.DocuDeliveryFinaByDocuDelivery',muiid:'FT-LOAN.DocuDeliveryFinaAppWorkBench',sheetCode:'FT-LOAN.DocuDeliveryFinaApp',copytype:'20',checkCols:['corpbcode','salccode','fcode','lccode']}]})
    </Invoker>
    </ClientInvokers>
    <UIInvokers>
   	 	<Invoker code="10" method="beforeCheck">
    		#FT-DOCU.CheckCreateDocuDeliveryFinaInvoker
    	</Invoker>
    </UIInvokers>
```

如何从 mains 获取

```java
String dlydicode = StrUtils.obj2str(invokerParam.getMain().get("dlydicode"));
```

### 31、拷贝工作台对应界面--控制是否展示生成 xxx 按钮

```java
toolHidCommands:'createDocuDeliveryFina'
```

### 32、前端判断数据 dataset 是否有值，ensureopen

可以采用 dataset.getrows().length   但是默认情况下，如果是要获取其他 tab 页的数据 dataset，在首次加载的时候是没有值的。

所以要采用 ensureopen 确保数据有值



### 33、前端从 dataset 获取多个值-getColumnStrValues，默认逗号，隔开

```java
String vmarkicodes = DataSetUtils.getColumnStrValues(mainDataSet, "vmarkicode", rows);
```

### 33、按钮开启事务、分布式事务

开启普通事务 `transTable="${mainTblname}"`

```java
<Command cmd="lastreceipt" title="${FT.cmd_lastreceipt?最后一次到单}" pstn="bindbar" btndesc="{tbname:'toolbar'}" checkModified="true"
			tblname="${mainTblname}" transTable="${mainTblname}">
```

同时在此基础上还要改成 `ServerInvokers`（分布式事务也是这样？）

```java
<ServerInvokers>
    <Invoker code="10" method="invoke">
    #FT-DOCU.LastReceiptUIInvoker
    </Invoker>
</ServerInvokers>
```

### 34、入口穿透、单据内穿透

```java
// 默认任意列都是穿透到本单据
<bean code="FT-DOCU.30" sortidx="30" impl="#SN-PLAT.PlatEntryListener?[{sheetCode:'${sheetcode}'}]" />

```

根据 tgtUINames 配置界面穿透，要对方支持配置好了穿透

```java
// 单据内穿透    
<bean code="FT-DOCU.DocuDeliveryFinaApp.170" sortidx="170" impl="#new snsoft.plat.bas.viewdetail.ViewDetailListener({'tgtUINames':['ft_loan_dlydfa_bas']})"/>   
```

要在这里配置好了穿透 dlydcode---> dlydicode

```java
<Details>
		<Colname>dlydcode</Colname>
		<TACBlock><![CDATA[
        dlydicode = dataSet.get("dlydicode")
        if dlydicode == null
            return null
        end if
        //确保记录存在
        assertExists("ft_docu_dlyd",dlydicode)
		info.funcid = "FT-DOCU.DocuDeliveryDetail"
        info.pm.put("AutoRefresh",1)
        info.pm.put("InitValue.dlydicode",dlydicode)
        return info
        ]]></TACBlock>
		<Remark><![CDATA[
        	说明：议付交单-入口穿透
        ]]></Remark>
	</Details>
```

### 35、详情页--删除按钮--会查询全部

```java
@SqlColumn(isnull = true,precompiled = true)

	/**
	 * <pre>
	 * 过滤条件使用预编译模式：col=?，即
	 * 参数有值时：col=?[v]
	 * 参数为空时：col=?[null]
	 * 
	 * 与{@link #flags()}=9同义。
	 * </pre>
	 * @return
	 */
	boolean precompiled() default false;

	/**
	 * <pre>
	 * 参数为空时的过滤条件为：col is null
	 * 参数不为空时的过滤条件为：col sqlop [v]
	 * 
	 * 与{@link #flags()}=1同义。
	 * </pre>
	 * @return
	 */
	boolean isnull() default false;
```

### 36、字符串比较多个值

```java
if (record.isInsertMode() && StrUtils.isStrIn("1,2",redflag.toString()))
{
    //红蓝操作、红冲操作，都是更新为作废
    stopList.add(salshipicoder);
}

//原始蓝单,以及红蓝的蓝单生效以后都要生成生效的待交单余额对象
if (saveMode == DXConst.OP_INSERT && (Objects.equals(rbflag, 3) || Objects.equals(rbflag, 0)))
{
    genDocuDeliveryCor(settObjects, Objects.equals(rbflag, 3));
    return;
}
```

### 37、前端获取后端初始化好的码表

> 将 UI 监听配置在 fn-detail ，前端默认可以加载这个初始化好的码表参数
>
> snsoft.ft.comm.match.RpModeMatchValue
>
> snsoft.ft.comm.ui.RpModeUIListener

### 38、SelectTableDataDialog 码表设置只读初始化

```java
aiprops="initParasVales:{sheetcode:'FT-DOCU.DocuDelivery',opids:'C'},paramItemsCtrl:{fcode:{readOnly:true},corpbcode:{readOnly:true}}"
```

### 39、参数校验 FTPlatException.requireNonNull(rptype, "rptype");

>  请求参数‘rptype’不允许为空！

### 40、对金额字段取反

```java
snsoft.ft.fund.util.FundUtils#fcyNegation
```

### 41、手动调用核销

先定义一个核销定义 [FT-RPADJ.AdvPayOffgCor.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\res\cor\FT-RPADJ.AdvPayOffgCor.xml) 

```java
snsoft.ft.fund.util.FundUtils#dealTargetCor
```

42、界面 ctrl+f1 的数据隐藏

界面 ctrl+f1 的数据隐藏默认会把 dataset 的数据展示出来，但是不写 c 标签的话，不展示对应的中文，只有英文？

顾问要求的一般是对应 table 里面的属性中文？

### 43、dataLoaded 和 onDataLoad 区别/ensureOpened

要用 dataLoaded  不然获取不到数据集，同时使用 ensureOpened()

### 44、复选框如何作为查询条件、以及如何展示在列表

```
/** 是否退款 */
private              String   refundflag;

<c name="refundflag" button="true" sqltype="12" mutiple="true" codedata="0:退款" showname="true" titleHidden="true" initval="1"/>

<c name="srcsheettype#40" title="${ft_pay_rdcdetailquery_view.srcsheettype}" sqltype="12" sqlexpr="srcsheettype" rdonly="true"/>

//是否退款
boolean isrefund = StringUtils.equals("0", params.getRefundflag());
if (isrefund)
{
    params.addFilter(SqlExpr.columnEqValue("m.srcsheettype", "40"));
}
```

### 45、手动调用拷贝新建

```
Map<String,Map<Object,Object>> srcParams = docuDeliveryClients.stream().collect(Collectors.toMap(DocuDeliveryClient::getDlydicode, obj -> Map.of()));
//Map<String, Map<String,Object>> srcParams = new HashMap<>();
//for(DocuDeliveryClient deliveryClient : docuDeliveryClients)
//{
//	srcParams.put(deliveryClient.getDlydicode(), MapUtils.toMap(FTSheetVO.SheetCol, DocuDeliveryClient.SheetCode));
//}


// 调用拷贝新建逻辑
PlatBusiSheetCopyService.PlatBusiSheetCopyParams param = new PlatBusiSheetCopyService.PlatBusiSheetCopyParams();
param.setUsercode(AppContext.getUserSession(true).getUserCode());
param.setTgtSheetCode("FT-LOAN.DocuDeliveryFinaApp");
param.setCopycode("FT-LOAN.DocuDeliveryFinaByDocuDelivery");
param.setSrcParams(srcParams);
param.setExtParams(new HashMap<>());
String sheetInnercode = PlatBusiSheetCopyService.impl.copyData(param);
```

### 46、凭证报错排查步骤

1. 开启调试
2. 查看数据源、标记表数据
3. 查看分录信息



### 47、去掉分页

删除下面的，后端对应去掉对应分页查询

```
dsprops.pageRows="10"
```

### 48、按钮控制，默认界面字段控制取值--删除按钮

状态大于 20 的时候设置删除按钮不可用，这里需要传入 uiname，不然默认是当前界面，当前界面是 ft_docu_dlydg，里面没有 status 字段，所以取不到值，会不生效

```
<Control setValue="false" ctrlField="disabled" sheetcode="${sheetcode}" dataOpids="C">
    <Condition colname="status" matchValue="20" matchMode="4" uiname="ft_docu_dlyd" />
</Control>
```

### 49、VOListener   正常都要加，比如小数位数

```
<bean code="HELP-INV.30" sortidx="30" impl="#SN-UI.VOListener?[{sheetCode:'${sheetcode}'}]"/>
```

### 50、dlgParam 和 copyMap 区别

```
dlgParam将界面的值传入到码表弹框里面进行初始化赋值
copyMap  将选择完的弹框里面的值赋值到当前界面的字段
```

### 51、 辅助录入输入点击监听+码表+码表初始化参数方法

> 码表获取初始化参数，如果是字典类型才能用 JSONFILTER？
>
> 如果是非字典，采用 setInitParamValue

```
// 点击按钮所在的表，该表中需要有对应码表的列
Table table = (Table) dialog.getItemByName("hbnz_trd_ord_copy");
// 选中该码表列
table.setSelectedColumn(table.indexOfColumn("salordcode"), true);
// 获取码表列
TableColumn column = table.getColumn("salordcode");
//根据码表列，获取码表弹窗
SelectTableDataDialog selectDialog = (SelectTableDataDialog) column.aidInputer;
//设置码表选项
selectDialog.selOptions = selectDialog.selOptions | 4;
//设置公司等过滤条件
selectDialog.setInitParamValue("limitmode", "20");
```



```
@Override
	public void itemAidInputing(Table table, TableEvent e)
	{
		super.itemAidInputing(table, e);
		window.console.log("floatpricecomb");
		if("floatpricecomb".equals(e.item.name)){
			String prjicode=table.dataSet.getValue("prjicode");
			Object bcodes=  RInvoke.rmInvoke("snsoft.ft.ord.comm.service.impl.OrdUIServiceImpl.getParentBcodesByPrjicode", prjicode);
			SelectTableDataDialog selectTableDataDialog = (SelectTableDataDialog) table.getColumn("floatpricecomb").aidInputer;
			selectTableDataDialog.setInitParamValue("bcode",bcodes);
			//CodeData codeData = (CodeData) e.forTblColumn.selectOptions;
			//codeData.setLoadParameter("JSONFILTER", snsoft.ft.utils.FTUtils.obj2InJsonFilter("bcode",new String[]{"11","223"}));
			//codeData.clearBuffered();
		}
	}
```

### 52、  SQLmapper 分页需要额外加上参数

```
Map<String, Object> paramsMap = MapUtils.toMap("filter", sqlExpr);
paramsMap.put("Page._PgRows", params.getRowsPerPage());
paramsMap.put("Page._PgNumber", params.getPageNo() - 1);
paramsMap.put("Page.AggSelect", params.getClientProperty("Page.AggSelect"));
QueryResults<TStockBWcodeChangeVou> results = TsmSqlMapper.impl.queryTStockBWcodeChangeVou(paramsMap);
```

### 53、SqlExpr   两个or用括号合并在一起

```
SqlExpr invstatusSqlExpr = SqlExpr.or(SqlExpr.columnNotInValues("invstatus", new String[] { "manuclosed", "autoclosed" }), SqlExpr.columnIsNull("invstatus"));
```

### 54、问题排错： Cannot invoke "Object.toString()" because "a" is null

错误问题只看到了自己的代码`getRdcRecDuedate(Unknown Source)`，实际上面的代码应该也要看的，也是属于南北的代码，而不是官方的源码。

最终问题是因为sql数据大于1000个的时候，getMaxStringLength方法里面会进行切割导致，采用了srcicode.toString

```
protected int getMaxStringLength()
		{
			int max = 32;
			if (sqlTypes.length == 1)
			{
				if (sqlTypes[0] == Types.VARCHAR || sqlTypes[1] == Types.NVARCHAR)
				{
					for (Object a : list)
					{
						String str = a.toString();
						int len = 0;
```



```
Caused by:java.lang.NullPointerException:Cannot invoke "Object.toString()" because "a" is null
	at snsoft.dx.sql.SqlDatabase$TmpTableImpl.getMaxStringLength(SqlDatabase.java:3854)
	at snsoft.dx.sql.SqlDatabase$TmpTableImpl.getInnerSqlSelectStmt(SqlDatabase.java:3948)
	at snsoft.sqlm.config.MapperEntity$Arg.buildInExpr(MapperEntity.java:606)
	at snsoft.sqlm.config.MapperEntity$ArgName.toSqlExpr(MapperEntity.java:695)
	at snsoft.sqlm.config.MapperEntity$DataLoaderForDynFunc.lambda$load$1(MapperEntity.java:1093)
	at snsoft.sqlm.config.MapperEntity.forEachReplace(MapperEntity.java:454)
	at snsoft.sqlm.config.MapperEntity.forEachReplace(MapperEntity.java:414)
	at snsoft.sqlm.config.MapperEntity.forEachReplace(MapperEntity.java:511)
	at snsoft.sqlm.config.MapperEntity$DataLoaderForDynFunc.load(MapperEntity.java:1098)
	at snsoft.sqlm.config.MapperEntity$DataLoaderForDynFunc.load(MapperEntity.java:1046)
	at snsoft.sqlm.config.MapperEntity.queryReadDataSet(MapperEntity.java:535)
	at snsoft.sqlm.handler.SqlMapperExecutorDefault.invokeQuery(SqlMapperExecutorDefault.java:64)
	at snsoft.sqlm.handler.SqlMapperExecutorDefault.invokeConfig(SqlMapperExecutorDefault.java:28)
	at snsoft.sqlm.handler.SqlMapperHandler.invoke(SqlMapperHandler.java:40)
	at jdk.proxy2/jdk.proxy2.$Proxy186.getRdcRecDuedate(Unknown Source)
	at snsoft.rpt.pay.rdc.service.impl.RdcDetailQueryToolService.handleRecData(RdcDetailQueryToolService.java:80)
	at snsoft.rpt.pay.rdc.service.impl.RdcDetailQueryToolService.loadData(RdcDetailQueryToolService.java:62)
	at snsoft.rpt.pay.rdc.service.impl.RdcDetailQueryToolService.loadData(RdcDetailQueryToolService.java:32)
```

55、新建按钮弹框报错不关闭？

```
CpsUsePayAppNewSheetInvoker

<Command cmd="newSheet" title="${cmd_newSheet}" pstn="${pstn?bindbar}"   btndesc="{tbname:'${tbname?toolbar}'}"  tblname="${mainTblname}" >
		<Control ctrlField="hidden" setValue="false" sheetcode="${sheetcode}" btnOpids="${opids?C}"/>
		<ClientInvokers>
			<Invoker code="10">
				new snsoft.plat.bas.sheet.cmd.sheet.CommandNewSheet({sheetcode:'${sheetcode}','muiid':'${muiid?}'})
			</Invoker>
		</ClientInvokers>
	</Command>
```



由于新建按钮是底层的，在check方法打开弹框，会校验非空，而自己校验的方法在beforeInvoke,所以check方法点击确定以后会校验空的报错，然后继续执行beforeInvoke，而此时弹框已经关闭了！！！

继承CommandNewSheet,重写check,在`t.checkNonBlankForSubmit();`之后再进行自己的方法补充？？

>  这种方式：继承重写的思想可以用于其他功能！

```
	@Override
	public ConfirmRequest check(InvokerEvent event)
	{
		if ($bool(muiid) && muiid.length() > 0)
		{
			return newDialogWithFunc(event, (InvokerEvent e) -> {
				DialogPane dlg = UIUtil.loadDialog(muiid);
				dlg.title = cfg.title;
				dlg.showModal();
				return dlg;
			}, (InvokerEvent e, DialogPane d) -> {
				Table[] tables = TableUtils.getAllTablesFromComponent(d, false);
				if ($bool(tables) && tables.length > 0)
				{
					for (int i = 0, len = tables.length; i < len; i++)
					{
						Table t = tables[i];
						t.postPending();
						if (!t.dataSet.isChanged(false))
							t.dataSet.setRowChanged(true);
						t.checkNonBlankForSubmit();
						event.checkData.$set(UrldefvaluesCol, DataSetUtils.getValuesTo(t.dataSet, t.dataSet.rowAt, null));
					}
				}
			});
		}
		return null;
	}
```



### 55、界面子表始终都是update  无法插入insert

排查了界面

  vo以后没发现异常，最终是在存盘监听里面始终`upag.setUpdate()`

### 56、主表根据子表值进行汇总

```
<!--原币金额 汇总-->
<bean code="FT-CPS.90" sortidx="90" impl="#SN-PLAT.SumListener?['fcy','ft_cps_upag','fcy']" />
```

### 57、如何在按钮注册里面写tac

```
    <!--删除-->
    <CommandRef ref="{'FT-PLAT.SheetCmdRefTplt':'regDelete'}" />
    <Command cmd="regDelete">
        <ClientInvokers>
            <Invoker code="15"><![CDATA[
    {
                    beforeCheck:function(event)
                    {
                        var isprodel = event.mainDataSet.getValue("isprodel");
                        if(isprodel=='Y')
                        {
                            throw new Error("自动生成单据，禁止操作!");
                        }
                        
                    }
                }
]]></Invoker>
            
        </ClientInvokers>
    </Command>
```

### 58、按钮注册如何实现或的关系

>  按钮注册如何表示空，不能写matchValue="null"或者matchValue=""

```
  <!--删除-->
        <CommandRef ref="{'FT-PLAT.OperateCmdRefTplt':'gdelete'}" />
        <Command cmd="gdelete">
            <Control setValue="false" ctrlField="disabled" sheetcode="${sheetcode}" dataOpids="C">
                <Condition uiname="ft_cps_upa" colname="status" matchMode="4" matchValue="20"  grp="0"/>
                <Condition uiname="${mainTblname}" colname="srcsheetcode"  matchValue="FT-CPS.CpsUsePayApp"  grp="0"/>
                <Condition uiname="ft_cps_upa" colname="status" matchMode="4" matchValue="20"  grp="1"/>
                <Condition uiname="${mainTblname}" colname="srcsheetcode" grp="1" />
            </Control>
        </Command>
```

### 59、底层模板按钮使用传参

比如新建按钮，会弹框一个自定义的界面，此时需要传参一些参数

```
muiid:'${muiid}'
replaceMode:${replaceMode?3},copyNonblankCheck:${copyNonblankCheck?false}.....省略
```

```
	<!--单据类型-->
	<property name="sheetcode" value="FT-CPS.CpsFloatPointDef" />
	<!--按钮依附表名-->
	<property name="mainTblname" value="ft_cps_fpt" />
	<!--弹出界面表名-->
	<property name="uiname" value="ft_cps_fpt" />
	<!--弹出界面-->
	<property name="muiid" value="FT-CPS.CpsFloatPointDefDialog" />
	<CommandGroup uiname="ft_cps_fpt">
		<!--《新建》按钮-->
		<CommandRef ref="{'FT-PLAT.BindbarCmdRefTplt':'nsheet'}">
			<property name="copyNonblankCheck" value="true"/>
			<property name="replaceMode" value="4"/>
		</CommandRef>
		<Command cmd="nsheet">
			<ClientInvokers>
				<Invoker code="30">
					new snsoft.ft.comm.cmdreg.CmdCommInvoker({mode:1})
				</Invoker>
			</ClientInvokers>
		</Command>
```



```
<Command cmd="nsheet" pstn="${pstn?bindbar}" title="${FT.cmd_newSheet}" btndesc="{tbname:'${tbname?toolbar}'}"  tblname="${mainTblname}">
    <Control setValue="false" ctrlField="disabled" sheetcode="${sheetcode}" btnOpids="${opids?C}"></Control>
    <ClientInvokers>
       <Invoker code="10">
          new snsoft.ext.cmd.com.biz.DialogInvokerCreateTableMain({replaceMode:${replaceMode?3},copyNonblankCheck:${copyNonblankCheck?false},copyValidCheck:${copyValidCheck?true},useOkEnabled:false,dlgParam:{muiid:'${muiid}',uiname:'${uiname}',noValueMapCols:'${noValueMapCols?}'}})
       </Invoker>
    </ClientInvokers>
</Command>
```

### 60、报表补数特殊情况处理：A字段补数+B字段补数  C字段=A-B

方法1：

由于A,B字段都是补数的，并且SQL直接写在了QR文件里面，所以不能直接拿到这个A,B 数据，此时可以写一个自定义QueryToolUIListener放在这个之后，就拿得到补数的数据了！

```
<!--UI补数-->
<bean code="RPT-LC.100" sortidx="100" impl="#SN-PLAT.QueryToolUIListener?[{tgtUINames:'ft_rpt_lc_app',qrycode:'RPT-LC.LcSummaryQueryQR'}]"/>
```

方法2：

本身写了一个`LcSummaryQryToolService`,但是这个位置在`RPT-LC.LcSummaryQueryQR.xml`文件放在了前面，此时拿不到这个acdfcy和backfcy,所以可以挪动位置，把这个ToolService放在后面就可以

```
<!--补数顺序位置不可变动!!!!放在后面，可拿到补数后的融资金额和还款金额，进行补数-->
	<Details>
		<Title> 可追溯信用证汇总表-事业部、未还款金额补数</Title>
		<Remark><![CDATA[
        ]]></Remark>
		<MatchColumns>bcode</MatchColumns>
		<ResultColumns>deptbcode</ResultColumns>
		<LoadImpl><![CDATA[
		   #RPT-LC.LcSummaryQryToolService
        ]]></LoadImpl>
	</Details>
```

### 61、底层拷贝方法：同名+不同名

```
//拷贝发货单主表数据数据 同名,不同名
FTUtils.copyVO(record, cor,
       new String[] { "salccode", "salshipicoder", "salshipcoder", "fcode", "corpbcode", "vprepare", "predate", "modifydate", "modifier", "cuicode", "status" });
FTUtils.copyVO(record, cor, "srcicode=salshipicode,srccode=salshipcode");
//内控信息
FTUtils.copyVO(ic, cor, "salordcode,salordicode");
```

### 62、报表补数：  SQL里面未到账金额=A-B当做查询条件---外面套一层子查询！

可追溯交单情况报表的未到账金额以及交单状态就是通过外面套一层子查询实现，这种可以实现的前提是数据不是通过service补数实现的，而是直接在sql里面查询出来的，如果是service额外补数，则没法

### 63、报表关联  join  on 字段过滤和where过滤

下面这个redflag 不能放在where 不然会有问题

```
left join snds(ft_docu_dlydc) dc on dc.dlydicode = d.dlydicode and dc.redflag = 0
left join snds(ft_loan_dlydfali) li on li.dlydicode = d.dlydicode  and li.redflag=0
```

### 64、按钮如何判断是否具有本单据权限---可执行/可点击、显示条件

判断是否具有本单据权限

- 一种是hidden显示，一种是disabled 可点击

```
<Control ctrlField="hidden" setValue="false" sheetcode="${sheetcode}" btnOpids="${opids?C}"/>
```

判断是否具有当前单据+下游单据权限

```
<Control ctrlField="hidden" setValue="false" sheetcode="${sheetcode}" btnOpids="${opids?C}"/>
代码判断下游单据权限
AuthInfoUtils.assertHasLimit(DocuDeliveryFinaAppService.SheetCode, new String[] { LimitConst.Opid_C });
```

>  修改交单银行按钮：
>
> 1、当前用户具有本单据操作权限；
>
> 2、 当前单据为非可修改状态；
>
> 3、议付交单主表“收款方式”存在非‘TT’

之前采用JS控制2/3点+按钮注册判断第1点，这样会发现只有第1点生效了，2/3点没有同时去满足，但是又没法在JS判断是否具有本单据操作权限，所以采用按钮注册判断3点，可以写JS

```
<!--1、当前用户具有本单据操作权限；
    2、当前单据为非可修改状态；
    3、议付交单主表“收款方式”存在非‘TT’。-->
<Control ctrlField="hidden" setValue="false" sheetcode="${sheetcode}" dataOpids="C">
    <Condition colname="status" matchMode="3" matchValue="20" />
    <Condition uiname="ft_docu_dlyd" matchValType="new snsoft.ft.docu.dlyd.lis.ModifyBankacccodeMatchValue({})" />
</Control>
```

### 65、码表--两个字典如何关联（级联）展示

字典文件里面加上var01关联关系，可以配置多个！

```
{
		"dicticode": "FT.AcdRomode",
		"name": "赎单方式",
		"remark": "赎单方式",
		"var01": "付款方式",
		"infos": [
			{
				"code": "10",
				"name": "押汇",
				"var01": "0025,0070"
			},
			{
				"code": "20",
				"name": "付汇",
				"var01": "0025,0070,0060"
			},
			{
				"code": "40",
				"name": "承兑",
				"var01": "0065,0075,0030"
			},
			{
				"code": "60",
				"name": "国内证代付",
				"var01": "0060"
			}
		]
	},
```

然后界面里面配置参数`cmprops.pmFromPane="{var01:'paymode'}"  cmparams.JSONFILTER="{n:'var01',v:'%{var01}%',op:'like'}"`

```
<c name="romode" title="${RES.C}" sqltype="12"  disableed="true" showname="true" aidInputerBtn="true"
disableDelIfAI="true" codedata="#DT_FT.AcdRomode" cmprops.pmFromPane="{var01:'paymode'}"  cmparams.JSONFILTER="{n:'var01',v:'%{var01}%',op:'like'}" />
```

### 66、学习码表、字典、辅助录入区别，学习界面xml码表参数配置，过滤，以及带出某些字段

> 码表：只有码和名；没有其他字段，减少网络传输？     辅助录入：会有其他字段，比如弹框带出来其他的
>
> CodeData指的是码表，是包括码表定义、辅助录入一系列功能的统称。
> 新版底层中，我们将用于界面的码名映射和辅助录入区分开，两者各司其职，从而避免旧版中界面辅助录入列较多，导致码名映射效率不高的问题。
> 辅助录入和码名映射理应是两个功能，只是一般情况下会成对出现。

```
码表参数
cmparams：码表查询参数，JSON格式，设置到xjs.CodeData.loadParameter上
cmparams.JSONFILTER
cmprops.pmFromPane
cmparams.status


辅助录入参数
aiprops
aiprops.copyMap
aiprops.dlgParam
aiprops.initParasVales

```

码表：

