# 积累

1、按钮invoker顺序：
前端执行完invoke，执行完如果后端有配置invoke  调用后端的invoke，根据method='invoke'

```java
<ClientInvokers>
    <Invoker code="10">
        new snsoft.ft.comm.cmdreg.CmdCommInvoker({time:4,mode:9})
    </Invoker>
    <Invoker code="20">
        new snsoft.ft.docu.accord.invoker.DocuAccordSendSerCancelInvoker({})
    </Invoker>

</ClientInvokers>
<UIInvokers>
    <Invoker code="10" method="invoke">
        #FT-DOCU.DocuAccordSendSerCancelInvoker
    </Invoker>
</UIInvokers>
```

```java
	<td>按钮事件处理父类</td>
 * 		<td>onClick:按钮点击前，处理与当前按钮业务无关的前置操作，可以返回弹出对话框；
 * 			<br/>beforeCheck:检查前方法，如果存在返回值则调用服务端beforeCheck方法；
 * 			<br/>check:检查客户端逻辑，可以返回弹出对话框；
 * 			<br/>beforeInvoke:调用前方法，如果存在返回值则调用服务端beforeInvoke方法；
 * 			<br/>invoke:按钮客户端业务逻辑接口，如果存在返回值则调用服务端invoke方法；
 * 			<br/>afterInvoke:如果服务端invoke方法存在返回值，则调用此方法；
 * 		</td>
```







# DEMO

VO注解：

| 名称                   | 校验 | 使用场景                                                     | 使用说明                                           |
| ---------------------- | ---- | ------------------------------------------------------------ | -------------------------------------------------- |
| @Table                 | 否   | 用于标注VO对象同数据库表的映射，name属性可选填写。当VO名与数据库表名不一致时必须指定，否则使用小写VO类名作为表名。 | @Table：表名                                       |
| @ValidResource         | 否   | 定义VO字段名称所在的资源文件的ID，注解标识主单据（真实单据号），用于附件操作权限控制 | @ValidResource：资源文件ID                         |
| @SheetInfo             | 否   | 定义当前VO数据对应的单据定义ID                               | @SheetInfo：单据定义                               |
| @Id                    | 否   | 定义当前VO数据主键                                           | @Id：主键                                          |
| @Column                | 否   | 用于标注VO对象的属性同数据库表列的映射关系，name属性可选填写。当属性名与列名不一致时必须指定，否则使用小写属性名作为列名 | @Column：映射关系                                  |
| @Cuicode               | 是   | 商户注解，作用：  1、如果启用商户，则不可为空且必须存在；  2、如果启用商户，VOListener需要将此注解解析为非空；  3、需要配合默认值DefaultValue("UCode:Cuicode")使用； | @Cuicode：商户                                     |
| @DefaultValue          | 否   | 单据默认值注解类（详细用法可以看代码）                       | @DefaultValue：默认值                              |
| @NotEmpty              | 是   | 校验值不能为空，一般为（CharSequence，Collection，Map，Array等） | @NotEmpty：禁止为空                                |
| @NotNull               | 是   | 校验Object不能为空                                           | @NotNull：禁止为空                                 |
| @NotBlank              | 是   | 校验String不能为空                                           | @NotBlank：禁止为空                                |
| @CodeTable             | 是   | 校验字段录入值是否在对应的码表数据范围内                     | @CodeTable：码表                                   |
| @VOField               | 否   | 定义字段值得最大小数位：如"${OPTS.Decimal.Qty}"              | @VOField：小数位                                   |
| @Positive              | 是   | 禁止输入负数及0                                              | @Positive：禁止负数及0                             |
| @PositiveOrZero        | 是   | 禁止输入负数                                                 | @PositiveOrZero：禁止负数                          |
| @Negative              | 是   | 禁止输入正数及0                                              | @@Negative：禁止正数及0                            |
| @NegativeOrZero        | 是   | 禁止输入正数                                                 | @NegativeOrZero：禁止正数                          |
| @PositiveOrNegative    | 是   | 根据方向字段值校验字段值的范围                               | @PositiveOrNegative：根据字段值校验范围            |
| @PositiveOrNegativeAdv | 是   | 根据方向字段值校验字段值的范围（其中多增加了一个判断字段：advrelyCol） | @PositiveOrNegativeAdv：根据字段值校验范围（增强） |
| @NotZero               | 是   | 被注释的元素必须是一个数字且不等于零                         | @NotZero：禁止为0                                  |
| @Min                   | 是   | 被注释的元素必须是一个数字，其值必须大于等于指定的最小值     | @Min：规定最小值                                   |
| @Max                   | 是   | 被注释的元素必须是一个数字，其值必须小于等于指定的最大值     | @Max：规定最大值                                   |
| @DecimalMin            | 是   | 被注释的元素必须是一个数字，其值必须大于等于指定的最小值     | @DecimalMin：规定最小值                            |
| @DecimalMax            | 是   | 被注释的元素必须是一个数字，其值必须小于等于指定的最大值     | @DecimalMax：规定最大值                            |
| @Size                  | 是   | 被注释的元素的大小必须在指定的范围内                         | @Size：规定范围                                    |
| @Digits                | 是   | 被注释的元素必须是一个数字，其值必须在可接受的范围内         | @Digits：规定数值精度                              |
| @Length                | 是   | 被注释的字符串的大小必须在指定的范围内                       | @Length：规定字符串长度                            |
| @Pattern               | 是   | 被注释的元素必须符合指定的正则表达式(eg:网址@Pattern(regexp = "^[\s\S]+\.(com\|cn)[\s\S]*$")) | @Pattern：正则表达式                               |
| @PatternEmail          | 是   | 被注释的元素必须是电子邮箱地址                               | @PatternEmail：邮箱格式校验                        |
| @PatternIdcardno       | 是   | 身份证格式效验                                               | @PatternIdcardno：身份证格式校验                   |
| @PatternMobile         | 是   | 手机号格式校验                                               | @PatternMobile：手机号格式校验                     |
| @Valid                 | 否   | 表示对这个对象属性需要进行验证                               | @Valid：对象校验                                   |
| @ResKey                | 否   | 指定这个对象属性的具体名称ID                                 | @ResKey：国际化资源KEY                             |
| @OneToOne              | 否   | 表示两张表数据是一对一的关系                                 | @OneToOne:一对一关系                               |
| @OneToMany             | 否   | 表示两张表数据是一对多的关系                                 | @OneToMany：一对多关系                             |
| @JoinColumn            | 否   | 两张表关联查询时的关联字段（一般为主键）                     | @JoinColumn：关联字段                              |
| @JoinColumns           | 否   | @JoinColumn的数组表示形式，多个字段关联查询时使用            | @JoinColumns：多个字段关联                         |
| @Initor                | 否   | 用于指定文件类型的数据加载时，数据处理的初始化类             | @Initor：初始化BEAN                                |
| @LangName              | 否   | 规定当前行数据支持多语言，多为码表定义使用                   | @LangName:多语言                                   |
| @XmlAccessorType       | 否   | 用于指定 JAXB 在映射 Java 类与 XML 之间的时候，应该访问哪一种属性 | @XmlAccessorType： JAXB 在映射访问的属性           |
| @XmlRootElement        | 否   | 代表 XML 文档的顶部元素                                      | @XmlRootElement：XML 文档的顶部元素                |
| @XmlType               | 否   | 注解用于控制类或枚举类型的映射，以定义XML的类型信息和结构。通过 @XmlType 注解，您可以指定属性的顺序、命名空间、是否包含子元素等 | @XmlType：控制类或枚举类型的映射                   |
| @Indexed               | 否   | 在Spring框架中，@Indexed注解用于标记类或者方法，表示它们是可被索引的。一般情况下，这个注解用于实现自定义的扫描器或者处理器。通过使用@Indexed注解标记类或者方法，Spring框架会在应用程序启动时将其自动注册到相关的索引中 | @Indexed：可被索引的                               |

1、demo工程

```java
D:.                       
└─help                    
    └─demo                
        ├─service         
        │  │  CcodeStopApplygService.java
        │  │  CcodeStopApplygUIService.java
        │  │  UnitService.java
        │  │  UnitUIService.java
        │  │
        │  └─impl
        │          CcodeStopApplygServiceImpl.java
        │          CcodeStopApplySubmitSVListener.java
        │          UnitServiceImpl.java
        │
        └─vo
                CcodeStopApply.java
                CcodeStopApplyg.java
                Unit.java

```



```java
D:.
├─app
│      AppConfigFT-DEMO.xml
│      ModulesFT-DEMO.xml  
│
├─menu
│      MenuFT-DEMO.xml
│
├─res
│  ├─doctype
│  │  └─voref
│  │          VORefFT-DEMO.xml
│  │
│  └─viewdetail
│          ViewDetailFT-DEMO.xml
│
├─resbundle
│      ResTbl30-FT-DEMO_zh.inf
│      ResUI30-FT-DEMO_zh.inf
│
├─sheet
│  ├─FT-DEMO.CcodeStopApply
│  │      coderef.xml
│  │      copy.xml
│  │      FN-sv.save.xml
│  │      FN-ui.detail.xml
│  │      FN-ui.entry.xml
│  │      limit.xml
│  │      retract.xml
│  │      sheet.xml
│  │      status.xml
│  │      vnbk.xml
│  │
│  └─FT-DEMO.Unit
│          FN-sv.save.xml
│          FN-ui.entry.xml
│          limit.xml
│          sheet.xml
│
├─snada
│      CreateDatabaseFT-DEMO.xml
│
└─ui
    ├─cmdreg
    │  └─FT-DEMO
    │          CcodeStopApplyDetail.xml
    │          CcodeStopApplyEntry.xml
    │          Unit.xml
    │
    └─res
        └─FT-DEMO
                CcodeStopApplyDetail.xml
                CcodeStopApplyEntry.xml
                Unit.xml
                UnitDlg.xml

```



2、菜单如果想要放到大菜单，需要放在menu_00 里面  挂着
appconfig和module一样  能在新的系统里面展示









3、子表过滤 nmpre="filter_cd"

```
<c name="ccode" title="${RES.C}" sqltype="12" aidInputerBtn="true" codedata="#FT-CCODE.Ccode"
               disableed="true"
               aiprops="initParasVales:{status:''}" showname="true" cmparams.sheetcode="FT-DEMO.CcodeStopApply"
               cmparams.opids="R,C" nmpre="filter_cd"/>
```


同时入参还需要一起

```
@SubColumn(table = "ft_cd_xychiqp_stc_g", pJoinColumn = "stcicode", sqlColumn = @SqlColumn(column = "ccode"))
private String ccode;
```

```
@Nmpre.Nmpres({
			//客商业务状态变更申请单明细
			@Nmpre(nmpre = "filter_cd", pJoinColumn = "stcicode", joinColumn = "stcicode", tblname = 
					"ft_cd_xychiqp_stc")})
```

4、@ResKey("FT.title.grp.ccodeStopClg")？？？

5、实际页面打开的时候，不是用单据号打开，使用文件路径打开`CcodeStopApplyEntry.xml`,所以拼接的是这个ui下面的res的FT-DEMO+CcodeStopApplyEntry而不是单据号`sheetcode="FT-DEMO.CcodeStopApply"`

```
http://127.0.0.1:8081/XYFT/uiinvoke/00/zh_CN/theme0/FT-DEMO.CcodeStopApplyEntry.html?_v=1717467812063
```

FT-DEMO.CcodeStopApply 用这个去找就会发现找不到！

```java
snsoft.ui.XMLUIComponentLoader#loadUIData(snsoft.ui.Environment, java.lang.String, int, java.util.Map<java.lang.String,java.lang.String>){
	String pattern = "ui/res/" + muiid.replace('.', '/');
}


```



6、拷贝主表字段：xprops.cpmastercol

```
<c name="sstcicode" sqltype="12" width="${E.G.CW.code}" hidden="true" xprops.cpmastercol=":sstcicode" />

```

7、需要配置穿透定义

8、resbundle 规则   30 50  大的能向小的取，如果同名 取大的--类似重写

![8596449a022064df38e79f411cc684b](E:\Apple\TyporaMD\Img\8596449a022064df38e79f411cc684b.png)

9、打印及附件,除了监听还需要配置此文件

```
cfg/res/doctype/voref/VORefFT-DEMO.xml
```

监听？？FN-sv.save和detail

```
<bean code="FT-CCODE.40" sortidx="40" impl="#FT-PLAT.FTSheetDocSaveSVListener?[{'sheetcode':'${sheetcode}'}]"/>
        <bean code="FT-CCODE.50" sortidx="50" impl="#SN-PLAT.SheetDocWfSVListener?[{'attachCfg':'ft_cd_xychiqp_stc:FT-DEMO.CcodeStopApply#plat_sheetdoc'}]"/>
```

```
<bean code="FT-CCODE.120" sortidx="120" impl="#SN-PLAT.SheetDocSaveUIListener?[{tgtUINames:'ft_cd_xychiqp_stc',sheetCodes:'FT-DEMO.CcodeStopApply'}]"/>
```

```

```

10、码表

码表配置方式

- \# DT_90.YESNO  字典
- \#90.sheet   码表
- Y:是;N：否
- sql:select 

11、核心监听

- 前端 客户端监听-XJS监听
- UI监听-接入层的监听
- 服务监听-DAO监听、存盘监听



客户端：TableListener、DatasetListener、DialogPanelListener

Table Dataset DialogPanel对象  核心前两个

- Table就是界面定义html  定义界面长什么样子----定义样式！！
- Dataset处理数据

```
Table: setToolbarEnable
getColumn
onTableCellClick
onTableRender


Dataset:
setValue getValue


```

最常用的：

- **dataSetFieldPosted**，列提交后，焦点已经定位到新列，数据也提交到了dataset，此时可处理当前列值改变需要级联处理的操作--值改变事件--比如单价*数量
- **dataSetRowNavigated**，行移动完成，此时焦点已在新行上，移动到新行后可以处理一些控制等操作---行移动事件，比如入口列表不同行之间点击的时候，上面的启用按钮根据状态控制显示隐藏
- **dataSetFieldPosting**，列提交前调用，是当前列值改变的情况下，试图移走光标，将数值变化提交给dataset的操作，此时焦点仍未移走，可抛异常中断操作，或列提交前处理布局或赋值操作等----值改变前校验数据！

<img src="E:\Apple\TyporaMD\Img\image-20240606173104409.png" alt="image-20240606173104409" style="zoom: 67%;" />





12、界面属性配置

13、码表清除缓存？？

14、版本修改：

- VO类 添加3个字段  且有2个默认值
- cmd按钮添加
- dialogpanel添加隐藏字段  oldVN

15、@Remote写在UIservice

16、想要同时监听主子表，在tgtUINames配置数组，默认是主表？

```
#new snsoft.ft.help.demo.CcodeStopApplyDetailJSListener({tgtUINames:['ft_cd_xychiqp_stc','ft_cd_xychiqp_stc_g']})
```

比如可以根据dataSet是主子表判断

```
dataSetFieldPosted(DataSet dataSet, DataSetEvent event)

if (dataSet==mainDataset){}
else if (dataSet==gDataset){
```

17、查询时间类似 sqltype采用91   具体到天      表格时间用93  具体到秒

```java
以下列违反【sqltype="93"】规则:
	predate:[FT-LC.LcAppDetail.xml]
```

18、除了币种显示码，其他都显示名字

- 字典一般是`codedata="#DT_FT.LCAppType"`   

- 码表---类似字典，一般是FT_CODE_FCodeV   cdtype=1
- 辅助输入----可以弹框输入查询--一般是FT_CODE_Facode    cdtype=2

19、码表定义实现参考：属地的码表

```
CodeDataFT-CODE-Client.xml 
District 
```

20、码表过滤：需要加上`cmparams="JSONFILTER_FIX:null"`   表示不过滤

aiprops控制初始查询，客商里面默认会有过滤status:70  所以需要额外配置，如果比如公司没有这个，就不需要配置aiprops；只要配置cmparams="JSONFILTER_FIX:null"

```java
<c name="ccode" title="${RES.C}" sqltype="12" aidInputerBtn="true" codedata="#FT-CCODE.Ccode"
               disableed="true"
               aiprops="initParasVales:{status:''}" showname="true" cmparams.sheetcode="FT-DEMO.CcodeStopApply"
               cmparams.opids="R,C" nmpre="filter_cd"/>
```



21、根据业务取对应的数据的服务；用业务编号补数业务员部门的  调用这个服务补数
snsoft.ft.prj.bas.service.PrjClientService#loadBusiness(java.util.Set<java.lang.String>)

22、小数点最小2  最大在VO写上注解`@VOField`

23、获取table信息

```java
@ResKey("FT.title.grp.ccodeStopClg")

 throw new FTPlatException(FTPlatErrConstants.FT_00000089, FTUtils.getTableTitle(main.getClass(), VOUtils.getVOTable(CcodeStopApplyg.class)));
```























### 排查

界面F12查看是否报错--后端报错和前端报错同时查看

查看底层文档定义  文档注释用法

比如某个按钮不显示：

F12 查看是不是就没加载这个html dom 还是加载了hidden而已

还是不行  先逐个注释，缩小排查范围，不确定是不是别的影响到你这个









