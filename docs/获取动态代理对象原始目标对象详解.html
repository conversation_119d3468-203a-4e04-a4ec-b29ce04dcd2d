<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>获取动态代理对象原始目标对象详解</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        .section {
            margin: 25px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .example {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
        }
        .concept {
            background-color: #f0f9ff;
            border-left: 4px solid #2196f3;
        }
        .memory {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        .diagram {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            text-align: center;
        }
        strong {
            color: #e74c3c;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 5px;
            border-radius: 3px;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin: 8px 0;
        }
        code {
            background-color: #f4f4f4;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .tip {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>获取动态代理对象原始目标对象详解</h1>

        <h2>========== 生活化例子 ==========</h2>
        <div class="section example">
            <h3>🎭 演员与替身的关系</h3>
            <p><strong>想象电影拍摄的场景：</strong></p>
            <ul>
                <li><strong>原始目标对象</strong>：就像真正的演员（比如成龙）</li>
                <li><strong>代理对象</strong>：就像演员的替身，负责危险动作</li>
                <li><strong>获取原始对象</strong>：就像从替身身上找到真正的演员信息</li>
            </ul>
            
            <h3>🏢 公司与代理人的关系</h3>
            <p><strong>商务场景的理解：</strong></p>
            <ul>
                <li><strong>原始目标对象</strong>：真正的公司老板</li>
                <li><strong>代理对象</strong>：公司的业务代表或经理</li>
                <li><strong>获取原始对象</strong>：通过代理人找到真正的决策者</li>
            </ul>

            <h3>📦 快递与代收点的关系</h3>
            <p><strong>日常生活场景：</strong></p>
            <ul>
                <li><strong>原始目标对象</strong>：你的真实收货地址</li>
                <li><strong>代理对象</strong>：代收点或快递柜</li>
                <li><strong>获取原始对象</strong>：通过代收点信息找到真正的收货人</li>
            </ul>
        </div>

        <h2>========== 概念讲解 ==========</h2>
        <div class="section concept">
            <h3>🔍 什么是动态代理对象的原始目标对象</h3>
            <p><strong>核心概念：</strong>在Java的动态代理机制中，<span class="highlight">代理对象包装了真正的业务对象</span>，我们需要通过特定方法获取被包装的原始对象。</p>
            
            <h3>🎯 两种主要的动态代理类型</h3>
            <ol>
                <li><strong>JDK动态代理</strong>：
                    <ul>
                        <li>基于接口实现</li>
                        <li>代理对象内部有一个 <code>h</code> 变量（InvocationHandler）</li>
                        <li>适用于有接口的类</li>
                    </ul>
                </li>
                <li><strong>CGLIB动态代理</strong>：
                    <ul>
                        <li>基于继承实现</li>
                        <li>代理对象内部有一个 <code>CGLIB$CALLBACK_0</code> 变量</li>
                        <li>适用于没有接口的类</li>
                    </ul>
                </li>
            </ol>

            <h3>🌟 Spring环境 vs 非Spring环境</h3>
            <div class="warning">
                <p><strong>⚠️ 重要区别：</strong></p>
                <ul>
                    <li><strong>Spring AOP环境</strong>：可以使用Spring提供的工具类和接口</li>
                    <li><strong>非Spring环境</strong>：需要直接操作JDK或CGLIB的底层API</li>
                </ul>
            </div>

            <h3>📋 SpringBoot版本差异</h3>
            <ul>
                <li><strong>SpringBoot 1.x</strong>：默认使用JDK动态代理</li>
                <li><strong>SpringBoot 2.x</strong>：默认使用CGLIB动态代理</li>
                <li><strong>原因</strong>：CGLIB更灵活，支持直接注入实现类</li>
            </ul>
        </div>

        <h2>========== 简单记法 ==========</h2>
        <div class="section memory">
            <h3>🧠 记忆口诀</h3>
            <div style="background: #fff; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <p><strong>"Spring有接口，非Spring看类型，JDK找h，CGLIB找回调"</strong></p>
                <ul>
                    <li><strong>Spring有接口</strong>：Spring环境可以用Advised接口</li>
                    <li><strong>非Spring看类型</strong>：非Spring环境要区分JDK和CGLIB</li>
                    <li><strong>JDK找h</strong>：JDK动态代理找h变量</li>
                    <li><strong>CGLIB找回调</strong>：CGLIB找CALLBACK_0变量</li>
                </ul>
            </div>

            <h3>📝 方法分类记忆</h3>
            <ol>
                <li><strong>Spring AOP环境（3种方法）</strong>：
                    <ul>
                        <li><strong>反射工具类</strong>：自己写AopTargetUtils</li>
                        <li><strong>Advised接口</strong>：直接转换调用getTargetSource()</li>
                        <li><strong>Spring工具类</strong>：AopProxyUtils.getSingletonTarget()</li>
                    </ul>
                </li>
                <li><strong>非Spring环境（2种类型）</strong>：
                    <ul>
                        <li><strong>JDK代理</strong>：Proxy.getInvocationHandler() + 反射</li>
                        <li><strong>CGLIB代理</strong>：反射获取CGLIB$CALLBACK_0</li>
                    </ul>
                </li>
            </ol>

            <h3>⚡ 实战记忆要点</h3>
            <ul>
                <li><strong>判断代理类型</strong>：先用AopUtils判断是否Spring代理</li>
                <li><strong>字段名记忆</strong>：JDK用"h"，CGLIB用"CGLIB$CALLBACK_0"</li>
                <li><strong>反射套路</strong>：getDeclaredField → setAccessible → get</li>
                <li><strong>Spring优先</strong>：Spring环境优先用Advised接口</li>
            </ul>
        </div>

        <h2>========== 图示 ==========</h2>
        <div class="section diagram">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1600 1000">
                <!-- 背景 -->
                <rect width="1600" height="1000" fill="#f8f9fa"/>
                
                <!-- 标题 -->
                <text x="800" y="50" text-anchor="middle" font-size="32" font-weight="bold" fill="#2c3e50">
                    获取动态代理对象原始目标对象方法图解
                </text>
                
                <!-- Spring AOP 环境 -->
                <g id="spring-aop">
                    <rect x="50" y="100" width="700" height="400" fill="#3498db" opacity="0.1" rx="15" stroke="#3498db" stroke-width="2"/>
                    <text x="400" y="130" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">
                        Spring AOP 环境
                    </text>
                    
                    <!-- 方法1：反射工具类 -->
                    <rect x="80" y="160" width="200" height="100" fill="#e74c3c" opacity="0.8" rx="10"/>
                    <text x="180" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="white">
                        方法1：反射工具类
                    </text>
                    <text x="180" y="205" text-anchor="middle" font-size="12" fill="white">
                        AopTargetUtils
                    </text>
                    <text x="180" y="225" text-anchor="middle" font-size="12" fill="white">
                        自己实现
                    </text>
                    <text x="180" y="245" text-anchor="middle" font-size="12" fill="white">
                        支持JDK+CGLIB
                    </text>
                    
                    <!-- 方法2：Advised接口 -->
                    <rect x="300" y="160" width="200" height="100" fill="#27ae60" opacity="0.8" rx="10"/>
                    <text x="400" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="white">
                        方法2：Advised接口
                    </text>
                    <text x="400" y="205" text-anchor="middle" font-size="12" fill="white">
                        直接转换
                    </text>
                    <text x="400" y="225" text-anchor="middle" font-size="12" fill="white">
                        最简单
                    </text>
                    <text x="400" y="245" text-anchor="middle" font-size="12" fill="white">
                        Spring推荐
                    </text>
                    
                    <!-- 方法3：Spring工具类 -->
                    <rect x="520" y="160" width="200" height="100" fill="#f39c12" opacity="0.8" rx="10"/>
                    <text x="620" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="white">
                        方法3：Spring工具类
                    </text>
                    <text x="620" y="205" text-anchor="middle" font-size="12" fill="white">
                        AopProxyUtils
                    </text>
                    <text x="620" y="225" text-anchor="middle" font-size="12" fill="white">
                        官方提供
                    </text>
                    <text x="620" y="245" text-anchor="middle" font-size="12" fill="white">
                        内部用Advised
                    </text>
                    
                    <!-- Spring代理对象示例 -->
                    <rect x="80" y="300" width="640" height="180" fill="#ecf0f1" rx="10"/>
                    <text x="400" y="330" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Spring代理对象结构
                    </text>
                    
                    <!-- JDK代理 -->
                    <rect x="100" y="350" width="280" height="110" fill="#9b59b6" opacity="0.2" rx="5"/>
                    <text x="240" y="375" text-anchor="middle" font-size="14" font-weight="bold" fill="#8e44ad">
                        JDK动态代理
                    </text>
                    <text x="120" y="395" font-size="12" fill="#2c3e50">• 基于接口</text>
                    <text x="120" y="415" font-size="12" fill="#2c3e50">• 内部有h变量</text>
                    <text x="120" y="435" font-size="12" fill="#2c3e50">• 实现Advised接口</text>
                    
                    <!-- CGLIB代理 -->
                    <rect x="400" y="350" width="280" height="110" fill="#e67e22" opacity="0.2" rx="5"/>
                    <text x="540" y="375" text-anchor="middle" font-size="14" font-weight="bold" fill="#d35400">
                        CGLIB动态代理
                    </text>
                    <text x="420" y="395" font-size="12" fill="#2c3e50">• 基于继承</text>
                    <text x="420" y="415" font-size="12" fill="#2c3e50">• 内部有CALLBACK_0</text>
                    <text x="420" y="435" font-size="12" fill="#2c3e50">• 实现Advised接口</text>
                </g>
                
                <!-- 非Spring环境 -->
                <g id="non-spring">
                    <rect x="850" y="100" width="700" height="400" fill="#e74c3c" opacity="0.1" rx="15" stroke="#e74c3c" stroke-width="2"/>
                    <text x="1200" y="130" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">
                        非Spring环境
                    </text>
                    
                    <!-- JDK动态代理方法 -->
                    <rect x="880" y="160" width="300" height="100" fill="#3498db" opacity="0.8" rx="10"/>
                    <text x="1030" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="white">
                        JDK动态代理
                    </text>
                    <text x="1030" y="205" text-anchor="middle" font-size="12" fill="white">
                        Proxy.getInvocationHandler()
                    </text>
                    <text x="1030" y="225" text-anchor="middle" font-size="12" fill="white">
                        + 反射获取目标对象
                    </text>
                    <text x="1030" y="245" text-anchor="middle" font-size="12" fill="white">
                        适用于有接口的类
                    </text>
                    
                    <!-- CGLIB动态代理方法 -->
                    <rect x="1200" y="160" width="300" height="100" fill="#9b59b6" opacity="0.8" rx="10"/>
                    <text x="1350" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="white">
                        CGLIB动态代理
                    </text>
                    <text x="1350" y="205" text-anchor="middle" font-size="12" fill="white">
                        反射获取CGLIB$CALLBACK_0
                    </text>
                    <text x="1350" y="225" text-anchor="middle" font-size="12" fill="white">
                        + 反射获取目标对象
                    </text>
                    <text x="1350" y="245" text-anchor="middle" font-size="12" fill="white">
                        适用于无接口的类
                    </text>
                    
                    <!-- 非Spring代理对象示例 -->
                    <rect x="880" y="300" width="620" height="180" fill="#ecf0f1" rx="10"/>
                    <text x="1190" y="330" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        非Spring代理对象结构
                    </text>
                    
                    <!-- 原生JDK -->
                    <rect x="900" y="350" width="280" height="110" fill="#3498db" opacity="0.2" rx="5"/>
                    <text x="1040" y="375" text-anchor="middle" font-size="14" font-weight="bold" fill="#2980b9">
                        原生JDK代理
                    </text>
                    <text x="920" y="395" font-size="12" fill="#2c3e50">• 只有h变量</text>
                    <text x="920" y="415" font-size="12" fill="#2c3e50">• 不实现Advised</text>
                    <text x="920" y="435" font-size="12" fill="#2c3e50">• 需要手动反射</text>
                    
                    <!-- 原生CGLIB -->
                    <rect x="1200" y="350" width="280" height="110" fill="#9b59b6" opacity="0.2" rx="5"/>
                    <text x="1340" y="375" text-anchor="middle" font-size="14" font-weight="bold" fill="#8e44ad">
                        原生CGLIB代理
                    </text>
                    <text x="1220" y="395" font-size="12" fill="#2c3e50">• 只有CALLBACK_0</text>
                    <text x="1220" y="415" font-size="12" fill="#2c3e50">• 不实现Advised</text>
                    <text x="1220" y="435" font-size="12" fill="#2c3e50">• 需要手动反射</text>
                </g>
                
                <!-- 流程步骤 -->
                <g id="process-steps">
                    <text x="800" y="580" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">
                        获取原始目标对象的通用流程
                    </text>
                    
                    <!-- 步骤1 -->
                    <circle cx="200" cy="650" r="25" fill="#3498db"/>
                    <text x="200" y="658" text-anchor="middle" font-size="16" font-weight="bold" fill="white">1</text>
                    <text x="200" y="690" text-anchor="middle" font-size="14" fill="#2c3e50">判断环境</text>
                    <text x="200" y="710" text-anchor="middle" font-size="12" fill="#7f8c8d">Spring vs 非Spring</text>
                    
                    <!-- 箭头1 -->
                    <path d="M 250 650 L 350 650" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                    
                    <!-- 步骤2 -->
                    <circle cx="400" cy="650" r="25" fill="#e74c3c"/>
                    <text x="400" y="658" text-anchor="middle" font-size="16" font-weight="bold" fill="white">2</text>
                    <text x="400" y="690" text-anchor="middle" font-size="14" fill="#2c3e50">判断代理类型</text>
                    <text x="400" y="710" text-anchor="middle" font-size="12" fill="#7f8c8d">JDK vs CGLIB</text>
                    
                    <!-- 箭头2 -->
                    <path d="M 450 650 L 550 650" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                    
                    <!-- 步骤3 -->
                    <circle cx="600" cy="650" r="25" fill="#f39c12"/>
                    <text x="600" y="658" text-anchor="middle" font-size="16" font-weight="bold" fill="white">3</text>
                    <text x="600" y="690" text-anchor="middle" font-size="14" fill="#2c3e50">选择方法</text>
                    <text x="600" y="710" text-anchor="middle" font-size="12" fill="#7f8c8d">接口 vs 反射</text>
                    
                    <!-- 箭头3 -->
                    <path d="M 650 650 L 750 650" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                    
                    <!-- 步骤4 -->
                    <circle cx="800" cy="650" r="25" fill="#27ae60"/>
                    <text x="800" y="658" text-anchor="middle" font-size="16" font-weight="bold" fill="white">4</text>
                    <text x="800" y="690" text-anchor="middle" font-size="14" fill="#2c3e50">获取目标对象</text>
                    <text x="800" y="710" text-anchor="middle" font-size="12" fill="#7f8c8d">返回原始对象</text>
                </g>
                
                <!-- 箭头标记定义 -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                    </marker>
                </defs>
                
                <!-- 关键提示 -->
                <g id="key-tips">
                    <rect x="100" y="800" width="1400" height="150" fill="#2c3e50" opacity="0.05" rx="10"/>
                    <text x="800" y="830" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">
                        🔑 关键提示
                    </text>
                    
                    <text x="120" y="860" font-size="14" fill="#2c3e50">
                        ✅ Spring环境优先使用Advised接口，简单直接
                    </text>
                    <text x="120" y="885" font-size="14" fill="#2c3e50">
                        ✅ 非Spring环境需要区分JDK和CGLIB，使用不同的反射方法
                    </text>
                    <text x="120" y="910" font-size="14" fill="#2c3e50">
                        ✅ JDK代理找"h"变量，CGLIB代理找"CGLIB$CALLBACK_0"变量
                    </text>
                    <text x="120" y="935" font-size="14" fill="#2c3e50">
                        ✅ 反射操作记住三步骤：getDeclaredField → setAccessible → get
                    </text>
                </g>
            </svg>
        </div>

        <!-- 代码示例区域 -->
        <div class="section">
            <h2>========== 实战代码示例 ==========</h2>

            <h3>🌟 Spring AOP环境 - Advised接口方法</h3>
            <div class="code-block">
                <strong>最推荐的方法：</strong>
                <pre><code>// 直接转换为Advised接口获取目标对象
Advised advised = (Advised) proxyObject;
Object target = advised.getTargetSource().getTarget();</code></pre>
            </div>

            <h3>🔧 Spring AOP环境 - 反射工具类方法</h3>
            <div class="code-block">
                <strong>通用工具类实现：</strong>
                <pre><code>public class AopTargetUtils {
    public static Object getTarget(Object proxy) throws Exception {
        if(!AopUtils.isAopProxy(proxy)) {
            return proxy; // 不是代理对象直接返回
        }

        if(AopUtils.isJdkDynamicProxy(proxy)) {
            // JDK动态代理：获取h变量
            Field h = proxy.getClass().getSuperclass().getDeclaredField("h");
            h.setAccessible(true);
            AopProxy aopProxy = (AopProxy) h.get(proxy);
            Field advised = aopProxy.getClass().getDeclaredField("target");
            advised.setAccessible(true);
            return ((AdvisedSupport)advised.get(aopProxy)).getTargetSource().getTarget();
        } else {
            // CGLIB动态代理：获取CGLIB$CALLBACK_0变量
            Field h = proxy.getClass().getDeclaredField("CGLIB$CALLBACK_0");
            h.setAccessible(true);
            Object dynamicAdvisedInterceptor = h.get(proxy);
            Field advised = dynamicAdvisedInterceptor.getClass().getDeclaredField("advised");
            advised.setAccessible(true);
            return ((AdvisedSupport)advised.get(dynamicAdvisedInterceptor)).getTargetSource().getTarget();
        }
    }
}</code></pre>
            </div>

            <h3>⚡ 非Spring环境 - JDK动态代理</h3>
            <div class="code-block">
                <strong>原生JDK代理获取目标对象：</strong>
                <pre><code>// 1. 获取InvocationHandler
MyInvocationHandler handler = (MyInvocationHandler) Proxy.getInvocationHandler(proxyObject);

// 2. 反射获取目标对象
Field field = MyInvocationHandler.class.getDeclaredField("origBean");
field.setAccessible(true);
Object target = field.get(handler);</code></pre>
            </div>

            <h3>🚀 非Spring环境 - CGLIB动态代理</h3>
            <div class="code-block">
                <strong>原生CGLIB代理获取目标对象：</strong>
                <pre><code>// 1. 获取CGLIB$CALLBACK_0变量
Field callbackField = proxy.getClass().getDeclaredField("CGLIB$CALLBACK_0");
callbackField.setAccessible(true);
MyMethodInterceptor interceptor = (MyMethodInterceptor) callbackField.get(proxy);

// 2. 反射获取目标对象
Field targetField = MyMethodInterceptor.class.getDeclaredField("origBean");
targetField.setAccessible(true);
Object target = targetField.get(interceptor);</code></pre>
            </div>

            <div class="tip">
                <p><strong>💡 实战建议：</strong></p>
                <ul>
                    <li>Spring环境优先使用 <code>Advised</code> 接口，代码最简洁</li>
                    <li>需要通用性时使用反射工具类，支持多种代理类型</li>
                    <li>非Spring环境必须手动判断代理类型并使用对应方法</li>
                    <li>反射操作要注意异常处理和访问权限设置</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('获取动态代理对象原始目标对象详解页面已加载完成！');
            
            // 添加交互效果
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
                    this.style.transition = 'all 0.3s ease';
                });
                
                section.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
