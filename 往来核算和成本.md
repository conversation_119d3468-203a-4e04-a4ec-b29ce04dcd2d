***系统往来费用类型***

这个是最基本的，主要是类型--大类

***商户往来费用类型***

实际系统中使用的，基于系统往来费用类型生成的，主要满足于多个商户情况下。

***系统往来费用类型核算方案***

主要定义基本的字段核算方案，基于费用类型定义具体的核算字段

***系统往来费用类型显示方案***

显示方案基于核算方案配置具体的关联单据（使用范围）；添加的时候会选择核算项，所选的范围就是***系统往来费用类型核算方案***里面的字段





## 成本

> TStockSalOutVMarkServiceImpl
> snsoft.ft.tsi.pi.service.impl.TStockPurInVMarkServiceImpl

正常跨部门库存转移单会推送成本，成本会生成凭证标记表，构建的时候重写的queryDataSource逻辑如下：

默认传入的是`VMarkCommParam<VMarkSrcI> param`    实际是CostRstSrc类型的，

根据这个对象的`csgicode`获取库存明细对象，然后库存对象里面会记录上游实际业务单据对象；

也就是成本对象-->库存对象-->实际业务单据

特别注意实际的srcgicode记录的是成本对象的costicode---成本明细内码

```
// 获取库存明细信息
Set<String> csgicodes = costRstSrcList.stream().map(CostRstSrc::getCsgicode).collect(Collectors.toSet());
List<TStockData> tStockDataList = DAO.newInstance(TStockData.class).queryListByID(csgicodes);
if(CollectionUtils.isEmpty(tStockDataList)) {
    return null;
}
// 获取业务单据信息和需要生成制凭证标记表的业务单据内码
Set<String> upicodes = tStockDataList.stream().map(TStockData::getUpicode).collect(Collectors.toSet());
BusiAccessService<FTSheetVO> accessService = SheetUtils.getAccessService(sheetcode);
List<FTSheetVO> busiSheetList = accessService.queryByInnerCodes(upicodes, false);
Map<String, FTSheetVO> busiSheetMap = busiSheetList.stream().collect(Collectors.toMap(b -> b.getInnercode(), b -> b, (v1, v2) -> v1));
Set<String> genVKBusiSheetIds = busiSheetList.stream().filter(b -> BusiConst.STATUS_APPR_PASS.equals(b.getStatus()))
        .map(b -> b.getInnercode()).collect(Collectors.toSet());
// 获取成本对象信息
Map<String, TStockData> tStockDataMap = tStockDataList.stream().filter(s -> genVKBusiSheetIds.contains(s.getUpicode()))
        .collect(Collectors.toMap(s -> s.getTspdicode(), s -> s, (v1, v2) -> v1));
if(CollectionUtils.isEmpty(tStockDataMap.keySet())) {
    return null;
}
```





![image-20250327101737902](E:\Apple\TyporaMD\Img\image-20250327101737902.png)

