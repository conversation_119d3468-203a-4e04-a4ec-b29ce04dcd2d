## 销售合同-->发货单-->出仓单

```java
境内销售合同选择收付款方式LC/DP/DA/
境外不限制
```

发货单不要选择我司自提，同时选择磅差处理为否，手动去出仓回单拷贝新建生成

> snsoft.ft.ssp.utils.SalShipUtils#genSSDataByPicker
> 生成逻辑。

<img src="E:\Apple\TyporaMD\Img\e71c5493940f043ecbcedc7087f920b9.jpg" alt="e71c5493940f043ecbcedc7087f920b9" style="zoom:50%;" />

<img src="E:\Apple\TyporaMD\Img\6a14f68562a2676c328f0156f03523b5.jpg" alt="6a14f68562a2676c328f0156f03523b5" style="zoom: 50%;" />



## 做单

境外发货单---3个是否磅差为否---直接会生成生效的出仓回单

境内发货单，第一个为是，第2、3为否，同时不要弄我司提货？





## 库存明细理解

一开始库存是400，所以可拣配是400

然后生成发货单拣配了20，所以剩余可拣配是380

接着生成出仓回单，只出仓10，所以可拣配来说还是380，但是余额变成了390，之前都是400，所以余额是要出仓回单以后才会变化？

![image-20250222101855448](E:\Apple\TyporaMD\Img\image-20250222101855448.png)







## 额度上下游

![8d1912e9e0d109f504d207c192c0b26b](E:\Apple\TyporaMD\Img\8d1912e9e0d109f504d207c192c0b26b.jpg)





## 承兑赎单

> **赎单确认和承兑确认都会生成付款申请单：**
>
> 如果是非LC的，此时没有支付记录，等待ATS付款回传以后生成对应的支付记录回写到对应的付款申请单
>
> 如果是LC的直接会生成一条虚拟的支付记录（挂在付款申请单），等待ATS付款回传以后生成对应的支付记录挂到承兑赎单

<img src="E:\Apple\TyporaMD\Img\24566e67529a5db7dae95f7c493d6e8b.jpg" alt="24566e67529a5db7dae95f7c493d6e8b" style="zoom: 67%;" />
