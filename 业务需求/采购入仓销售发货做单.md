![export](E:\Apple\TyporaMD\Img\export.png)

```java
select *
from ft_tsp_dt
where isbonded = 'Y'                                   --是否保税货物（境外）
  and tradetype = '40'                                 --贸易方式
  and trademode not in ('02', '03')                    --贸易形态
  and stockdate <= to_date('2024-11-18', 'yyyy-mm-dd') --入出仓日期
  and sublib in ('SP', 'KH', 'QH')                     --商品子库
  and bcode = 'B00007'
  and grpcorpbcode = 'C00006'                          --分组公司
  and gcode in ('G240724002')
  and (pickprjicode) in --拣配业务内码
      (select prjicode
       from ft_prj_prj
       where psmode = '30'    --购销方式
         and isdatasyn = '40' --数据同步
         and isshare = 'N'    --是否共享
         and iscoop = 'N')                             --是否合作
  and cuicode = 'C000000001'
  and balflag in (2, 3)                                --余额标记
  and dataflag = 1                                     --明细类型
  and redflag = 0                                      --红冲标记
order by stockdate
```



# 采购合同->采购到货-->采购小提单（境外需要?）->采购入仓

> 最主要的就是公司、部门、商品那些要对应上
>
> 采购入仓单生效以后就会生成库存，在库存实时查询可以查到
>
> 另外公司和部门要建立关系才可以，在库存/应收应付账期会校验

## 境外案例

### 采购端

采购合同：CG12177

- 入口直接新建：进口敞口 采购通用贸易业务

- 需要涉及策略，只需要先在父策略维护好商品类目，子策略拷贝新建父策略，然后选择对应的商品类目即可
- 无额度占用-->信用到期类型：固定日期
- 能多选的就全选：交货方式，物流服务类型等
- 如果负数的话，增值税率选择0或者0点几

采购到货：CG12177-CQP1

- 磅差选择否
- 境外不能直接入仓？   要选择境外采购小提单，然后生效，然后基于小提单进行下级操作生成采购入仓
- 如果采购入仓单提交生效不了，直接ctrl+f1 强制改为70也能当做库存使用？



小提单号：CG12177-CQP1

- 货物流向：选择一般贸易进口

### 销售端

> 新建销售合同：出口敞口销售合同

销售合同：CG12177-CQP11

销售发货单：FH-241217012，**选择磅差为否才能自动生成出仓单（境内的又不能？）**

销售出仓单：HD-241217001





# 境内

基本跟境外一致，

采购入仓单：CG1216CQP

发货单：好像要选择磅差为是，手动去出仓

> 新建销售合同：内贸敞口销售合同