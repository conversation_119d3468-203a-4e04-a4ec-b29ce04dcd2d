# 议付交单往来、预收账数据

> 如果议付交单是lc的100，会生成应收信用证费用类型往来（应收）    
> 然后有追放款银行融资90，生成90的短期借款费用类型往来（应付）
> 那我还款的时候生成的往来应该是100的应收信用证费用类型（实收）+90的短期借款费用类型往来（实付）
>
>
> 客户还款手续费--银行手续费
>
> 还贷手续费--融资手续费
>
> 还贷利息--融资利息

> **交单承兑后会生成预收账款凭证，是通过先自动生成认领单，基于认领单生成的凭证**

> 承兑赎单：  如果是LC的，生成的支付记录挂在承兑赎单单据；如果非LC，会生成在付款申请单里面的支付记录
>
> 赎单融资：主动还款（直接ATS发起），非主动还款（走赎单融资申请单）

```java
select * from ft_fund_rela_bmp where SRCICODE='674ab53979d32866e37cc6a5';

-- 交单数据
select *
from ft_docu_dlyd
where dlydicode ='67500478353b250ea0393dde';



--往来数据
select dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
     , vdc as 应实, status, fcode, scerate, suerate
     , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode
from ft_lrp_lrp
where srcsheetcode like '%FT-DOCU.DocuDelivery%'  
    and srcicode = '67500478353b250ea0393dde' order by lrpicode asc;


-- x:核销   o:原始   r：红冲
    
--往来余额表
select *from ft_lrp_lrp_cb where lrpicodex  in (select lrpicodex
                                                from ft_lrp_lrp
                                                where srcsheetcode like '%FT-DOCU.DocuDelivery%'
                                                  and srcicode = '67500478353b250ea0393dde'  )  and fcy>0  order by 
                                                  lrpicodex asc;

-- 查询这条余额有多少个地方使用
select *from FT_LRP_LRP where LRPICODEX='6749220eccf70d14a23e07b0';


-- 预收账
SELECT srccode, redflag, dc AS 收付, fcy, recfcode
     , recfcy, scy, zcny, zusd, fserate
     , recfserate, scerate, suerate, bankfcy, bankscy
     , bankzcny, bankzusd
FROM ft_rec_rclm
WHERE srcsheetcode LIKE '%FT-DOCU.DocuDelivery%'
    AND srcicode = '67500478353b250ea0393dde';



--交单重复做单删除步骤： 删除审批流数据，交单承兑表数据，预收账数据，往来数据
-- 1、 删除审批数据
delete
from  APPRTASKLIST
where taskid in (select TASKID from APPRDATA where INNERCODE = '673ea4f3d83d241d56bc7781');
delete
from  APPRDATA
where INNERCODE = '673ea4f3d83d241d56bc7781';

-- 2、交单承兑表数据
delete  from ft_docu_dlydc where dlydicode='674967290f6aa1189a596373';

-- 3、预收账数据
delete  from  ft_rec_rclm where srcicode='674967290f6aa1189a596373' and  srcsheetcode='FT-DOCU.DocuDelivery';

-- 4、往来数据
delete  from  ft_lrp_lrp  where DLYDICODE='674967290f6aa1189a596373'  and  srcsheetcode='FT-DOCU.DocuDelivery';

```

> 交单融资

```java
-- 交单往来-DP/DA/LC、
-- 融资放款、还款-有追、无追
select* from ft_loan_dlydfa where dlydfaicode='67500880353b250ea0394008';
-- 放款往来查询
select dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
     , vdc as 应实, status, fcode, scerate, suerate
     , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode
from ft_lrp_lrp
where srcsheetcode  like '%FT-LOAN.DocuDeliveryFinaLI%'
  and srcicode in  (select dlydfaliicode from ft_loan_dlydfali  where
    dlydfaicode='67500880353b250ea0394008' ) order by lrpicode asc;


--往来余额表
select *from ft_lrp_lrp_cb where lrpicodex  in (select lrpicodex
                                                from ft_lrp_lrp
                                                where srcsheetcode like '%FT-LOAN.DocuDeliveryFinaLI%'
                                                  and srcicode in  (select dlydfaliicode from ft_loan_dlydfali  where
                                                    dlydfaicode='67500880353b250ea0394008' )  ) order by lrpicodex asc;


-- 交单往来
select *
from ft_lrp_lrp_cb
where lrpicodex in (select lrpicodex
                    from ft_lrp_lrp
                    where srcsheetcode like '%FT-DOCU.DocuDelivery%'
                      and srcicode = '67500478353b250ea0393dde') and fcy > 0 order by lrpicodex asc;



-- 放款产生的银行费用
select dlydicode,paymode, fcy, srccode, srcsheetcode, status, fcode, scerate, suerate
                , fserate, scy, zcny, zusd from ft_fee_bfee  where srcsheetcode like '%DocuDeliveryFinaLI%'
                            and srcicode   in  (select dlydfaliicode from ft_loan_dlydfali  where
                                                    dlydfaicode='67500880353b250ea0394008' and redflag=0);



-- 放款删除重复推送步骤
delete from ft_fund_vmark where srcsheetcode='FT-LOAN.DocuDeliveryFinaLI' and srcicode in(select dlydfaliicode from 
ft_loan_dlydfali  where
    dlydfaicode='67500880353b250ea0394008');

delete from
    ft_lrp_lrp
WHERE srcsheetcode  like '%FT-LOAN.DocuDeliveryFinaLI%' and srcicode in  (select dlydfaliicode from ft_loan_dlydfali 
                                                                                               where
    dlydfaicode='67500880353b250ea0394008');

delete from ft_loan_dlydfali  where dlydfaicode='67500880353b250ea0394008';

update ft_lrp_lrp_cb set fcyed=0,FCYING=fcy where lrpicodex  in (select lrpicodex
                                                                 from ft_lrp_lrp
                                                                 where srcsheetcode like '%FT-DOCU.DocuDelivery%'
                                                                   and srcicode = '674442b5a5ab043d91cf816e'  )  and fcy>0


select * from ft_lrp_lrp_ct where LRPICODEX in (select lrpicodex
                                                from ft_lrp_lrp
                                                where srcsheetcode like '%FT-DOCU.DocuDelivery%'
                                                  and srcicode = '674442b5a5ab043d91cf816e'  ) and tgttbl='ft_lrp_lrp_cb';




----------------------------------------还款-------------------------------------------------------------


-- 还款往来查询
SELECT DLYDICODE,paymode, fcy, srccode, srcsheetcode, dc AS 收付
     , vdc AS 应实, status, fcode, scerate, suerate
     , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode
FROM ft_lrp_lrp
WHERE srcsheetcode  like '%FT-LOAN.DocuDeliveryFinaRI%' 
and srcicode   in  (select dlydfariicode from ft_loan_dlydfari  where
    dlydfaicode='67500880353b250ea0394008' ) order by LRPICODE asc;


--往来余额表
select *from ft_lrp_lrp_cb where lrpicodex  in (select lrpicodex
                                                from ft_lrp_lrp
                                                where srcsheetcode like '%FT-LOAN.DocuDeliveryFinaRI%'
                                                  and srcicode   in  (select dlydfariicode from ft_loan_dlydfari  where
                                                    dlydfaicode='67500880353b250ea0394008' )  ) order by 
                                                                                                          lrpicodex asc;


-- 还款产生的银行费用
select *from FT_FEE_BFEE  where SRCSHEETCODE like '%DocuDeliveryFinaRI%'
                            and SRCICODE='67500880353b250ea0394008';




-- 还款信息
select *
from ft_loan_dlydfari where  dlydfaicode='67500880353b250ea0394008';

-- 放款、还款产生凭证标记表
select  *from  FT_FUND_VMARK where SRCSHEETCODE like '%DocuDeliveryFinaRI%';



-- 还款重复推送步骤：
delete from ft_fund_vmark where  srcsheetcode='FT-LOAN.DocuDeliveryFinaRI' and  srcicode in(select dlydfariicode from ft_loan_dlydfari  where
    DLYDFAICODE='67500880353b250ea0394008');

delete from
    ft_lrp_lrp
WHERE srcsheetcode  like '%FT-LOAN.DocuDeliveryFinaRI%' and srcicode in  (select dlydfariicode from ft_loan_dlydfari
                                                                          where
                                                                              dlydfaicode='67500880353b250ea0394008');

delete from ft_loan_dlydfari  where DLYDFAICODE='67500880353b250ea0394008';





-- 更新信用证信息
update ft_docu_dlydg_cb set  lccode='LC241112009',lcregicode='6733392a79a29a175a4ffc7b' where PAYMODE in ('0060','0065','0070','0075');
update ft_docu_dlydg_cb set  lccode=null,lcregicode=null where PAYMODE not  in ('0060','0065','0070','0075');
update ft_docu_dlyd set  LCCODE='LC241112009',LCREGICODE='6733392a79a29a175a4ffc7b' ;
update ft_loan_dlydfa  set bcode='B00153',corpbcode='CCW00000442';
-- sit:B00153  30034071   CCW00000442 301
```



## 往来数据

执行提交到待审--负数往来，状态为10草拟

然后执行完交单确认--DP正数往来，DP负数往来状态改为70生效、承兑确认--远期LC往来；

然后再执行承兑确认取消--红冲远期LC往来、交单确认取消--红冲DP正数往来，同时原来的负数往来状态改为10草拟

>
> 红冲往来注意点：
>
> ```java
> //承兑取消红冲： 过滤出【议付交单内码不为空/交单金额大于0/并且是远期信用证类型】的往来进行红冲（远期信用证会先进行承兑取消，再次进行交单取消，避免重复执行）
> 
> //交单取消红冲：过滤出【议付交单内码不为空/交单金额大于0/并且不是！远期信用证类型】的往来进行红冲（远期信用证会先进行承兑取消，再次进行交单取消，避免重复执行）
> ```

<img src="E:\Apple\TyporaMD\Img\image-20241121164616976.png" alt="image-20241121164616976"  />

```java
LRPICODE,LRPICODEX,LRPICODEO,LRPICODER
LRPICODE当前最新单据的内码，LRPICODEX  上级原始内码
```



## 预收账数据

只有LC（不限制即期远期）才生成预收账往来，生成时机：即期LC交单确认时生成，远期LC承兑确认生成

红冲预收账往来，远期LC在承兑确认取消（通过议付交单内码），即期LC在交单确认取消（通过交单确认内码），如果红冲过了不会重复红冲，所以不用担心重复红冲问题，并且即期LC和远期LC不会同时存在，所以也不用担心

<img src="E:\Apple\TyporaMD\Img\image-20241121162718405.png" alt="image-20241121162718405" style="zoom: 67%;" />

# 其他

> 收付项目  控制费用类型     他们对应同一个码表

lrp_cb 表 可以对应多条往来，lrp_lrp  只会关联一条cb

相当于一条cb有多个上游对他进行操作。   cb是下游类似议付交单，上游有发货单、出仓回单生成对应的cb，更新，删除对应的cb



**交单确认取消：**

红冲往来会删除cb表数据---此时lrpicode=lrpicodex

**交单融资放款红冲：**

红冲往来会还原cb表数据，此时lrpicode!=lrpicodex



# 测试场景

## 交单

> 注意正向-->逆向     再重复正向-->逆向的场景

DP/DA: 

- 提交审批-->交单确认->交单取消

- 交单提交审批生成负数往来，状态=10；冲销出仓回单生成的往来，此时lrpicodex 对应的是出仓回单生成的cb

- 交单确认生成往来，将之前负数往来状态改为70   

- 交单确认取消，红冲往来，此时rpicode=lrpicodex-cb表删除
- 没有承兑确认，承兑确认取消

LC：

- 需要生成预收账

- 即期LC 也是交单确认-->确认取消，基本跟DP/DA差不多，只是多了上面的预收账

- 远期LC   交单确认-->承兑确认-->承兑取消-->交单取消，只在承兑确认的时候生成往来，只在承兑取消的时候红冲往来

  

## 交单融资

> 无追全额放款   无追全额还款   无追部分放款   无追部分还款
>
> 有追全额放款   有追全额还款  有追部分放款   有追部分还款

> 交单金额是120，根据明细生成了两条往来50,70

### 无追放款：生成实收往来，还款生成实收（剩余的）

核销议付交单生成的往来（核销的是cb表），部分放款100，此时核销会核销金额50,70-（100-50）=20；

有2条往来数据，对应的cb表是议付交单的往来数据，应该是议付交单往来对应的cb表的金额fcyed进行扣减



放款-->放款取消

放款-->还款->还款取消->放款取消

放款->放款红冲->放款->还款->还款红冲->还款



**有追放款：生成应付往来，还款的时候核销应付，同时核销议付交单的应收往来，生成实收**





