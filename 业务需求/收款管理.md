![image-20250414170651427](E:\Apple\TyporaMD\Img\image-20250414170651427.png)



## 流程图讲解

这张图讲的是**客户给你打钱后，ERP系统里怎么认领、调整、冲销**的全过程，就像你开网店收到一笔订单款后要做的账务处理。以下是分步拆解：

---

#### **1. 网银流水数据**  
**（相当于“银行短信通知”）**  
• **场景**：客户的货款打到你的公司账户了，银行生成一条流水记录（含金额、打款方、时间）。  
• **ERP作用**：系统自动抓取这条流水，但还不知道这笔钱对应哪笔订单。  
• **举个栗子🌰**：  
  > 你卖货给客户A，他转了1万元，银行流水显示“XX公司转入1万”，但系统里还没匹配到具体订单。

---

#### **2. 网银到账认领工作台**  
**（相当于“对账小助手”）**  
• **场景**：财务人员在这里手动或半自动匹配流水和订单。  
• **ERP操作**：  
  • 选择一条银行流水，关联到对应的**客户订单**或发票（比如客户A的订单编号PO123）。  
  • 系统提示匹配成功与否（类似网购时“支付成功”提示）。  
• **关键点**：  
  • 如果流水不明（比如客户没备注订单号），需要人工联系确认。  
  • 匹配后，系统标记这笔钱为“已认领”。

---

#### **3. 收款认领**  
**（相当于“盖章确认到账”）**  
• **场景**：确认这笔钱属于哪笔应收款，核销客户欠账。  
• **ERP操作**：  
  • 系统自动生成凭证：`借：银行存款1万，贷：应收账款-客户A 1万`。  
  • 客户A的欠款记录减少1万。  
• **举个栗子🌰**：  
  > 客户A原本欠你1万，现在他付清了，你的账本上“应收款”就清零了。

---

#### **4. 款项调整**  
**（相当于“售后客服处理异常”）**  
• **场景**：发现钱对不上怎么办？比如：  
  • **多打了**：客户付了1.1万（多付1千）。  
  • **少打了**：客户只付了9千（还欠1千）。  
• **ERP操作**：  
  • **多付**：标记为“预收款”（下次订单抵扣），或退款。  
  • **少付**：保留“应收账款”差额，继续催款。  
• **举个栗子🌰**：  
  > 客户A多付1千，系统把这1千记到“预收账款”，下次他下单直接扣抵。

---

#### **5. 预收冲销（含“预应冲销”）**  
**（相当于“用定金抵尾款”）**  
• **场景**：客户之前付过定金（预收款），现在用定金支付尾款。  
• **ERP操作**：  
  • 系统自动用预收款冲抵本次应收款，生成凭证：  
    `借：预收账款1千，贷：应收账款1千`。  
  • 剩余金额走正常收款流程。  
• **举个栗子🌰**：  
  > 客户B下单时付了2千定金，尾款8千。这次他付尾款时，系统自动用2千定金抵扣，只需再收6千。

---

### **全流程总结**  
```  
银行到账 → 认领匹配订单 → 核销应收款 → 处理差额 → 用定金抵账  
```
**为什么需要这个流程？**  
• **钱账一致**：确保每笔钱都能对应到具体订单。  
• **减少手工活**：自动冲销、调整，财务不用按计算器。  
• **防错防漏**：客户多付少付都能妥善处理。  

**新手一句话记忆**：  
**“银行到账→认领订单→销账→有问题就调整→有定金就抵扣”**  
（就像网购收货后“确认订单→售后处理→用优惠券”一样简单！）