# 总线

```java

交单申请撤回是否成功：
http://10.91.17.205:8011/CTRM/FUND/ProxyServices/docuDeliveryBackResultPS
交单确认、承兑确认
http://10.91.17.205:8011/CTRM/FUND/ProxyServices/confirmDocuDeliveryPS
交单确认取消、承兑确认取消：
http://10.91.17.205:8011/CTRM/FUND/ProxyServices/cancelDocuDeliveryPS


交单融资办理结果回传：
http://10.91.17.205:8011/CTRM/FUND/ProxyServices/saveFinaLendInfoHandleResultPS
交单融资还款结果回传：
http://10.91.17.205:8011/CTRM/FUND/ProxyServices/saveFinaRepInfoHandleResultPS
交单融资申请撤回是否成功：
http://10.91.17.205:8011/CTRM/FUND/ProxyServices/docuDeliveryFinaBackResultPS



https://ctrm-sit.xiangyu.cn/xyacs/access/OT-ATS/FT-FUND/confirmDocuDelivery
https://ctrm-sit.xiangyu.cn/xyacs/access/OT-ATS/FT-FUND/docuDeliveryBackResult
https://ctrm-sit.xiangyu.cn/xyacs/access/OT-ATS/FT-FUND/cancelDocuDelivery
https://ctrm-sit.xiangyu.cn/xyacs/access/OT-ATS/FT-FUND/saveFinaLendInfoHandleResult
https://ctrm-sit.xiangyu.cn/xyacs/access/OT-ATS/FT-FUND/saveFinaRepInfoHandleResult
https://ctrm-sit.xiangyu.cn/xyacs/access/OT-ATS/FT-FUND/docuDeliveryFinaBackResult
https://ctrm-sit.xiangyu.cn/xyacs/access/OT-ATS/FT-FUND/docuDeliveryFinaCancel


http://10.91.17.205:8011/CTRM/FUND/confirmDocuDelivery/ProxyServices/confirmDocuDeliveryPS
http://10.91.17.205:8011/CTRM/FUND/docuDeliveryBackResult/ProxyServices/docuDeliveryBackResultPS
http://10.91.17.205:8011/CTRM/FUND/cancelDocuDelivery/ProxyServices/cancelDocuDeliveryPS
http://10.91.17.205:8011/CTRM/FUND/saveFinaLendInfoHandleResult/ProxyServices/saveFinaLendInfoHandleResultPS
http://10.91.17.205:8011/CTRM/FUND/saveFinaRepInfoHandleResult/ProxyServices/saveFinaRepInfoHandleResultPS
http://10.91.17.205:8011/CTRM/FUND/docuDeliveryFinaBackResult/ProxyServices/docuDeliveryFinaBackResultPS
http://10.91.17.205:8011/CTRM/FUND/docuDeliveryFinaCancel/ProxyServices/docuDeliveryFinaCancelPS
```



# 1-议付交单

## 交单/承兑确认

> http://127.0.0.1:8081/XYFT/access/OT-ATS/FT-FUND/confirmDocuDelivery

```

    {
    "docuDeliveryConfirmInfo": {
        "confirmtype": "2",
        "operator": "C00000000100019",
        "rdate": "1729664887408",
        "odate": "1729664887408",
        "dlydicode": "670f2869ef895a799420c33e"
    },
    "commonParameters": {
        "pageTotal": "1",
        "docType": "xxx",
        "docCode": "xxx",
        "source": "ATS",
        "target": "CTRM",
        "pageNo": "1"
    }
}
```

## 交单申请撤回是否成功

> http://127.0.0.1:8081/XYFT/access/OT-ATS/FT-FUND/docuDeliveryBackResult

```java

    {
    "docuDeliveryBackParam": {
        "operator": "bao.zhang",
        "retractresult": "S",
        "dlydicode": "6736f884a9aa4f147e7ac95e"
    },
    "commonParameters": {
        "pageTotal": "1",
        "docType": "xxx",
        "docCode": "xxx",
        "source": "ATS",
        "target": "CTRM",
        "pageNo": "1"
    }
}
```



## 交单确认、承兑确认撤回接口

> http://127.0.0.1:8081/XYFT/access/OT-ATS/FT-FUND/cancelDocuDelivery

```

    {
    "docuDeliveryCancelParam": {
        "operator": "bao.zhang",
        "retractresult": "S",
        "dlydicode": "6736f884a9aa4f147e7ac95e"
    },
    "commonParameters": {
        "pageTotal": "1",
        "docType": "xxx",
        "docCode": "xxx",
        "source": "ATS",
        "target": "CTRM",
        "pageNo": "1"
    }
}
```

# 2、交单融资

## 放款-融资办理结果回传

> http://127.0.0.1:8081/XYFT/access/OT-ATS/FT-FUND/saveFinaLendInfoHandleResult



```
{
  "lendInfoHandleResults": [
    {
      "isrecourse": "N",
      "startdate": "*************",
      "duedate": "*************",
      "docrate": 2.00,
      "irtfcy": 3.00,
      "feefcy": 4.00,
      "payertype": "10",
      "fintype": "20",
      "bankno": "bankno_5",
      "bankacccode": "************",
      "srcsyscode": "87001",
      "operator": "opera11",
      "dlydfaicode": "2"
    }
  ],
  "commonParameters": {
    "source": "source_56218e65d064",
    "target": "target_7dde598b09fa",
    "docType": "docType_a59d3f63eecd",
    "docCode": "docCode_7a2bf198e766",
    "pageTotal": "pageTotal_41588a50c722",
    "pageNo": "pageNo_6c0bbd7384f5",
    "property": "property_cef835fbacbe"
  }
}
```





## 还款-融资办理结果回传

> http://127.0.0.1:8081/XYFT/access/OT-ATS/FT-FUND/saveFinaRepInfoHandleResult



```
{
 "repInfoHandleResults": [
    {
      "repdate": "*************",
      "fcy": "66",
      "bankacccode": "bankacccode_3b34624e742c",
      "bankno": "bank",
      "feefcy": "3.21",
      "loanbankacccode": "loan22dc",
      "loanbankno": "loanb6",
      "loanfeefcy": "11",
      "loanirtfcy": "22",
      "recbankacccode": "recban44",
      "recbankno": "recb9e8f11c",
      "recfcy": "321",
      "operator": "oper",
      "srcsyscode": "srcsys4179",
      "dlydfaicode": "672365729676400d7fa9540a"
    }
  ],
  "commonParameters": {
    "source": "source_56218e65d064",
    "target": "target_7dde598b09fa",
    "docType": "docType_a59d3f63eecd",
    "docCode": "docCode_7a2bf198e766",
    "pageTotal": "pageTotal_41588a50c722",
    "pageNo": "pageNo_6c0bbd7384f5",
    "property": "property_cef835fbacbe"
  }
}
```

## 交单融资申请撤回是否成功

> http://127.0.0.1:8081/XYFT/access/OT-ATS/FT-FUND/docuDeliveryFinaBackResult
>



```java

    {
    "deliveryFinaBackResultInfo": {
        "operator": "C00000000100019",
        "retractresult": "S",
        "dlydfaicode": "2"
    },
    "commonParameters": {
        "pageTotal": "1",
        "docType": "xxx",
        "docCode": "xxx",
        "source": "ATS",
        "target": "CTRM",
        "pageNo": "1"
    }
}
```

## 交单融资放款、还款取消

> http://127.0.0.1:8081/XYFT/access/OT-ATS/FT-FUND/docuDeliveryFinaCancel

> http://10.91.17.205:8011/CTRM/FUND/ProxyServices/docuDeliveryFinaCancelPS
>

```
    {
    "deliveryFinaCancelInfo": {
        "operator": "C00000000100019",
        "canceltype": "1",
        "dlydfaicode": "2",
        "srcsyscode": "srrc"
    },
    "commonParameters": {
        "pageTotal": "1",
        "docType": "xxx",
        "docCode": "xxx",
        "source": "ATS",
        "target": "CTRM",
        "pageNo": "1"
    }
}
```







# 3、议付交单数据源 loadSource

> 根据商品拣配明细对应数量生成对应的分摊表明细
>
> 比如合同 1  有 2 条数据 10%  90%      每一条都要乘以对应比例的拣配明细，所以应该是 8 条

![dcacc1dcc8352d0c5e94ab22384a3bc](E:\Apple\TyporaMD\Img\dcacc1dcc8352d0c5e94ab22384a3bc.png)



## 销售待交单对象表

（1） 销售发货单：若是境内销售发货单，则销售发货单收款方式存在“系统收付款方式”为‘LC’（境外销售发货单不限收款方式），且销售发货单主表“是否提前交单”为‘是’：

①  销售发货单生效或提交到同审时，按收款方式分摊表（若是境内发货单，只有LC的结算方式才要写入对象表；境外发货单不限收款方式）写入对象表；

②  销售发货单红冲完后，同步更新【销售待交单对象表】记录的状态为‘作废’；

③  销售发货单红蓝操作后，同步更新【销售待交单对象表】记录的状态为‘作废’；蓝单提交到‘同审’或‘生效’状态后，重新将对象表状态更新为‘同审’或‘生效’；

④  销售发货单取消提交同审，同步删除【销售待交单对象表】对应记录；





# 状态流转

> 

## 交单





**交单申请调用ATS：**
提交审批--->通过定时任务task  触发DocuDeliveryApprovedTask---FundCommSheetExchange60Callback

此时状态从30变为 成功的话是62接口已接收    失败是59 接口犯错



**调用ERP申请撤回（已送接口撤回）：**   前提是62已接收状态才可申请撤回

成功状态改为91申请处理中，失败改为59接口返错



**此时可以调用ATS申请撤回是否成功接口：**

确认撤回成功：改为90作废

驳回的话：改为62已接收



**调用ERP然后执行交单确认接口：**

确认成功将状态改为70生效



**调用ERP可以执行交单确认取消接口：** 状态必须是70才能执行

状态改为62已接收









根据你提供的状态流转描述，我们可以使用Mermaid语法来绘制状态图。以下是状态流转的Mermaid流程图：

```mermaid
stateDiagram-v2
    [*] --> 30待审定时任务触发: 提交审批
    30待审定时任务触发 --> 62接口已接收:成功
    30待审定时任务触发 --> 59接口返错:失败

    62接口已接收 --> 91申请处理中: 申请撤回-已送接口撤回
    62接口已接收 --> 59接口返错: 调用失败

    91申请处理中 --> 90作废: 申请撤回成功S
    91申请处理中 --> 62接口已接收: 申请撤回驳回E

    62接口已接收 --> 70生效: 交单确认接口
    70生效 --> 62接口已接收: 交单确认取消接口

 
```

### 状态流转解释

1. **提交审批**：
   - 从初始状态 `[*]` 进入状态 `30`，表示提交审批。

2. **通过定时任务触发DocuDeliveryApprovedTask**：
   - 从状态 `30` 进入状态 `62`，表示接口已接收，审批通过。
   - 如果接口犯错，则从状态 `30` 进入状态 `59`，表示接口犯错。

3. **调用ERP申请撤回（已送接口撤回）**：
   - 从状态 `62` 进入状态 `91`，表示申请处理中。
   - 如果接口返错，则从状态 `62` 进入状态 `59`，表示接口返错。

4. **确认撤回成功**：
   - 从状态 `91` 进入状态 `90`，表示作废。
   - 如果驳回，则从状态 `91` 回到状态 `62`，表示已接收。

5. **调用ERP执行交单确认接口**：
   - 从状态 `62` 进入状态 `70`，表示生效。

6. **调用ERP执行交单确认取消接口**：
   - 从状态 `70` 回到状态 `62`，表示已接收。

7. **结束状态**：
   - 状态 `59`、`90`、`70` 都可以回到初始状态 `[*]`，表示流程结束。

### 总结

通过上述Mermaid流程图，可以清晰地看到各个状态之间的流转关系。每个状态的流转都有明确的前提条件和结果状态，确保了整个流程的逻辑清晰和可维护性。
