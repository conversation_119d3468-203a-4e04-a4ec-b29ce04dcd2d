# 外汇

**外汇（Foreign Exchange）** 简而言之是指不同国家的货币在国际交易中交换的行为和市场。外汇市场是一个全球性的金融市场，用于买卖不同货币的交易，，为国际贸易、投资和现金流动提供了基础设施。

## 外汇的基本特征

1. **货币对**：
   - 外汇交易通常是以两个货币的形式出现，例如 EUR/USD（欧元对美元），其中 EUR 是基础货币，USD 是报价货币。
2. **市场规模**：
   - 外汇市场是全球最大的金融市场，每日的交易量达到几万亿美元，几乎都是通过全球化的网络进行交易。
3. **即期与远期交易**：
   - **即期交易（Spot Forex）**：通常在交易后两个工作日内结算的交易，按照当前汇率进行货币兑换。
   - **远期交易（Forward Forex）**：双方约定在未来的某一时间点，以特定汇率交换货币的交易。
4. **用途**：
   - **国际贸易**：企业在跨国进出口时需要将本币兑换为外币进行交易。
   - **投资**：投資者参与外汇市场，通过货币兑换赚取差价。



外汇即是各国货币在国际间的交换工具，通过外汇市场进行的交易，不仅支持国际贸易，还有助于经济流动和投资。了解外汇的基本知识有助于从事全球化业务的个人和企业合理管理货币风险。

# 外汇交割

外汇交割（Foreign Exchange Settlement or Foreign Exchange Delivery）是指在完成外汇交易后，买方和卖方按照约定的条件和时间，实际交换货币的过程。这通常涉及两种不同货币的转移，是外汇交易（Forex Trading）的最后一步。

## 外汇交割的详细过程

1. **交易执行**：买方和卖方通过银行、经纪商或其他金融机构进行外汇交易，约定货币对、交易金额、汇率和交割日期。
2. **交割日期**：外汇交易通常有两个主要的交割日期类型：
   - **即期交割**（Spot Delivery）：交易完成后通常在两个工作日内进行交割。
   - **远期交割**（Forward Delivery）：双方约定在未来某个特定日期进行交割。
3. **货币转移**：在交割日，买方将其本币转账至卖方指定的银行账户，同时卖方将约定的外币转账至买方指定的银行账户。
4. **确认和结算**：双方银行确认货币转移完成，并进行最终的结算。

## 交割方式

- **实物交割**：实际转移货币的所有权，通常在银行间市场或大额交易中进行。
- **现金交割**：不涉及实际货币转移，而是通过现金结算差额，常见于零售外汇交易和差价合约（CFD）中。

## 风险管理

外汇交割涉及汇率风险，即在交割日实际汇率可能与交易时约定的汇率不同。为了管理这种风险，交易者可以使用以下工具：

- **远期合约**（Forward Contracts）：锁定未来某个日期的汇率。
- **期权**（Options）：购买权利（但非义务）以特定汇率进行交易。

## 注意事项

- **合规性**：确保交割过程符合相关法律法规和国际标准。
- **流动性**：某些货币对可能在特定时间或市场条件下流动性较低，影响交割效率。
- **信用风险**：确保交易对手方有足够的信用和财务稳定性，以避免交割失败。

外汇交割是确保外汇市场正常运作的关键环节，它不仅涉及货币的实际转移，还涉及风险管理和合规性问题。了解和妥善管理外汇交割过程对于所有参与外汇交易的实体都至关重要。

# 往来

> 　**往来款是什么意思？** 往来款可以这样理解：比如你销售商品给对方，对方没有付款给你，就是对方欠你的钱，在财务里记应收帐款。对方销售材料给你加工，你也没有付款给别人，就是你欠别人的钱，在财务里记应付帐款，应收和应付就是往来款。

在资金财务管理中，“往来”一般指的是企业与其他单位或个人之间的财务往来关系，包括应收款项、应付款项、借贷关系等。具体来说，往来通常涉及以下几个方面：

### 1. **应收款（Accounts Receivable）**
- 当企业为客户提供商品或服务并被允许在未来某个时点进行付款时，就形成了应收款。
- 应收款是企业的资产，反映了客户尚未支付给企业的款项。

### 2. **应付款（Accounts Payable）**
- 企业购买商品或服务后尚未支付给供货商或服务提供者的款项称为应付款。
- 应付款是企业的负债，表示公司需要在未来支付的款项。

### 3. **借贷往来（Loans Payable/Receivable）**
- 企业与银行、金融机构或其他企业之间的借款和贷款关系也属于财务往来。
- 借贷往来包括借款协议、还款计划及利息支付等方面的重要信息。

### 4. **其他往来（Other Receivables/Payables）**
- 包含与各种其他第三方的财务交互，例如保证金、预付账款、优惠补助、员工借款等。

### 往来的会计处理
- **账务记录**：往来的每一项变动都需要在财务系统中进行 detailed 的账务记录，包括发生事业的时间、款项金额、交易对方以及支付方式。
- **借贷平衡**：会计原则要求记录的借记总额和贷记总额保持平衡，确保财务报表的准确性。

### 重要性
1. **现金流管理**：通过管理应收款和应付款，企业能够有效地规划和控制现金流，提高资金使用效率。
2. **财务透明度**：对往来的清晰核算帮助财务人员和领导层更全面了解企业的财务状况，便于做出决策。
3. **信用管理**：管理应收款有助于企业进行信用评分、风险评估，以避免信用风险和坏账损失。

### 总结
“往来”在资金财务管理中是描述企业与外部单位和个人之间财务交流的重要说法，涵盖了应收应付双方的经济往来。在日常经营中，合理管理这些往来不仅是保证企业正常运营的重要方式，也是企业财务管理和分析的关键。



# D/P、D/A、LC

D/P（Documents against Payment）和 D/A（Documents against Acceptance）是国际贸易中常用的两种收付款方式。它们主要涉及交易双方在货物交付和付款之间的文件和流程。

### D/P（Documents against Payment）

#### 定义

D/P 是一种付款方式，在这种方式下，卖方通过银行提交一项文件包（包括运输单据、发票等），买方必须在支付货款之后才能获得这些文件以获取货物。

#### 工作流程

1. **交易建立**：卖方和买方签订销售合同，根据合同条款进行交易。
2. **发货**：卖方将货物发出，并准备相关的运输单据（如提单、商业发票）。
3. **文件提交**：卖方通过其银行将上述文件提交给买方所在银行。
4. **买方付款**：买方在看到文件后需要向其银行支付货款。
5. **获取文件**：买方在付款后，从其银行获取必要文档，进而提取货物。

#### 优缺点

- 优点
  - 卖方收到支付的保障性高。
  - 买方在未付款的情况下不能拿到货物，从而降低卖方的风险。
- 缺点
  - 卖方在接即付款时有交货风险，买方可能面临拒收货物的风险。

### D/A（Documents against Acceptance）

#### 定义

D/A 是一种取货付款方式，类似于 D/P)。但在 D/A 中，买方并不立即付款，而是先接受（承兑）卖方提供的汇票，支付方式因此延后。

#### 工作流程

1. **交易建立**：入手环节先如同 D/P 的步骤。
2. **发货**：卖方发出货物，并准备相关单据。
3. **文件提交**：卖方通过其银行将单据提交给买方所在银行。
4. **买方签署汇票**：买方在不支付货款的情况下，只需签署承兑汇票，即表示将来会按照约定时间支付货款。
5. **提取货物**：买方获得相关单据后，可以提取货物。
6. **到期付款**：到期时，买方需按照汇票进行付款。

#### 优缺点

- 优点
  - 给买方更多的付款时间和灵活性。
  - 买方有时间进行检查和销售，能在现货交易中遮掩一定的风险。
- 缺点
  - 卖方面临未知的收款风险，如果买方到期未付款，可能会影响企业流动资金。

### 总结

- **D/P（Documents against Payment）**：付款时获得文件，企业的现金流转相对安全；
- **D/A（Documents against Acceptance）**：买方在承诺付款的基础上获得商品，给予买方较大的灵活性和占用期。



# 原币、本位币、汇率

> ​     本位币是指本国国家的流通货币，如中国的本位币就是人民币，美国的本位币就是美元。原币则是一个相对概念，主要应用于跨国领域，如一家美国外资企业的老板是美国人，企业的财务报表为使老板看得明百，一般情况下都是按照美元来向老板汇报财务报表，这个时候的美元在中国国内就被看作是美国的原币。

1. **用友系统的本币和原币是什么意思**？

本币：就是记账本位币，在我国境内就是人民币(RMB)；而原币：就是你所做那笔业务的款项，人家用的是什么币别如美元,欧元,港币.....)等等，这要看你的厂家是什么币了。本币=原币*汇率。

> **比如美国公司在中国，5美元（原币） *7的汇率（此时是美元兑换人民币的汇率）=35人民币（本位币）**
>
> 比如中国公司在美国，100人民币（原币）*0.13的汇率（人民币兑换美元的汇率）=13美元 （本位币）

换而言之，本币是你建立账套的时候选择的本位币，系统默认是人民币，可以修改为其他的币种；非本位币的币种在系统称为外币，在单据上的名称为原币。
2.**本位币和原币有什么区别**？
那么，在输入外币凭证时有本位币自动折算成原币。那原币和本位币有什么区别呢？用友软件商城为您解释用友软件中本位币和原币的区别。
**本位币和原币**都是相对的概念。 
比如中国，规定以人民币为记帐本位币，意思是说在国内的企业所有的财务报表都要用人民币来进行反应，方便统计、计划等宏观分析，也方便横向、纵向对比； 
而原币则相对于本位币来讲，比如一家德国外资企业，老板是德国人，企业的财务报表当然要让德国老板看得明百，一般情况下都是按照德国马克来相老板汇报财务报表的，这个时候的德国马克在中国国内就被看作原币。 
同样的情况如果到了德国，则马克成了本位币，而人民币则成了德国人眼中的原币。 
一般情况下，国内的外资企业在向国内的一些政府机构上报统计报表的时候，都要换算成本位币的报表。

- sfcode 本位币币种，根据公司获取，比如在中国本位币就是CNY了


- scerate   本位币折人民币汇率

  ```java
  BigDecimal scerate = FundUtils.getFrate(corpbcode.get(), sfcode, "CNY", info.getRepdate(), docuAccOrd.getSheetcode(), docuAccOrd.getCuicode());
  ```
- suerate  本位币折美元汇率

  ```java
  BigDecimal suerate = FundUtils.getFrate(corpbcode.get(), sfcode, "USD", info.getRepdate(), docuAccOrd.getSheetcode(), docuAccOrd.getCuicode());
  ```
- fserate   当前单据币种折本位币汇率--也叫做折本位币汇率

  ```java
  	BigDecimal fserate = FundUtils.getFrate(corpbcode.get(), fcode, sfcode, info.getRepdate(), docuAccOrd.getSheetcode(), docuAccOrd.getCuicode());
  ```

所以人民币是本位币sfcode=CNY的话，单据也是人民币的话，那就是fserate=1，suerate=0.14，scerate=1

如果人民币是本位币sfcode=CNY,但是单据获取的币种是USD的话，fserate=7，suerate=0.14，scerate=1