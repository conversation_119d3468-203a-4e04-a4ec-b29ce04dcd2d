![image-20250402103006288](E:\Apple\TyporaMD\Img\image-20250402103006288.png)

> 预付冲销和款项调整区别？
>
> 预付冲销限制更多，款项调整调整的东西更多，具体。。。

## 流程图讲解

这张图描述的是企业通过ERP系统**支付供应商款项**的全过程，从“记账”到“付款”再到“调整”，就像你网购后从下单到退款的全流程。以下是分步解释：

---

#### **1. 应付余额/新建**  
**相当于“记账”**  
• **场景**：你公司收到供应商的发票（比如买了10箱原料，总价1万元），但还没付款。  
• **ERP操作**：财务在系统中录入这笔欠款，生成“应付账款”记录（相当于记账本上写“欠A供应商1万”）。  
• **作用**：明确欠谁多少钱，避免漏付或重复付。

---

#### **2. 付款申请**  
**相当于“申请打款”**  
• **场景**：快到付款日了，采购部或财务部提交付款申请（比如“下周付A供应商1万”）。  
• **ERP操作**：  
  • 填写付款金额、供应商账号、付款理由（如“原料款”）。  
  • 提交后需领导审批（类似你网购下单前要输密码确认）。  
• **作用**：防止乱花钱，确保付款合规。

---

#### **3. 资金实付（标注“资金”）**  
**相当于“实际转账”**  
• **场景**：审批通过后，财务通过网银或支票把钱打给供应商。  
• **ERP操作**：  
  • 系统生成付款凭证（自动记账：借“应付账款”1万，贷“银行存款”1万）。  
  • 标记该笔应付账款为“已付”。  
• **作用**：钱付出去后，系统里的账要同步更新。

---

#### **4. 款项调整**  
**相当于“售后处理”**  
• **场景**：付款后发现问题，比如：  
  • **多付了**：实际应付1万，但手滑付了1.1万。  
  • **货有问题**：供应商同意退款或补货。  
• **ERP操作**：  
  • 调整应付金额（如冲减多付的1000元）。  
  • 生成调整凭证（借“银行存款”1000，贷“应付账款”1000）。  
• **作用**：确保账目和实际一致，避免对不上。

---

#### **5. 预付冲销（含“预应冲销”箭头）**  
**相当于“用预付款抵账”**  
• **场景**：如果你之前付过定金（比如预付了3000元），这次实际应付1万，只需再付7000元。  
• **ERP操作**：  
  • 系统自动用预付的3000元冲抵本次应付款（剩余7000走“资金实付”）。  
  • 生成凭证：借“应付账款”3000，贷“预付账款”3000。  
• **作用**：避免重复付款，简化流程。

---

### **完整流程总结**  
1. **记账**（应付余额/新建）→ 2. **申请付款**（付款申请）→ 3. **打钱**（资金实付）  
   ↓  
4. **有问题？**（款项调整）→ 5. **有定金？**（预付冲销）

---

### **为什么需要这个流程？**

• **防错**：避免付错钱、付多钱。  
• **留痕**：每一步都在ERP中记录，方便查账。  
• **省时间**：自动化冲销、调整，不用手工算账。  

如果你是新手，只需记住：**“记账→申请→付款→有问题就调整”**，其他细节ERP会自动处理！



## 付款申请单

> 如果是拷贝新建的，一定是采购货款，这个是上游到货单生成的往来数据？  如果是手动新建的，可以自己选择收付项目

付款申请单上游为非业务应付发票

付款申请审批通过后推送资金壳，由资金壳G6 生成实付记录推送回来---支付记录tab

> 资金壳类似总线，决定推送G6还是ATS？？？

有了支付记录以后，根据收付项目，如果是应付，可以生成付款调整单；如果是预付，可以生成预付冲销单

> 通过追加应付生成的或者入口拷贝新建生成的付款明细正常都是应付，如果是手动录入的才是预付？

![image-20250402112509945](E:\Apple\TyporaMD\Img\image-20250402112509945.png)

## 付款实付单-资金实付单

一个付款申请可以生成多个资金实付单号；

每个资金实付单号，对应分摊到付款明细，所以对应资金实付单里面的实付单号对应多个实付分摊明细

![image-20250402112724552](E:\Apple\TyporaMD\Img\image-20250402112724552.png)

每个资金实付单号，对应分摊到付款明细，所以对应资金实付单里面的实付单号对应多个实付分摊明细

![image-20250402161059326](E:\Apple\TyporaMD\Img\image-20250402161059326.png)





## 付款调整单、预付冲销单

付款调整单可以根据有无款项调整进行选择，预付冲销单只能是有款项调整；两者区别具体应该是根据收付项目？



## 为什么是拷贝fpsg_cb_view

为什么不直接拷贝fpsg 付款实付记录表数据，而是要弄一张cb表，因为考虑到可以多次拷贝核销，相当于有一个核销记录表，如果直接拷贝原表，相当于每次全量拷贝，并且不核销的情况才可以，类似于交单融资拷贝议付交单

再比如一种是跨服务的拷贝cb表，比如议付交单的拷贝新建来源是销售发货单收付款方式分摊表，不可能直接访问上游的这个分摊表，而是要基于这个分摊表生成对应的cb核销表，从这个核销表里面多次拷贝核销



对于每次拷贝新建，涉及的计划执行金额fcy，待执行金额fcying，已执行金额fcyed------fcy=fcyed+fcying；新建单据默认是全量的计划执行金额，有的可以修改待执行金额，新建完单据是会插入一条ct表记录，类似日志记录，记录内容是本次用了多少金额，

假设比如总的计划执行金额是fcy=100；第一次拷贝输入的金额是10 那就是生成一条10金额fcy的ct表记录，此时fcying=10，如果再次拷贝新建一条输入金额20；此时fcying=10+20（是累加的！）这个输入的金额对应的是单据里面明细的fcy，比如交单明细的fcy作为插入ct表的记录，注意如果针对同一条交单明细一直修改金额，改的也是对应的ct表的金额，而不会新增一条ct记录，除非再次针对这个cb表的行记录拷贝新建一个单据，就会生成新的一个ct记录

```
select
g.dlydgicode tgticode,'ft_docu_dlydg' tgttbl,g.salshipssicoder,g.fcy as fcy
from
ft_docu_dlyd m join ft_docu_dlydg g on m.dlydicode=g.dlydicode
where
g.dlydgicode in (:tgticodes) and g.salshipssicoder is not null
```





