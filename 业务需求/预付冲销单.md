# 预付冲销单

> - 
>   预付款---就是公司自己实际付款的钱
>   冲销数据---就是别人给公司付款的钱
>
> - 相当于用别人给公司付款的钱 冲掉自己去实付款的钱
>
> - **生效以后---冲销数据会生成一条正的数据  付款申请子表      预付款生成一条负数的数据**
>
>   红冲是发现做错了    ：红冲: [红字](https://zhidao.baidu.com/search?word=红字&fr=iknow_pc_qb_highlight)冲销, 会计常用语, 意思是一笔帐记错了, 就做一笔红字填写的负帐把错误冲平掉.
>
>   
>
>   结算对象锁死    其他没有   要判断

>
> 付款申请主表：支付以后生成实付记录，然后会生成对应的实付分摊明细表。
>
> 本次使用金额  是在输入值以后传到后端  到对应的列表字段  然后列表字段有值了根据那个字段自动生成记录

<img src="E:\Apple\TyporaMD\Img\image-20240819111229710.png" alt="image-20240819111229710" style="zoom:67%;" />







<img src="E:\Apple\TyporaMD\Img\image-20240624095206114.png" alt="image-20240624095206114" style="zoom:67%;" />

<img src="E:\Apple\TyporaMD\Img\image-20240624095226007.png" alt="image-20240624095226007" style="zoom:67%;" />



![image-20240625091332869](E:\Apple\TyporaMD\Img\image-20240625091332869.png)

<img src="E:\Apple\TyporaMD\Img\image-20240624095258941.png" alt="image-20240624095258941" style="zoom:67%;" />



### 详情页默认设置字段？

> 内码一定要设置，单据相关字段也设置，避免有问题

```java
<c name="status" title="${RES.C}" sqltype="12" width="${E.G.CW.status}" codedata="#SN-PLAT.status"
showname="true"
cmparams="sheetcode:'FT-DEMO.CcodeStopApply'" rdonly="true"/>
<c name="bcode" title="${b_applywcode}" sqltype="12" width="${E.G.CW.bcode}" codedata="#FT-ORGZ.BWcode" disableed="true"
showfname="true" tipIfOverflow="true" uiprops.textDir="rtl" aidInputerBtn="true" submitOnInput="true" noblankOnSubmit="true"
cmparams.sheetcode="FT-CCODE.StatusChange" cmparams.opids="C" disableDelIfAI="true"
uiprops.renderer="new snsoft.plat.busi.comm.BusiBWcodeNameRender({})" />
<c name="wcode" title="${RES.C}" sqltype="12" codedata="#FT-ORGZ.Wcode" showname="true" width="${G.CW.wcode}" hidden="true"/>

<c name="cuicode" sqltype="12" hidden="true"/>
<c name="sheetcode" sqltype="12" hidden="true"/>

<c name="ratifydate" title="${RES.C}" sqltype="93" width="${E.G.CW.time}" hidden="true"/>
<c name="submitdate" title="${RES.C}" sqltype="93" width="${E.G.CW.time}" hidden="true"/>
<c name="performdate" title="${RES.C}" sqltype="93" width="${E.G.CW.time}" hidden="true"/>
<c name="vprepare" title="${RES.C}" sqltype="12" hidden="true"/>
<c name="predate" title="${RES.C}" sqltype="93" hidden="true"/>
<c name="modifier" title="${RES.C}" sqltype="12" hidden="true" modifierColumn="true"/>
<c name="modifydate" title="${RES.C}" sqltype="93" hidden="true" modifydateColumn="true"/>
<c name="wfcode" title="${RES.C}" sqltype="12" hidden="true"/>
<c name="wfuid" title="${RES.C}" sqltype="12" hidden="true" codedata="#SN-APPR.wfunit" showname="true"
tipIfOverflow="true"
xprops.CodeData.KeyNames="wfcode"/>
```







