# 一、总体原则

系统做《版本修改》时采用在原表新增记录的方式，并采用如下字段对不同的记录进行区分：

1、版本标识：用于区分最新版本蓝单据和历史版本数据

| 值   | 含义                                             |
| ---- | ------------------------------------------------ |
| 0    | 最新有效状态的单据                               |
| 1    | 处于版本修改中的最新有效状态的单据               |
| 2    | 处于版本修改中的最新未生效的单据                 |
| 6    | 处于版本修改中的最新未生效的单据（自动版本备份） |

1、上一版本和正在修改的版本在同一个表中，并增加标记（当前表中只保留处于版本修改中的上一版本数据）；

2、当执行《版本修改》时，处理如下：

 1）将上一版本内码增加后缀同时状态改为68，并将标识设置为1；

 2）将当前修改版本内码和原内码一致，同时状态改为11，并将标识设置为2。

3、当执行《取消版本修改时》处理如下：

 1）删除当前修改版本数据；

 2）将上一版本内码去掉后缀同时状态改为70，并将标识设置为0。

4、当修改版本生效处理如下：

 1）将上一版本记录删除，并进入到版本备份表中；

 2）将当前修改版本生效，并将标识设置为0。

5、当统计上一版本的数据可按条件：  （状态 >=70 or 状态=68）；

6、当统计含最新版本的数据可按条件：  状态>=20/30  且 标识=0,2,6  ；

# 二、数据推演

| 第一次录入数据 |           |          |        |      |          |           |          |           |      |
| :------------- | :-------- | -------- | ------ | ---- | -------- | --------- | -------- | --------- | ---- |
| 操作           | **主表**  | **子表** |        |      |          |           |          |           |      |
| 主表内码       | 主表内码V | 版本标识 | 版本号 | 状态 | 子表内码 | 子表内码v | 主表内码 | 主表内码V |      |
| 原始           | M01       | M01      | 0      | 1    | 70       | G01       | G01      | M01       | M01  |

| 第一次版本修改且未生效 |           |          |        |      |          |           |          |           |      |
| :--------------------- | :-------- | -------- | ------ | ---- | -------- | --------- | -------- | --------- | ---- |
| 操作                   | **主表**  | **子表** |        |      |          |           |          |           |      |
| 主表内码               | 主表内码V | 版本标识 | 版本号 | 状态 | 子表内码 | 子表内码v | 主表内码 | 主表内码V |      |
| 原始                   | M01<01>   | M01      | 1      | 1    | 68       | G01<01>   | G01      | M01<01>   | M01  |
| 新                     | M01       | M01      | 2      | 2    | 11       | G01       | G01      | M01       | M01  |

| 第一次版本修改且生效 |           |          |        |      |          |           |          |           |      |
| :------------------- | :-------- | -------- | ------ | ---- | -------- | --------- | -------- | --------- | ---- |
| 操作                 | **主表**  | **子表** |        |      |          |           |          |           |      |
| 主表内码             | 主表内码V | 版本标识 | 版本号 | 状态 | 子表内码 | 子表内码v | 主表内码 | 主表内码V |      |
| 新                   | M01       | M01      | 0      | 2    | 70       | G01       | G01      | M01       | M01  |

| 第一次版本修改《撤销版本修改》 |           |          |        |      |          |           |          |           |      |
| :----------------------------- | :-------- | -------- | ------ | ---- | -------- | --------- | -------- | --------- | ---- |
| 操作                           | **主表**  | **子表** |        |      |          |           |          |           |      |
| 主表内码                       | 主表内码V | 版本标识 | 版本号 | 状态 | 子表内码 | 子表内码v | 主表内码 | 主表内码V |      |
| 原始                           | M01       | M01      | 0      | 1    | 70       | G01       | G01      | M01       | M01  |



<img src="E:\Apple\TyporaMD\Img\image-20250506104935644.png" alt="image-20250506104935644" style="zoom:67%;" />

```
status_68?被修改  
status_11?修改

<column name="lcaicodev" title="信用证开证申请原版本内码" type="VARCHAR(SZIBILL)"/>
<column name="vsn" title="版本" type="INTEGER" />
<column name="vsnflag" title="版本标记列名" type="INTEGER" />
<column name="vsntype" title="版本修改类型" type="VARCHAR(SZTYPE)" />
```



```
select m.status,
       m.lcaicode,
       m.lcaicodev,
       lcacode,
       vsn,
       vsnflag,
       vsntype,
       '子表',
       g.lcagicode,
       g.lcagicodev,
       g.lcaicode,
       g.lcaicodev,
       mergeflag,
       changeflag,
       retractflag
from ft_lc_app m,
     ft_lc_appg g
where m.lcaicodev = '68076f566b11430690f1f1bc'
  and m.lcaicode = g.lcaicode
```

