# 审批相关

> 需要注意参考别人的 VO 类 和 service  是否有改变

```java
AdvPayOffService extends FTBusiAccessService<AdvPayOff>, SheetApprAccessService<AdvPayOff>
    
AdvPayOffServiceImpl extends FTBusiAccessServiceImpl<AdvPayOff>

RecPayOff extends FTSheetVO 
```

监听

```java
<!-- 审批相关 -->
<bean code="FT-PDOC.90" sortidx="90" impl="#SN-APPR.UITaskPerformListener?[{}]"/>
<bean code="FT-CCODE.150" sortidx="150" impl="#new snsoft.prod.appr.TaskPerformListener({})"/>
```

# 文档打印

- `cfg/res/doctype/voref/VORefFT-RPADJ.xml`

> 可能报错 key is null

- 配置文档打印监听 [FN-sv.save.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\sheet\FT-RPADJ.AdvPayOff\FN-sv.save.xml) 

```java
<!-- 附件相关 -->
		<bean code="FT-RPADJ.30" sortidx="30" impl="#FT-PLAT.FTSheetDocSaveSVListener?[{'sheetcode':'${sheetcode}'}]"/>
		<bean code="FT-RPADJ.40" sortidx="40" impl="#SN-PLAT.SheetDocWfSVListener?[{'attachCfg':'ft_rpadj_off:${sheetcode}#plat_sheetdoc'}]"/>
```

- 配置文档打印监听 [FN-ui.detail.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\sheet\FT-RPADJ.AdvPayOff\FN-ui.detail.xml) 

```java
<!-- 附件相关 -->
		<bean code="FT-PDOC.80" sortidx="80" impl="#SN-PLAT.SheetDocSaveUIListener?[{tgtUINames:'${mainUINames}',sheetCodes:'${sheetcode}'}]"/>
```



# 新建弹框-校验并初始化

```java
   <CommandRef ref="{'FT-TPL.SheetCmdRefTplt':'newSheet'}">
            <!--弹出界面-->
            <property name="muiid" value="FT-RPADJ.NewAdvPayOffDlg" />
        </CommandRef>
```

同时配置页面需控制收款方选择的时候要先选择部门-业务员   [NewAdvPayOffDlg.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\ui\res\FT-RPADJ\NewAdvPayOffDlg.xml) 配置监听

```java
<jslistener><![CDATA[
    #new snsoft.plat.bas.busi.PlatFunctionListener({})
    #new snsoft.ft.rpadj.pay.NewAdvPayOffJSListener({})
]]></jslistener>
```

理论上点完确定会自动打开到详情页，通过参数配置---穿透定义

```java
?InitValue.ft_pfiv_feeinv.bcode=B000000003&InitValue.ft_pfiv_feeinv.sfcode=CNY
```



|      |      |      |
| ---- | ---- | ---- |
|      |      |      |
|      |      |      |
|      |      |      |



# 版本备份

1、 [status.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\status.xml)  添加状态

```
 <entry code="11" name="备份"/>
```

2、按钮添加两个，版本修改  取消版本修改

```
<CommandRef ref="{'FT-TPL.SheetCmdRefTplt':'vbBackup'}" />
<CommandRef ref="{'FT-TPL.SheetCmdRefTplt':'vbCancel'}" />
```

**同时 name 要叫做 toolbar**

```
<m:Toolbar name="toolbar" uiprops.cellClassName="head-toolbar-panel">

```

3、DialogPane 隐藏字段新增 2 个

```
<m:DialogPane name="param" region="north" showToolbar="true" layoutm="grid" hidden="true">
        <c name="sstcicode" title="内码" sqltype="12" noblank="true"/>
        <c name="vsn" title="${RES.C}" sqltype="4"/>
        <c name="oldvsn" title="${RES.C}" sqltype="4"/>
    </m:DialogPane>
```

4、VO 修改
实现 VersionBackupVO

```
BasVO implements VersionBackupVO
```

加字段

```
/**
	 * 版本号
	 */
	@Column
	@DefaultValue("1")
	private Integer vsn;
	/**
	 * 版本标记
	 */
	@Column
	@DefaultValue("0")
	private Integer vsnflag;
	/**
	 * 版本修改类型
	 */
	@Column
	private String vsntype;

	/**
	 * 本版本生效时间
	 */
	@Column
	private Date curratifydate;
```

5、添加 [vnbk.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\vnbk.xml) 

> 注意一定要配置另外 2 个，不然审批前端按钮展示会不显示

```
 <entry tbl="ft_cd_xychiqp_stc">
        <pos>curratifydate,wfcode,wfuid</pos>
    </entry>
```

6、点击版本备份以后  提交按钮不显示？？

```
snsoft.prod.appr.cmd.ApprCmdProvider#canSubmit  
通过这个控制，如果有审批记录则不展示次按钮
```

 [vnbk.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\vnbk.xml) 加上！处理这个审批记录

```
<bean code="FT-PROT.40" sortidx="40">
    #SN-APPR.ApprVersionBackupListener
</bean>
```

7、监听总结：
 [FN-ui.detail.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\FN-ui.detail.xml) 

```
<bean code="FT-CCODE.200" sortidx="230"
			impl="#new snsoft.plat.bas.sheet.vnbk.CompareVersionJSListener({mUiName:'ft_cd_xychiqp_stc',hiddenQueryPane:true})" />
		<!--版本比较 UI-->
		<bean code="FT-CCODE.210" sortidx="235" impl="#SN-PLAT.CompareVersionUIListener?[{}]" />
		<bean code="FT-CCODE.220" sortidx="240"
			impl="#new snsoft.plat.bas.sheet.vnbk.VersionBackupListener({'sheetCode':'${sheetcode}',hiddenQueryPane:true})" />
```

 [FN-sv.save.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\FN-sv.save.xml) 

```
        <bean code="FT-CCODE.60" sortidx="60" impl="#SN-PLAT.VersionBackupFunctionListener"/>

```

 [vnbk.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\vnbk.xml) 

```
 <beans>
        <bean code="FT-LOAN.StatusVersionBackupListener" sortidx="10">
            #SN-PLAT.StatusVersionBackupListener
        </bean>
        <bean code="FT-PROT.40" sortidx="40">
            #SN-APPR.ApprVersionBackupListener
        </bean>
    </beans>
```

### 新版本

> 不同点说明

1、按钮添加两个，版本修改  取消版本修改

```java
<!--版本修改-->
		<CommandRef ref="{'FT-TPL.SheetCmdRefTplt':'sheetVnbk'}" />
		<CommandRef ref="{'FT-TPL.SheetCmdRefTplt':'vbCancel'}" />
```

sheetVnbk 默认是下拉框，所以这里要额外配置--**新建 [vnmd.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\sheet\FT-DOCU.DocuDelivery\vnmd.xml)** 

```java
<!--正常修改，可不设置bean,默认指向：#SN-PLAT.VnbkCommandBackup-->
<entry sysvntype="S01" />
```

2、vnk.xml 参考议付交单，里面也有监听

3、 [FN-sv.save.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\sheet\FT-DOCU.DocuDelivery\FN-sv.save.xml)  监听

```java
	<!--        版本控制 -->
		<bean code="FT-DOCU.DocuDelivery.110" sortidx="110" impl="#SN-PLAT.VersionBackupFunctionListener"/>
```

 [FN-ui.detail.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\sheet\FT-DOCU.DocuDelivery\FN-ui.detail.xml) 监听    版本比较

```java
<bean code="FT-DOCU.DocuDelivery.170" sortidx="170" impl="#new snsoft.plat.bas.sheet.vnbk.CompareVersionJSListener({ignoCmpCell:'predate,modifydate'})"/>
<bean code="FT-DOCU.DocuDelivery.180" sortidx="180" impl="#SN-PLAT.CompareVersionUIListener?[{}]"/>
```

4、注意要有 curratifydate 字段

```java
<column name="curratifydate" title="本版本生效时间" type="DATE" />

```

其他的参考上面的！





# 复制

1、 [copy.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\copy.xml)   加上拷贝字段和监听

```
 <beans>
        <bean code="FT-PLAT.FTOutCodeSheetCopyListener" sortidx="10">
            #FT-PLAT.FTOutCodeSheetCopyListener?[{}]
        </bean>
    </beans>
    <entry tbl="ft_cd_xychiqp_stc">
        <neg>sstcicode,sstccode,status,vprepare,predate,modifier,modifydate,wfcode,wfuid,vsn,vsnflag,vsntype,submitdate,performdate,ratifydate,curratifydate</neg>
    </entry>

    <entry tbl="ft_cd_xychiqp_stc_g">
        <neg>sstcgicode,sstcicode,stopexp</neg>
    </entry>
```

2、按钮定义即可

```
 <CommandRef ref="{'FT-TPL.SheetCmdRefTplt':'sheetCopy'}">
            <property name="tbname" value="toolbar"/>
        </CommandRef>
        <Command cmd="sheetCopy">
            <Control ctrlField="hidden"  setValue="false" sheetcode="${sheetcode}" btnOpids="${opids?C}">
                <Condition colname="status" matchValue="30" matchMode="3"/>
            </Control>
            <ClientInvokers>
                <Invoker code="30">
                    new snsoft.ft.comm.cmdreg.CmdCommInvoker({})
                </Invoker>
            </ClientInvokers>
        </Command>
```

好像没有额外的操作配置监听了

# 撤单

1、 [retract.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\retract.xml) 

```
<bean code="statusRetractBean" sortidx="10">
    #SN-PLAT.StatusRetractBean
</bean>
<bean code="pntProjectRetractBean" sortidx="20">
    #FT-PLAT.CodeRefRetractBean?[{}]
</bean>
```

2、[coderef.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-help-java\xyft-help\src\main\resources\cfg\sheet\FT-DEMO.CcodeStopApply\coderef.xml)  需要配置编码引用，撤单的时候会校验，CodeRefSqlMapper 自动拼接参数？  参数都用 Map

​	<entry code="FT-Prj.Business" ref="#SN-PLAT.CodeRefSqlMapper?['snsoft.ft.prj.sqlm.service.PrjSqlMapper','queryBusinessByPticode','updateBusinessByPticode']"/>

3、然后按钮注册即可

```
        <CommandRef ref="{'FT-TPL.SheetCmdRefTplt':'retract'}" />

```

好像没有额外的操作配置监听了



# 界面属性控制

监听：

```java
<!--        界面属性控制监听类-->
<bean code="FT-CCODE.40" sortidx="40" impl="#SN-UI.UIOptCtrlListener?[{sheetCode:'${sheetcode}'}]" />
```



```java
<UIOpts>
		<uiname>ft_cd_xychiqp_stc</uiname>
		<colname>sstccode</colname>
		<ctrlFields>readOnly</ctrlFields>
		<setValue>true</setValue>
		<UIVals grp="0">
			<uiname>ft_cd_xychiqp_stc</uiname>
			<colname>vsn</colname>
			<sqltype>4</sqltype>
			<matchMode>2</matchMode>
			<matchValue>1</matchValue>
		</UIVals>
	</UIOpts>
```



**注意事项：**

- 属性控制的 uiname 要写当前 record 表的，不能写表头的，不然会控制表头隐藏的字段，以为没有效果

- 同理，如果在 js 实现属性控制，也是要获取对应的 table，比如基本信息的 table，而不是主表

  ```java
  if (!$bool(basTable))
  		{
  			basTable = getTable(table, "ft_docu_dlyd_bas");
  		}
  ```

- 另外如果在 js 实现了属性控制，记得在原来的属性控制删除掉，不然会冲突发现生效不了





# 按钮操作-监听、编辑笔头

按钮 invoker 顺序：
前端执行完 invoke，执行完如果后端有配置 invoke  调用后端的 invoke，根据 method ='invoke'

```java
<ClientInvokers>
    <Invoker code="10">
        new snsoft.ft.comm.cmdreg.CmdCommInvoker({time:4,mode:9})
    </Invoker>
    <Invoker code="20">
        new snsoft.ft.docu.accord.invoker.DocuAccordSendSerCancelInvoker({})
    </Invoker>

</ClientInvokers>
<UIInvokers>
    <Invoker code="10" method="invoke">
        #FT-DOCU.DocuAccordSendSerCancelInvoker
    </Invoker>
</UIInvokers>
```



```java
<ClientInvokers>
    <Invoker code="10">
        new snsoft.ft.comm.cmdreg.CmdCommInvoker({time:5})
    </Invoker>
    <Invoker code="20">
        new snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker()
    </Invoker>
    <Invoker code="30">
        new snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker2()
    </Invoker>
    <Invoker code="40">
        new snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker3()
    </Invoker>
</ClientInvokers>
<UIInvokers>
    <Invoker code="20" method="beforeInvoke">
        #FT-DOCU.FinanceCalculateInvoker
    </Invoker>
    <Invoker code="30" method="beforeInvoke">
        #FT-DOCU.FinanceCalculateInvoker2
    </Invoker>
    <Invoker code="40" method="invoke">
        #FT-DOCU.FinanceCalculateInvoker3
    </Invoker>
</UIInvokers>

FinanceCalculateInvoker1111-check
VM868:388 FinanceCalculateInvoker222-check
VM868:418 FinanceCalculateInvoker333-check
VM868:352 FinanceCalculateInvoker1111-beforeInvoke
VM868:382 FinanceCalculateInvoker222-beforeInvoke
VM868:412 FinanceCalculateInvoker333-beforeInvoke
VM868:364 FinanceCalculateInvoker1111-invoke
VM868:394 FinanceCalculateInvoker222-invoke
VM868:424 FinanceCalculateInvoker333-invoke
```

- 后端 invoker 的 code 跟前端没有关系，前端的 invoke 方法都会先执行完毕才会调用到后端对应的方法，并且前端 **只要有一个 invoke 方法（注意默认的时间 CmdCommInvoker 返回了也会当做有返回）？？** 有返回值就会到后端，并且参数会带到后端

  ```java
    <Invoker code="9999" method="invoke">
          #FT-DOCU.FinanceCalculateInvoker3
      </Invoker>
  ```

  

- check 方法先按顺序执行，不管是否有弹框，check 都会按顺序执行

- 接着 check 的弹框点了放弃，就不会执行后续的任何方法了

- 调用前方法，如果存在返回值则调用服务端 beforeInvoke 方法理解：意思就是如果这里有返回值，同时后端配置的 <Invoker code="20" method="beforeInvoke">  就会调用，如果 null 就不调用后端的 apply 方法了

  > 这里调用服务端 beforeinvoke 方法的意思是说根据 method 配置了什么，就调用 apply；比如配置了 method = "beforeInvoke" 会在前端 invoker 的 beforeInvoke 方法有返回值的情况下调用对应后端的 apply 方法，如果没有返回值就不调用了

  ```java
  注意是对应同个code 的情况下！
   <Invoker code="20">
          new snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker()
      </Invoker>
    <Invoker code="20" method="beforeInvoke">
          #FT-DOCU.FinanceCalculateInvoker
      </Invoker>
  ```

  > 常用组合：check、invoke、afterInvoke

```java
	<td>按钮事件处理父类</td>
 * 		<td>onClick:按钮点击前，处理与当前按钮业务无关的前置操作，可以返回弹出对话框；
 * 			<br/>beforeCheck:检查前方法，如果存在返回值则调用服务端beforeCheck方法；
 * 			<br/>check:检查客户端逻辑，可以返回弹出对话框；
 * 			<br/>beforeInvoke:调用前方法，如果存在返回值则调用服务端beforeInvoke方法；
 * 			<br/>invoke:按钮客户端业务逻辑接口，如果存在返回值则调用服务端invoke方法；
 * 			<br/>afterInvoke:如果服务端invoke方法存在返回值，则调用此方法；----这个好像有问题！
 * 		</td>
```

## 编辑按钮

```java
<attrs>
    <attr type="203" name="editccode" title="${FT.cmd_edit}${ft_rpadj_off.ccode}" _rlog="true"/>
</attrs>

<ToolbarBtn name="ft_rpadj_off_bas_btn_editccode" notInDataSet="true" xprops.domTitle="${FT.cmd_edit}${ft_rpadj_off.ccode}" noClientComponent="true" uiprops.className="ui-btn" xprops.iconClassName="icons-btn-edit" titleHidden="true" followLayout="true"/>
```

注意 uiname 对应，cmd 对应

```java
<CommandGroup uiname="ft_rpadj_off_bas">
		<Command cmd="editccode" saveOnClick="true" pstn="cell" checkModified="false" title="${FT.cmd_edit}" tblname="${mainTblname}"
			btndesc="{icon:'icons-btn-edit'}" reskeys="">
			<!--<Control ctrlField="disabled" setValue="false" sheetcode="${sheetcode}" btnOpids="${opids?C}">-->
				<!--<Condition colname="status" matchValue="26" matchMode="4" />-->
				<!--<Condition colname="fcode,sfcode" matchValType="snsoft.ft.match.ColsEqualsMatchValue" negmatch="1" />-->
			<!--</Control>-->
			<ClientInvokers>
				<Invoker code="10">
					new snsoft.ft.comm.cmdreg.CmdCommInvoker({time:4,mode:8})
				</Invoker>
				<Invoker code="20">
					new snsoft.ft.rpadj.pay.EditCcodeInvoker({})
				</Invoker>
			</ClientInvokers>
			<UIInvokers>
				<Invoker code="10" method="invoke">
					#FT-RPADJ.EditCcodeInvoker
				</Invoker>
			</UIInvokers>
		</Command>

	</CommandGroup>
```

### 编辑需要连续弹框 2 个



共享表操作，点击会将主表的值传到主表  invoker 写 `	mainTable.openPopupEdit("test111");`    注意 dialogMode = "true" 才会隐藏 平常不显示

```java
<m:RecordTable dialogMode="true" cellcols="1" layoutm="grid" mainui="ft_rpadj_off" name="test111" region="null" title="编辑姓名-性别">
		<c name="bcode" title="${b_wcode}" sqltype="12" aidInputerBtn="true" cmparams="status:'~10'" cmparams.opids="C,R"
			cmparams.sheetcode="FT-RPADJ.AdvPayOff" codedata="#FT-ORGZ.BWcode" disableed="true" selectMiddle="true" showfname="true"
			showname="true" tipIfOverflow="true" uiprops.textDir="rtl" noblank="true" submitOnInput="true" />
		<c name="wcode" title="${RES.C}" sqltype="12" noblankOnSubmit="true" rdonly="true" codedata="#FT-ORGZ.Wcode" showname="true" hidden="true"/>
		<c name="corpbcode" title="${RES.C}" sqltype="12" codedata="#FT-ORGZ.CorpBcode" cmparams.opids="C"
			cmparams.status="70" showname="true" cmprops.pmFromPane="{fromBcode:'bcode'}" cmparams.fromBtype="&quot;01&quot;"
			disableed="true" noblankOnSubmit="true" noblank="true" submitOnInput="true" selectMiddle="true"  />
		<c name="ccode" title="${RES.C}" sqltype="12" aidInputerBtn="true" aiprops="initParasVales:{status:'70'}" codedata="#FT-CCODE.Ccode"
			disableed="true"  showname="true" noblank="true" submitOnInput="true" />
	</m:RecordTable>
```

### 第二个弹框确认才调用后端

> - 执行顺序：
>   10 check  20check  30check
>   如果 20 里面的 new  dialog  点击否   30 的 check 不会走了
> - 点击是的话  会执行 30 的 check     执行完毕此时 30 的 invoke 里面进行传参到后端，调用后端
> - 如果 invoke 返回 null 也不会调用后端的 apply 方法
> - 但是配置了 3 个前端 invoker  只要有一个就会执行后端

```java
<ClientInvokers>
<Invoker code="10">
    new snsoft.ft.comm.cmdreg.CmdCommInvoker({time:4,mode:8})
</Invoker>
<Invoker code="20">
    new snsoft.ft.rpadj.pay.EditCcodeInvoker({})
</Invoker>
<Invoker code="30">
    new snsoft.ft.rpadj.pay.EditCcodeConfirmInvoker({})
</Invoker>
</ClientInvokers>
```

## 壮哥-公共弹框编辑

```java
<Command cmd="modifyInputMode" title="${FT.cmd_modifyInputMode}">
    <ClientInvokers>
        <Invoker code="05">
            new snsoft.ft.comm.cmdreg.CellsModifyInvoker({cells:'tsininputmode,aogdate,istracpop',noshowedit:false})
        </Invoker>
        <Invoker code="10">
            new snsoft.ft.psp.bas.invoker.DomPurShipInModeInvoker({cells:'tsininputmode',noshowedit:false})
        </Invoker>
        <Invoker code="20">
            new snsoft.ft.comm.cmdreg.CmdCommInvoker({'time':5})
        </Invoker>
    </ClientInvokers>
```



```java
Array<Object> confirms = $a();
confirms.add(showConfirmDialog(event, "条件判断标题", "条件判断是否修改", (InvokerEvent e) -> {
    return event.checkData.hasElement("defvaluesCol") && ((JSObject) (event.checkData.$get("defvaluesCol"))).$get("tsininputmode") == "02";
}, null));
confirms.add(showConfirmDialog(event, "永远弹框标题", "永远弹框内容"));
return (ConfirmRequest) (Object) confirms;
```

## 例子-生成凭证

```java
<Command cmd="createVoucher" pstn="${pstn?bindbar}" title="${SNA.cmd_createVoucher}" btndesc="{tbname:'${tbname?toolbar}'}"  tblname="${mainTblname}"
    reskeys="FT.00000048,SNA-VM.00000010,SNA-VM.00000011,SNA-VM.00000012" checkModified="true" mutipleRows="true" invokeProgress="pane" transDist="true" transTable="${mainTblname}">
    <Control setValue="false" ctrlField="disabled" sheetcode="${sheetcode}" dataOpids="${opids?C}" />
    <ClientInvokers>
        <Invoker code="10">
            new snsoft.sna.vm.autovoucher.invoker.CreateVoucherInvoker({muiid:'${muiid?SNA-VM.VoucherCreateDialog}',vmarkTblName:'${vmarkTblName?}',srciodeFld:'${srciodeFld}',sheetcode:'${sheetcode}',careYM:'${careYM?true}',vksheetcode:'${vksheetcode?}'})
        </Invoker>
        <Invoker code="20">
            new snsoft.sna.vm.autovoucher.invoker.CreateVoucherCmdInovker({time:5})
        </Invoker>
    </ClientInvokers>
    <UIInvokers>
        <Invoker code="10" method="beforeCheck">
            #SNA-VM.CreateVoucherCheckInvoker
        </Invoker>
        <Invoker code="20" method="invoke">
            #SNA-VM.CreateVoucherInvoker
        </Invoker>
    </UIInvokers>
</Command>
```

> 需求背景，想要在生成凭证按钮模板之前进行校验

` <Invoker code="1" method="beforeCheck">`   一开始前后端写的是 method = invoke，这样的话发现执行不到，因为按钮模板里面用的是 beforeCheck  

```java
<Command cmd="createVoucher">
    <ClientInvokers>
        <Invoker code="1">
            new snsoft.ft.loan.dlydfin.app.invoker.CreateVoucherCheckInvoker({})
        </Invoker>

    </ClientInvokers>
    <UIInvokers>
        <Invoker code="1" method="beforeCheck">
            #FT-LOAN.CreateVoucherCheckInvoker
        </Invoker>
    </UIInvokers>
</Command>
```

> 先执行前端所有的 beforecheck  按照 code 顺序执行    执行完毕以后，
>
> 如果后端有对应的 beforeCheck 就执行对应的，假如后端有多个的话也是按照 code 执行
>
> <hr>
>
> 存证这个例子就是先执行
>
>
> 比如同时有 beforeCheck  check    invoke    且后端也有 beforeCheck   invoke
>
> 先执行前端的 beforeCheck   比如有 2 个 beforeCheck  按照 code 顺序执行，
> code 10 = 我自定义的，code 20 = 底层的，   那会执行自定义的校验交单融资   然后执行底层的校验已制证
>
> 如果此时后端有 beforeCheck 且有多个，会按 code 顺序执行，
> 
>
> 执行完校验以后，应该要继续执行前端的 check   此时一般会弹框，弹框如果放弃就不执行后续逻辑了，如果确定，继续执行后端对应的 check（多个按 code 顺序）
>
> 如果后端没有，就就继续执行前端的 invoke（多个按 code 顺序）, 执行完前端就继续执行后端对应的 invoke（多个按 code 顺序）！



## 实际理解

议付交单的删除按钮之前需要检查，所以应该采用 `<Invoker code="5" method="beforeCheck">`  这样可以在 CmdCommInvoker 里面获取参数，然后校验逻辑，报错了的话此时不去走底层的 `snsoft.ext.cmd.com.biz.CommandDelete`

```java
<CommandRef ref="{'FT-TPL.SheetCmdRefTplt':'regDelete'}" />
		<Command cmd="regDelete">
			<Control ctrlField="disabled" setValue="false" sheetcode="${sheetcode}" btnOpids="${opids?C}">
				<Condition colname="status" matchMode="4" matchValue="20"/>
			</Control>
			<ClientInvokers>
				<Invoker code="5">
					new snsoft.ft.comm.cmdreg.CmdCommInvoker({time:5})
				</Invoker>
			</ClientInvokers>
			<UIInvokers>
				<Invoker code="5" method="beforeCheck">
					#FT-DOCU.DelDocuDeliveryInvoker
				</Invoker>
			</UIInvokers>
		</Command>
```

> 承兑赎单删除是在前端校验，虽然 code = 20，但是前端 AccOrdRegDeleteInvoker 写的 method 是 beforeCheck，所以会先执行自定义的 beforeCheck 才去执行底层的 `snsoft.ext.cmd.com.biz.CommandDelete`
>
> **所以不要理解为 code = 20，就一定比 code = 10 的底层方法执行后面，实际上是按照所有的 beforeCheck--> check--> beforeInvoke--> invoke 这样执行**

```java
	<Command cmd="regDelete">
			<Control ctrlField="disabled" setValue="false" sheetcode="${sheetcode}" btnOpids="${opids?C}">
				<Condition colname="status" matchMode="4" matchValue="20"/>
			</Control>
			<ClientInvokers>
				<Invoker code="20">
					new snsoft.ft.docu.accord.invoker.AccOrdRegDeleteInvoker({})
				</Invoker>
			</ClientInvokers>
		</Command>
```



# 单据提交检查

-  cfg/sheet/FT-PDOC.PayDocApp/CHK-SN-APPR.10.xml ---可以进行组合多个 sheetcheckref 的内容，也可以写 tac
- cfg/res/sheetcheckref/FT-PDOC.PayDocAppSubNull.xml--里面指向具体的类
- snsoft.ft.pdoc.app.service.impl.PayDocAppCheckListene



# 查询工具

## 说明

```
<Details>
		<Title>可追溯信用证汇总表-商品名称补数</Title>
		<Remark><![CDATA[
        ]]></Remark>
		<MatchColumns>srcicode,srcsheetcode</MatchColumns>
		<ResultColumns>cnamedesc</ResultColumns>
		<LoadSql><![CDATA[
		select t.lcaicode srcicode,t.sheetcode srcsheetcode,g.cnamedesc cnamedesc from ft_lc_app t,ft_lc_appg g where t.lcaicode=g.lcaicode
		and t.lcaicode in(<infilter>)
		union all
        select t.acdicode srcicode,t.sheetcode srcsheetcode,i.cnamedesc cnamedesc
		from ft_docu_acd t inner join ft_docu_acdg g on t.acdicode=g.acdicode
		left join ft_docu_psg i on i.acdgicode=g.acdgicode
        and t.acdicode in(<infilter>)
		]]></LoadSql>
		<FilterColumn>srcicode</FilterColumn>
		<TableName>ft_lc_appg</TableName>
		<Aggs>
			<ColumnName>srcicode</ColumnName>
			<AggType>0</AggType>
		</Aggs>
		<Aggs>
			<ColumnName>srcsheetcode</ColumnName>
			<AggType>0</AggType>
		</Aggs>
		<Aggs>
			<ColumnName>cnamedesc</ColumnName>
			<AggType>7</AggType>
		</Aggs>
	</Details>
```

< MatchColumns  一般要和 < FilterColumn 一致，但是有的时候，采用 FilterColumn 查出来的没有唯一，可以多加上某些字段让它唯一匹配，

filter 是为了过滤出来，而过滤出来以后要和原来数据匹配，所以会有一个 MatchColumns  ，也是类似分组字段

这个 MatchColumns 的字段正常也是要在 <Aggs> 里面作为分组字段





## 界面查询列补数

### 第一种方式

> 对比第二种应该就是不用写额外的一个 service，同时也不用再 UI Service 里面指定 code 文件

1、界面配置 sqlexpr = null

```java
<c name="bcode" title="${RES.C?业务员部门}" sqltype="12" sqlexpr="null"   rdonly="true" codedata="#FT-ORGZ.BWcode" cmparams.sheetcode="${sheetcode}" cmparams.opids="R,C"
					showname="true" disableed="true" uiprops.renderer="new snsoft.plat.busi.comm.BusiBWcodeNameRender({})" />
```

2、`cfg/res/qrytool/FT-DOCU.DocuAccOrdEQR.xml`  配置查询工具  以	**QR 结尾**

```java
	<Details>
		<Title>承兑赎单工作台</Title>
		<Remark><![CDATA[
		]]></Remark>
		<MatchColumns>prjicode</MatchColumns>
		<ResultColumns>prjicode,tradetype,bcode,wcode</ResultColumns>
		<LoadSql><![CDATA[
            select prjicode,wcode,bcode,tradetype from ft_prj_prj where prjicode in (<infilter>)
		]]></LoadSql>
		<FilterColumn>prjicode</FilterColumn>
		<TableName>ft_docu_psacd_cb</TableName>
	</Details>
```

3、 [FN-ui.workbench.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\sheet\FT-DOCU.DocuAccOrd\FN-ui.workbench.xml)   配置即可

```java
<!-- 补数 -->
		<bean code="FT-DOCU.40" sortidx="40" impl="#SN-PLAT.QueryToolUIListener?[{qrycode:'FT-DOCU.DocuAccOrdWCQR'}]"/>
```

### 第二种方式

这个补数的类是`#SN-PLAT.QueryToolQRListener` 而下面的是

```
<bean code="FT-LOAN.10" sortidx="10" impl="#SN-PLAT.QueryToolQRListener?[{qrycode:'FT-LOAN.DocuDeliveryFinaWorkBenchQR'}]"/>
```



```
<bean code="FT-LOAN.DocuDeliveryFinaApp.40" sortidx="40" impl="#SN-PLAT.QueryToolUIListener?[{tgtUINames:['ft_loan_dlydfali_view','ft_loan_dlydfari_view'],qrycode:'FT-DOCU.DocuDeliveryFinaAppVMQR'}]"/>
```



> 参考  交单融资处理工作台
> [FN-sv.workbench.query.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\sheet\FT-LOAN.DocuDeliveryFinaApp\FN-sv.workbench.query.xml) 

```java
涉及文件：
1、界面配置的sqlexpr=null 
2、FT-LOAN.DocuDeliveryFinaWorkBenchQR.xml
3、FN-sv.workbench.query.xml指向上面第2部的DocuDeliveryFinaWorkBenchQR.xml
4、DocuDeliveryFinaAppServiceImpl#queryLendInfoWorkbenchDetail的查询code指向第3部的sv.workbench.query    
```

1、界面配置 sqlexpr = null 同理

2、  [FT-LOAN.DocuDeliveryFinaWorkBenchQR.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\res\qrytool\FT-LOAN.DocuDeliveryFinaWorkBenchQR.xml)   里面配置不太一样

通过 loadService 方式 ` #FT-LOAN.DocuDeliveryFinaHandleQryToolService`  

```java
	<Details>
		<Title>交单融资处理工作台字段补数</Title>
		<Remark><![CDATA[
		交单融资处理工作台字段补数
		]]></Remark>
		<MatchColumns>lrpicode</MatchColumns>
		<ResultColumns>
			irtfcy,feefcy
		</ResultColumns>
		<LoadImpl><![CDATA[
		    #FT-LOAN.DocuDeliveryFinaHandleQryToolService
		]]></LoadImpl>
	</Details>
```

3、监听【配置在额外的地方 `cfg/sheet/FT-LOAN.DocuDeliveryFinaApp/FN-sv.workbench.query.xml`

```java
<function xmlns="http://www.snsoft.com.cn/schema/SheetConfig" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<name>【交单融资处理工作台】补数</name>
	<remark><![CDATA[
    ]]></remark>
	<properties>
		<property name="sheetcode" value="FT-LOAN.DocuDeliveryFinaApp" />
	</properties>
	<beans>
		<!--补数-->
		<bean code="FT-LOAN.10" sortidx="10" impl="#SN-PLAT.QueryToolQRListener?[{qrycode:'FT-LOAN.DocuDeliveryFinaWorkBenchQR'}]"/>
	</beans>
</function>

```

4、工作台查询配置要指定 `"sv.workbench.query"`

snsoft.ft.loan.dlydfin.service.impl.DocuDeliveryFinaAppServiceImpl#queryLendInfoWorkbenchDetail

```java
@Override
public QueryResults<Lrpclient> queryLendInfoWorkbenchDetail(QueryHandleWorkbenchLendDetailParams params)
{
    return funcQuery(params, "sv.workbench.query", Lrpclient.class);
}
```

> 注意这个sv.workbench.query 是怎么关联到`cfg/sheet/FT-LOAN.DocuDeliveryFinaApp/FN-ui.workbench.xml`下面的补数文件，而不是去找`cfg/sheet/FT-LOAN.DocuDeliveryFinaLI/FN-ui.handleworkbench.xml` 这个补数，是通过
> snsoft.plat.bas.busi.service.impl.AbstractFunctionService#funcQuery-->`getQueryListeners` 去获取当前Service上面的单据号，也就是`
>
> ```
> public class DocuDeliveryFinaAppServiceImpl extends FTBusiAccessServiceImpl<DocuDeliveryFinaApp> implements DocuDeliveryFinaAppService
> `
> ```

```
	public <T> List<FunctionQueryListener<T>> getQueryListeners(String funccode)
	{
		if (funccode == null)
			return Collections.emptyList();
		String sheetcode = getSheetCode();
		if(sheetcode == null)
			throw new InfoException("找不到单据号[@SheetInfo(...)]："+getClass().getName());
		return (List<FunctionQueryListener<T>>)SheetConfigService.impl.parseFunctionListeners(sheetcode, funccode);
		/*List<String> lines = SheetConfigService.impl.getFunctionListeners(getSheetCode(), funccode);
		return (List<FunctionQueryListener<T>>) (List<?>) ListenerParser.parser().parseListeners(lines);*/
		/*EventListener[] listeners = getListeners(funccode, "SN-PLAT.QR");
		List<FunctionQueryListener<T>> list = new ArrayList<>();
		for (EventListener lis : listeners)
		{
			list.add((FunctionQueryListener<T>) lis);
		}
		return list;*/
	}
```



### 第三种方式

这个补数的类是`#SN-PLAT.QueryToolQRListener` 而下面的是

```
<bean code="FT-LOAN.10" sortidx="10" impl="#SN-PLAT.QueryToolQRListener?[{qrycode:'FT-LOAN.DocuDeliveryFinaWorkBenchQR'}]"/>
```

```
<bean code="FT-LOAN.DocuDeliveryFinaApp.40" sortidx="40" impl="#SN-PLAT.QueryToolUIListener?[{tgtUINames:['ft_loan_dlydfali_view','ft_loan_dlydfari_view'],qrycode:'FT-DOCU.DocuDeliveryFinaAppVMQR'}]"/>
```



>  [FT-DOCU.DocuDeliveryFinaAppVMQR.xml](D:\snsoftn10\CODE\xyerp\xyft\parents-xyft-java\parents-xyft-fund-java\xyft-fund\src\main\resources\cfg\res\qrytool\FT-DOCU.DocuDeliveryFinaAppVMQR.xml) 

参考这个，也不需要再 UIService 指向这个 code，同时也不需要额外定义一个 FN-sv.workbench.query.xml 指向这个 QR 文件，并且 Service 里面入参是前端参数！（**前端补数？？？**）

```java
涉及文件：
1、界面sqlexet=null
2、FT-DOCU.DocuDeliveryFinaAppVMQR.xml
3、FN-ui.handleworkbench.xml指向第2个的文件    
```



## 查询条件关联补数

> 查询条件的业务员查询时，如果对应下面的结果里面的业务员也是补数来的，就需要像下面一样配置

承兑赎单工作台补数：`cfg/ui/res/FT-DOCU/DocuAccOrdWorkbench.xml`

```java
<!--业务员、业务员部门 需要通过补数来处理-->
		<c name="wcode" title="${RES.C?业务员}" sqltype="12" sqlexpr="null"  width="${G.CW.ucode}" codedata="#sheet.wcode" showname="true"/>
		<c name="bcode" title="${RES.C?业务员部门}" sqltype="12" sqlexpr="null"  width="${E.G.CW.bcode}" codedata="#sheet.bcode" showname="true"/>
```

`snsoft.ft.docu.accord.service.DocuAccOrdService.EntryDlgParams`

```java
/** 部门-业务员 */
@SubColumn(table = "ft_prj_prj", pJoinColumn = "prjicode", sqlColumn = @SqlColumn(column = "bcode"))
private              String     bcode;
/** 业务员 */
@SubColumn(table = "ft_prj_prj", pJoinColumn = "prjicode", sqlColumn = @SqlColumn(column = "wcode"))
private              String     wcode;
```





# 拷贝核销     

> - json 字段影响界面
> - @column 要写
> - asbean 要写 service 而不是单据号
> - 模板分组字段：  select   a, max(b), sum(c), avg(d)   from table  group by  a

- **拷贝 datacopy：** datacopy-FT-DOCU.DocuAccOrdByPurShip.xml ----共下面 3 个配置项

  - `DocuAccOrdByPurShipCopyDataLoader`: 拷贝选中勾选值校验、除配置外的逻辑补充
  - `DocuAccOrdByPurShipCopyDataListener`：拷贝选中勾选值数据加载
  - 目标表 = 源表   拷贝关系配置：`ft_docu_acd=ft_docu_psacd_cb:*,-$sys;`

- **核销 cor：** FT-DOCU.DocuAccOrdByPurShip.xml    

  ```java
  <SourceBean>FT-PDOC.DocuAccOrdCorBSourceService</SourceBean><!--生成余额监听-->
  <BalanceClass>snsoft.ft.docu.accord.vo.PurShipForDocuAccOrdCorBalance</BalanceClass><!--余额对象-->
  <TraceClass>snsoft.ft.docu.accord.vo.PurShipForDocuAccOrdCorTrace</TraceClass><!--目标对象-->
  <SourceTable>ft_psp_psst</SourceTable>  上游核销--对应当前的列表
  <CorRelaField>purshipssicode</CorRelaField>  核销关联关系字段
  <CorFields>fcy</CorFields>   核销字段
  ```

  - `DocuAccOrdCorBSourceService-loadSource核销来源加载`
  - VO:    ` PurShipForDocuAccOrdCorBalance-CB余额表    PurShipForDocuAccOrdCorTrace-CT核销记录表`

  

  

- DocuAccOrdCorBSourceService-loadSource 核销来源加载 	
- `DocuAccOrdByPurShipCopyDataLoader`: 拷贝选中勾选值校验、除配置外的逻辑补充
- PurShipForDocuAccOrdCorTrace-CT 核销记录表        
- DocuAccOrdByPurShipCopyDataListener-校验、逻辑补充        
- PurShipForDocuAccOrdCorBalance-CB 余额表        
- cor-FT-DOCU.DocuAccOrdByPurShip.xml         
- datacopy-FT-DOCU.DocuAccOrdByPurShip.xml         
- FN-sv.save.xml
- FN-ui.detail.xml
- FN-ui.workbench.xml



## 工作台

### 按钮注册

```java
FTSheetDataCopyListener：
/**
	 * 	拷贝模式：用于区分不同的拷贝方式【同一个地方的模式一定相同】-不配置的默认入口拷贝
	 *  10：入口拷贝  20：单据内拷贝  30：工作台拷贝 40：生成下级单据
	 */
	protected final String                   copyMode;
```



```java
DocuAccOrdWorkbench.xml：
<CommandGroup uiname="ft_docu_psacd_cb" >
		<!-- 生成承兑赎单 -->
		<Command cmd="createApp" title="${title_FT-DOCU.createApp?生成承兑赎单}" pstn="bindbar" btndesc="{tbname:'btnGroup'}" checkModified="true"
			tblname="${mainTblname}">
			<ClientInvokers>
				<Invoker code="10">
					new snsoft.ft.comm.cmdreg.SheetDataCopyInvoker({copyMode:'30',commands:[{command:'createApp',isToolHidden:'false',cpid:'FT-DOCU.DocuAccOrdByPurShip',muiid:'FT-DOCU.DocuAccOrdWorkbench',sheetCode:'${sheetcode}',copytype:'20',checkCols:['corpbcode','ccode','fcode','lccode','paymode','domabr']}]})
				</Invoker>
			</ClientInvokers>
		</Command>
	</CommandGroup>
```

### 分组

> 注意分组展示的所有字段一定要在原来的原型（按明细展示）有定义展示，不然分组的时候会出不来！显示有问题

- 加上这三个属性，同时需要注意 `attachTbName="toolbar"`  要对应上面的 Toolbar 按钮名字，不然出不来！

```java
<m:Toolbar name="toolbar" uiprops.cellClassName="toolbar-panel" region="north"></m:Toolbar>
```

```java
uiprops.attachTbName="toolbar" groupData="true" xprops="GrpParamName:'按付款申请单号'"
```

- 监听配置加上：

  ```
  @lib:table/tools/TableGroupData
  #new snsoft.ft.comm.busi.SelectGroupJSListener({})
  ```

- 还需要 `FN-ui.workbench.xml` 配置按钮注册，同时监听加上？

```java
<!-- 分析模板 -->
<bean code="FT-RPADJ.40" sortidx="40" impl="#new snsoft.ft.comm.busi.SelectGroupJSListener({})"/>
```

- 最后还要配置 `cfg/ui/grppm/FT-RPADJ.AddAdvanceWorkBench.xml`
  - "groupType": 分组类型，比如分组、最大最小求和等
  - groupFlags：列的选项，隐藏，交叉，升序等
  - groupLevl： 除了分组类型是 1，其他都是 0？
  - 隐藏字段也要配置
  - 这个顺序会影响界面的展示顺序？？





## 入口

### 按钮注册

同工作台一样配置：

### datacopy 配置-关键

> - DocuAccOrdByPurShipCopyDataLoader 根据核销内码加载选中的数据行，然后数据拷贝的时候就是根据这个数据行的值，再进行拷贝关系 ft_docu_acd = ft_docu_psacd_cb: 进行拷贝
>
> - 最后可以在 DocuAccOrdByPurShipCopyDataListener 监听里面拿取到这个值，再次进行修改填充一些额外的值
> - 已有内码追加金额 `ft_rpadj_offg:fcy=payfcy`  `  **由于我要将界面手动输入的本次使用金额的值赋值给冲销金额，所以在 DocuAccOrdByPurShipCopyDataLoader 里面额外将界面传入的 currentfcy 塞进去，接着通过这个配置进行拷贝**

`cfg/res/datacopy/FT-DOCU.DocuAccOrdByPurShip.xml`

- ft_docu_acd = ft_docu_psacd_cb:    也可以配置 view 视图？？
- 

```java
<Attrs>
		<Aname>srcbean</Aname>
		<Stdvalue>FT-DOCU.DocuAccOrdByPurShipCopyDataLoader</Stdvalue>
	</Attrs>
	<Attrs>
		<Aname>sheetcode</Aname>
		<Stdvalue>FT-DOCU.DocuAccOrd</Stdvalue>
	</Attrs>
	<Attrs>
		<Aname>listeners</Aname>
		<Stdvalue><![CDATA[snsoft.ft.docu.accord.service.impl.DocuAccOrdByPurShipCopyDataListener.new]]>		  </Stdvalue>	
	</Attrs>
    <Attrs>
		<!--累加字段信息-->
		<Aname>accfldinfo</Aname>
		<Stdvalue><![CDATA[ft_rpadj_offg:fcy=payfcy]]></Stdvalue>
	</Attrs>
	<Attrs>
		<!--子表关系字段:核销码-->
		<Aname>gtablejoincolumn</Aname>
		<Stdvalue><![CDATA[ft_rpadj_offg.fpsgicoder=ft_pay_fpsg.fpsgicoder]]></Stdvalue>
	</Attrs>
	<Attrs>
		<!-- 目标表=源表 -->
		<Aname>copyfldinfo</Aname>
		<Stdvalue><![CDATA[
            ft_docu_acd=ft_docu_psacd_cb:
                *,
                -$sys;
            ft_docu_acdg=ft_docu_psacd_cb:
                *,
                -$sys,
                fcy=fcying;
		]]></Stdvalue>
	</Attrs>
```

```java
PurShipForDocuAccOrdCorBalance 到货单对承兑赎单明细核销余额表
PurShipForDocuAccOrdCorTrace  到货单对承兑赎单明细核销记录表
```

> `ft_rpadj_offg:fcy=payfcy  `  **由于我要将界面手动输入的本次使用金额的值赋值给冲销金额，所以在 DocuAccOrdByPurShipCopyDataLoader 里面额外将界面传入的 currentfcy 塞进去，接着通过这个配置进行拷贝**

```java
queryList.forEach(finPayDetailShare -> finPayDetailShare.setPayfcy(BigUtils.o2Big(srcParams.get(finPayDetailShare.getFpsgicoder()).get("currentfcy"))));
```



#### 配置说明：

```java
FT-DOCU.DocuAccOrdByPurShipCopyDataLoader：加载勾选的具体数据
snsoft.ft.docu.accord.service.impl.DocuAccOrdByPurShipCopyDataListener.new：
beforeSave：拷贝前逻辑补充  --比如无法通过配置拷贝的
afterLoad：拷贝前数据校验---点击确定？
```

左边目标表，也就是下游单据，

```
<!-- 目标表=源表 -->
<Aname>copyfldinfo</Aname>
<Stdvalue><![CDATA[
          ft_docu_acd=ft_docu_psacd_cb:
              *,
              -$sys;
          ft_docu_acdg=ft_docu_psacd_cb:
              *,
              -$sys,
              fcy=fcying;
]]></Stdvalue>
```

### cor 配置--关键

> 核销关系扣减：
>
> - cb-余额表，ct-多次核销记录表
> - 核销扣减关系主要是通过 DocuAccOrdCorTargetServiceImpl#loadTarget 加载到 g 表里面的数据的 fcy 进行扣减数量，
>
> **一开始金额错乱是因为 ft_rpadj_offg = ft_pay_fpsg_cb:   直接将 cb 表里面的 fcy 拷贝到了 g 表的 fcy, 然后查询出来的数据就是 `select g.offgicode tgticode,'ft_rpadj_offg' tgttbl,g.fpsgicoder,g.fcy as fcy`   这样把 g.fcy 复制给了 ct 表的 fcy 了，导致它扣减的其实是 ft_pay_fpsg_cb 里面的 fcy，但是其实首先这个金额是不应该直接拷贝的 `-fcy,`，应该通过界面手动输入的值去扣减，所以应该在 before save 监听前手动塞进去界面的值，然后拷贝完核销的时候，拿取到这个 fcy 查出来的就是正确的-即手动输入的值，然后以这个值去作为核销记录！！！**
>
> ```java
> advpayoffdetail.setFcy(BigUtils.add(BigUtils.o2Big(advpayoffdetail.getFcy()), BigUtils.o2Big(extParams.get("currentfcy"))))
> ```
>
> 

`cfg/res/cor/FT-DOCU.DocuAccOrdCor.xml`

> reactivebal  一般是 N，被动拷贝核销    通过手动操作！

```java
reactivebal="N" >
	<Name>【承兑赎单】余额核销</Name>
	<SourceBean>FT-PDOC.DocuAccOrdCorBSourceService</SourceBean><!--生成余额监听-->
	<BalanceClass>snsoft.ft.docu.accord.vo.PurShipForDocuAccOrdCorBalance</BalanceClass><!--余额对象-->
	<TraceClass>snsoft.ft.docu.accord.vo.PurShipForDocuAccOrdCorTrace</TraceClass><!--目标对象-->
	<SourceTable>ft_psp_psst</SourceTable>
	<CorRelaField>purshipssicode</CorRelaField>
	<CorFields>fcy</CorFields>
	<Flags>0</Flags>
	<Remark><![CDATA[
        1、承兑赎单对象核销；
        2、负责人：王乾坤；
	]]></Remark>
	<Targets>
		<TargetTable>ft_docu_acdg</TargetTable>
		<TargetBean>FT-DOCU.DocuAccOrdCorTargetService</TargetBean>
		<Remark>目标</Remark>
	</Targets>
```

- **上游使用的: loadSource 核销来源加载**

```java
<SourceBean>FT-PDOC.DocuAccOrdCorBSourceService</SourceBean><!--生成余额监听-->
snsoft.ft.docu.sqlm.service.DocuSqlMapper#loadDocuAccOrdCorBalance
由于上游核销调整成《采购到货单付款方式分摊表》，但表还未设计出来，故这边先保留，以及部门、人员、是否代开证、合同公司，后续再调整
```



- **SourceTable 来源于付款方式，文档说明，purshipssicode 采购到货单付款方式分摊内码   CorFields 核销字段是 fcy 金额**

```java
<SourceTable>ft_psp_psst</SourceTable>
<CorRelaField>purshipssicode</CorRelaField>
<CorFields>fcy</CorFields>
```

**核销的目标**     核销是以明细维度拷贝核销的，ft_docu_acdg 对应承兑明细表

```
<Targets>
    <TargetTable>ft_docu_acdg</TargetTable>
    <TargetBean>FT-DOCU.DocuAccOrdCorTargetService</TargetBean>
    <Remark>目标</Remark>
</Targets>
```

`snsoft.ft.docu.accord.service.impl.DocuAccOrdCorTargetServiceImpl#loadTarget`  实际 SQL

```java
<!--【承兑赎单】核销记录表加载 -->
	<mapper method="loadDocuAccOrdCorTrace">
		<sql>
			select
			g.acdgicode tgticode,'ft_docu_acdg' tgttbl,g.purshipsicode,g.fcy as fcy
			from
			ft_docu_acd m join ft_docu_acdg g on m.acdicode=g.acdicode
			where
			g.acdgicode in (:tgticodes) and g.purshipsicode is not null
		</sql>
	</mapper>
```

对应的 tgticodes 就是这个监听里面配置的 `tgtInnerName:'acdgicode'`

```java
<!-- 核销 -->
<bean code="FT-DOCU.160" sortidx="160" impl="#new snsoft.plat.bas.cor.CorTargetCheckJSListener({tgtUINames:['ft_docu_acdg'],corcode:'FT-PDOC.PayDocAppCor',corrfld:'purshipssicode',corfld:'fcy',tgtInnerName:'acdgicode'})"/>
```

## 问题

- 我的是视图，如何配置拷贝？  ---只在查询用视图，然后 dataload 查的是 fpsg，拷贝配置也是用 fpsg---当然其实也可以用视图？
- 我的是视图，如何配置核销？  ---核销一切正常，都是配置 CB  CT 两个表，sourcetable 也是 ft_pay_fpsg
- 界面手动输入的值如何传值进行核销，currentfcy 在 dataload 手动塞进去到一个不要用的列 pay_fcy，拷贝配置去配置这个 pay_fcy 是累加字段，表.AC
- 如何追加核销？拷贝配置去配置这个 pay_fcy 是累加字段，表.AC
- 分析模板配置：attachname 取的是 toolbar 的 name  

## 其他

1、拷贝是 cb 表且带了业务数据---跨服务：议付交单拷贝发货单销售方式

```java
	<Stdvalue><![CDATA[
            ft_docu_dlyd=ft_docu_dlydg_cb:
                *,
                -$sys;
            ft_docu_dlydg=ft_docu_dlydg_cb:
                *,
                -$sys,
                recfcy=fcy,
                fcy=fcying;
		]]></Stdvalue>
```

2、只拷贝不核销，采用视图，load 里面加载的是主表，拷贝定义可以直接获取子表同名拷贝？---是因为主表关联到了子表吧？

> 交单融资拷贝议付交单

需要注意 dlydfcy、dlydbankcode 这种拷贝的是预付交单的对应不同名字段，同时还要设置不拷贝-fcy, -bankcode,

```java
List<DocuDelivery> queryList = DAO.newInstance(DocuDelivery.class)
.queryList(SqlExpr.columnInValues("dlydicode", srcParams.keySet().toArray(new String[0])), true, true);

	<Stdvalue><![CDATA[
            ft_loan_dlydfa=ft_docu_dlyd:
                *,
                dlydfcy=fcy,
                dlydbankcode=bankcode,
                -fcy,
                -bankcode,
                -adate,
                -remark,
                -$sys;
            ft_loan_dlydfag=ft_docu_dlydg:
                *,
                dlydfcy=fcy,
                -$sys;
		]]></Stdvalue>
```

3、孙表之间的拷贝，基于实付分摊明细拷贝-追加预付

> 预付冲销单子表拷贝

```java
<Stdvalue><![CDATA[
            ft_rpadj_offg.AC=ft_pay_fpsg:
                *,
                -$sys,
                -fcy,
                -fcyed,
                -payfcy,
                -scy,
                -zcny,
                -zusd,
                -fserate,
                -scerate,
                -suerate,
                -fzerate,
                tcaptime=paydate,
                srccode=pacode,
                srcicode=paicode;
          
		]]></Stdvalue>
```

## 拷贝-单据内明细累加金额

> 议付交单明细内追加，此时交单金额可变，所以可以分批追加，对于同一个核销内码是进行累加金额，而不是新增到另外一行
>
> gtablejoincolumn： 配置的是核销关系，而不是主子表关联关系，一开始配置成了主子表关联关系！
>
> accfldinfo：配置累加金额
>
> **此时不需要写任何代码，只需要这个配置即可实现**

> **如果是预付冲销单那种，在拷贝新建弹出的工作台里面需要手动输入金额核销的话，就需要额外用另外一个字段 payfcy 占用配置，同时在 DocuDeliveryBySalShipCopyDataListener 这里面额外处理--具体参考预付冲销单实现**

```java
	<Attrs>
		<Aname>accfldinfo</Aname>
		<Stdvalue><![CDATA[
            ft_docu_dlydg:fcy=fcying
        ]]></Stdvalue>
	</Attrs>

	<Attrs>
		<Aname>gtablejoincolumn</Aname>
		<Stdvalue><![CDATA[ft_docu_dlydg.salshipssicoder=ft_docu_dlydg_cb.salshipssicoder]]></Stdvalue>
	</Attrs>

	<Attrs>
		<!-- 目标表=源表 -->
		<Aname>copyfldinfo</Aname>
		<Stdvalue><![CDATA[
            ft_docu_dlyd=ft_docu_dlydg_cb:
                *,
                -$sys;
            ft_docu_dlydg.AC=ft_docu_dlydg_cb:
                *,
                -$sys,
                recfcy=fcy,
                fcy=fcying;
		]]></Stdvalue>
	</Attrs>
```

## 超额核销

```java
<Flags>1</Flags>
```

## 为什么是拷贝 fpsg_cb_view（cb/ct 表理解）

为什么不直接拷贝 fpsg 付款实付记录表数据，而是要弄一张 cb 表，因为考虑到可以多次拷贝核销，相当于有一个核销记录表，如果直接拷贝原表，相当于每次全量拷贝，并且不核销的情况才可以，类似于交单融资拷贝议付交单

再比如一种是跨服务的拷贝 cb 表，比如议付交单的拷贝新建来源是销售发货单收付款方式分摊表，不可能直接访问上游的这个分摊表，而是要基于这个分摊表生成对应的 cb 核销表，从这个核销表里面多次拷贝核销



对于每次拷贝新建，涉及的计划执行金额 fcy，待执行金额 fcying，已执行金额 fcyed------fcy = fcyed+fcying；新建单据默认是全量的计划执行金额，有的可以修改待执行金额，新建完单据是会插入一条 ct 表记录，类似日志记录，记录内容是本次用了多少金额，

假设比如总的计划执行金额是 fcy = 100；第一次拷贝输入的金额是 10 那就是生成一条 10 金额 fcy 的 ct 表记录，此时 fcying = 10，如果再次拷贝新建一条输入金额 20；此时 fcying = 10+20（是累加的！）这个输入的金额对应的是单据里面明细的 fcy，比如交单明细的 fcy 作为插入 ct 表的记录，注意如果针对同一条交单明细一直修改金额，改的也是对应的 ct 表的金额，而不会新增一条 ct 记录，除非再次针对这个 cb 表的行记录拷贝新建一个单据，就会生成新的一个 ct 记录

```
select
g.dlydgicode tgticode,'ft_docu_dlydg' tgttbl,g.salshipssicoder,g.fcy as fcy
from
ft_docu_dlyd m join ft_docu_dlydg g on m.dlydicode=g.dlydicode
where
g.dlydgicode in (:tgticodes) and g.salshipssicoder is not null
```







## 往来理解

> 议付交单生效生成的应收往来，交单融资无追从银行放款生成实收往来，要对之前议付交单生成的应收往来进行核销

```java

cb
lrpicodex  fcy  fcyed  fcying
L001        1000   0    1000
cb表始终是更新：
L001        1000   100   900
L001        1000   300   700  
L001        1000   200   800     

ct
tgicode  lrpicodex   fcy
 icode1	   L001      100
 icode2	   L001      200
 icode     L001     -100
```

```java
select
    lrpicode as tgticode, 'ft_lrp_lrp' as tgttbl, lrpicodex, fcy, scy, zcny, zusd
    from ft_lrp_lrp
    where
    lrpicode in (?)
```

放款取消，往来要删除，所以核销 mode 应该也是-1 删除

```java
FundUtils.dealTargetCor("FT-DOCU.DocuDeliveryLrpCor", delLrpclients, "lrpicode", "lrpicodex", -1); 执行这个sql如下 
delete  ft_lrp_lrp_ct
update  ft_lrp_lrp_cb
```



红冲放款，新增一条负数的往来，所以核销 mode 也是 1 插入新增







# 主子孙关联查询配置

**fmainui 对应 cpfmastercol  是子表的**

```java
mainui="ft_rpadj_off" fmainui="ft_rpadj_offg"

<c name="officode" sqltype="12" width="${E.G.CW.code}" xprops.cpmastercol=":officode" hidden="true"/>
<c name="offgicode" sqltype="12" width="${E.G.CW.code}" xprops.cpfmastercol=":offgicode" hidden="true" />
```

- 查询孙表数据的查询参数，要多一个子表的内码，之前只配置了主表的
- compiled = true，不能配置 precompiled  不然 null 的参数会变成 and  offgicode is null 传进去  导致查询为空

```java
class DetailAdvPayOffgsParams extends QueryParams
	{
		@Serial
		private static final long   serialVersionUID = -1601623358455115497L;
		/** 冲销单内码 */
		@SqlColumn(column = "officode", compiled = true)
		private              String officode;
		@SqlColumn(column = "offgicode", compiled = true)
		private              String offgicode;
	}
```

实体类配置：拷贝 主子表内码

```java
/**调整/冲销单子表内码*/
@Column
@DefaultValue("CopyMaster:offgicode")
private String				offgicode;
/**调整/冲销单内码*/
@Column
@DefaultValue("CopyMaster:officode")
@NotNull
private String				officode;
```

### 保存要额外配置

要配置强制存盘字段，不然带不过去！！

```java
dsprops="forceSaveColumns:['offgicode']"
```



### 入口查询多选--子表配置

查询参数的收款方式、货前后等是来源于子表的列，同时这三个都是属于码表，需要支持多选，此时配置成

```java
//收款方式
@SubColumn(table = DocuDeliveryg.TABLE_NAME, pJoinColumn = "dlydicode", sqlColumn = @SqlColumn( column = "paymode"))
private String[] paymode;
```

同时列表页的收款方式也是码表，多个值显示，要配置 `mutiple="true"`



# 新单据工具使用

> http://127.0.0.1:8081/XYFT/uiinvoke/00/zh_CN/theme0/FT-DEV.CreateFiles.html?_v = 1723517242908

```java
{
 "tblname":"ft_docu_dlyd",
 "voname":"DocuDelivery",
 "details":[
     {
         "tblname":"ft_docu_dlydg",
         "voname":"DocuDeliveryg",
         "multiple":true,
         "fldname":"docudeliverygs"
     },
	  {
         "tblname":"ft_docu_dlydb",
         "voname":"DocuDeliveryBol",
         "multiple":true,
         "fldname":"docudeliverybols"
     },
	  {
         "tblname":"ft_docu_dlyds",
         "voname":"DocuDeliverySt",
         "multiple":true,
         "fldname":"docudeliverysts"
     },
	  {
         "tblname":"ft_docu_dlydc",
         "voname":"DocuDeliveryAc",
         "multiple":true,
         "fldname":"docudeliveryacs"
		
     },
	  {
         "tblname":"ft_docu_dlydf",
         "voname":"DocuDeliveryFc",
         "multiple":true,
         "fldname":"docudeliveryfcs"
     },
	  {
         "tblname":"ft_docu_dlydr",
         "voname":"DocuDeliveryDr",
         "multiple":true,
         "fldname":"docudeliverydrs"
     }
 ]
}
```

修改点：

- VO: 主键设置---copymaster---idx--fcy 金额
- 在接口加了注解@SpringBean@Service(DocuDeliveryService.BeanName)
- 要 extends FTSheetVO



# 接口调用

> http://127.0.0.1:8081/XYFT/access/OT-ATS/FT-FUND/cancelDocuAccOrd

```
	{
	"docuDeliveryConfirmInfo": {
		"confirmtype": "1",
		"operator": "C00000000100019",
		"rdate": "1725607869",
		"odate": "1725607011",
		"dlydicode": "66b4245f2ef5ff4550284210"
	},
	"commonParameters": {
		"pageTotal": "1",
		"docType": "xxx",
		"docCode": "xxx",
		"source": "ATS",
		"target": "CTRM",
		"pageNo": "1"
	}
}
```

# 红冲、红蓝

- 默认的拷贝新建以后，提交生效 rbflag 就是 0
- 普通的红冲操作，提交生效以后，rbflag = 1

- 红冲生成新单据：此时有 3 个单据，红冲、被红冲、新生成的蓝单，蓝单生效以后就是 3，红冲的单据就是 2

```java
/**红蓝标识
	 *"0：蓝单
     * 1：红单
	 * 2：红蓝-红单
	 * 3：红蓝-蓝单"
	 * */
@Column
private Integer							rbflag;
```

```java
/**
	 * 红冲标识列名
	 */
	String col_flag = "redflag";

	/**
	 * 正常(蓝)
	 */
	int    Red_0    = 0;

	/**
	 * 红冲原始单据
	 */
	int    Red_1    = 1;

	/**
	 * 红蓝原始单据
	 */
	int    Red_4    = 4;

	/**
	 * 红票
	 */
	int    Red_2    = 2;
```

红蓝生成新单据才会有重复对某条记录红冲?   这时候 o 字段记录的是当前单据关联的上一条的内码，而 r 字段是最原始的！

<img src="E:\Apple\TyporaMD\Img\image-20241107191707110.png" alt="image-20241107191707110" style="zoom:67%;" />

# 码表

码表过滤

```java
aiprops.dlgParam="{bcode:'ft_docu_dlyd.bcode'}" 
```

码表赋值

```java
aiprops.copyMap="{bolcode:'bolcode'}"
```

码表过滤+锁死参数只读

```java
<c name="logistcode" title="${RES.C}" sqltype="12" aidInputerBtn="true" codedata="#FT-LOGI.StowageTrack" disableed="true"
    showname="true" cmparams.sheetcode="FT-DOCU.DocuDelivery"  cmparams.opids="R,C" rdonly="true"
    aiprops.copyMap="{bolcode:'bolcode'}" aiprops="paramItemsCtrl:{'ccode':{'readOnly':true},'bcode':{'readOnly':true},'corpbcode':{'readOnly':true}},
    dlgParam:{ccode:'ft_docu_dlyd.salccode',bcode:'ft_docu_dlyd.bcode',corpbcode:'ft_docu_dlyd.corpbcode'}" />
```

> 添加提单号，添加客商，通过 ClickPullDownInvoker 监听 模拟点击配置好的列码表实现：所以在界面 ui 配置好码表即可！

```java
new snsoft.ext.cmd.com.biz.ClickPullDownInvoker({'tCell':'logistcode', 'autoSave': false})
```

# 同审

```
公共
同审关系表 （FUND已支持，其他服务各自改造）
<columns cpFromTable="appr_tgbs_tplt"/>

同审关系数据生成
各个模块配置单据生成同审关系配置：cfg/res/tgbsrele 参照付款申请（/resources/cfg/res/tgbsrele/FT-PAY.xml）



业务单据添加字段
主表VO实现接口：FundApprTgb
子表VO实现接口：FundApprTgbDetail

主表添加同审字段，详情界面布置隐藏：
<column name="tgbicode" title="同审单内码" type="VARCHAR(SZIBILL)"/>
<column name="tgbcode" title="同审单号" type="VARCHAR(SZNBILL)"/>


子表（实现 FundApprTgbDetail 接口）添加字段，详情界面布置隐藏，拷贝来源单据时赋值对应单据值
<column name="srcsheetcode" title="来源单据类型" type="VARCHAR(SZSHEET)"/>
<column name="srccode" title="来源单据号" type="VARCHAR(SZNBILL)"/>
<column name="srcicode" title="来源单据内码" type="VARCHAR(SZIBILL)"/>
<column name="srcgicode" title="来源单据子表内码" type="VARCHAR(SZIBILL)"/>
<column name="srcmodifydate" title="来源单据修改时间" type="DATE"/>
<column name="srcstatus" title="来源单据状态" type="VARCHAR(SZSTATUS)"/>


业务配置：
状态：添加同审状态：26

详情监听
<bean code="FT-APPR.190" sortidx="190" impl="#FT-PLAT.FTApprTgbSheetDetailUIListener?[{sheetCode:'${sheetcode}'}]"/>

按钮模板
<CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiSubmitGroup'}" />
<CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiApprSubmit'}" />
<CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiTgbSubmit'}" />
<CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiRetracttoGroup'}" />
<CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiRetracttodraft'}" />
<CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiRetracttosuspend'}" />
<CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiRetracttoreject'}" />
<CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiOpentgb'}" />


<!--〖同步按钮〗-->
<CommandGroup uiname="ft_loan_dlydfag">
    <CommandRef ref="{'FT-APPR.SheeTgbCmdRefTplt':'busiSyncData'}">
        <property name="tbname" value="dlyddetail_toolbar"/>
    </CommandRef>
</CommandGroup>

重写同步方法
snsoft.ft.loan.dlydfin.service.impl.DocuDeliveryFinaApprTgbReleBean
```

- 单据审批链要勾选审批模板



**同审测试操作：**

议付交单提交同审，此时下游交单融资可以拷贝新建同审状态的数据，如果交单融资马上提交同审，会默认加入到上游议付交单的同审单

如果未提交同审，此时议付交单去撤回同审，然后修改数据，此时继续提交同审后，下游交单融资也去提交同审，这时候会校验上游议付交单有修改数据，需要点击同步按钮以后才可以继续提交同审

场景:

- 交单提交到同审26，交单融资拷贝交单同审状态数据，交单融资去提交同审，会根据子表新加的字段srcicode...等6个字段自动加入到同一个同审单

- 上下游单据都是同审的情况上游去撤回同审：撤回上级单据必须同时选中撤回下级单据！

- 上下游单据都是同审的情况，下游撤回到草拟，然后上游单据撤回同审到草拟以后，去提交单审，则下游单据不能操作了，只能删除单据？！  报错： 存在未提交同审的来源单据，禁止操作!

- 上下游单据都是同审的情况，下游撤回到草拟，然后上游单据撤回同审到草拟以后，去修改一些数据，然后继续提交同审，此时需要通过同步按钮去同步数据才可以！

  

```
//（2）若〖同审关系表〗存在“修改时间”与主要子表“来源单据修改时间”不一致的数据，则提示错误“引用的来源单据已修改，请《同步数据》！\n来源单据类型1：来源单据号1\n来源单据类型2：来源单据号2....”；
校验的是来源单据主表的修改时间和srcmodifdate时间一致性
```



