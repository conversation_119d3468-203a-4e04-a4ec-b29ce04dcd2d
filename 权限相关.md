> 用户---角色（岗位）---权限（菜单权限-功能权限？、单据权限--数据权限？）
>
> C 能操作也能查看    R 只读
>
> 凭证制单：CV

```
凭证红冲按钮权限：CV 才能可以按，否则置灰
<Command cmd="redLiVoucher" title="${FT.cmd_generateRedVoucher}" pstn="bindbar" btndesc="{tbname:'lendtoolbar',icon:'icons-btn-stop'}" tblname="ft_loan_dlydfali_view" invokeProgress="pane">
<Control setValue="false" ctrlField="disabled" sheetcode="FT-LOAN.DocuDeliveryFinaLI" dataOpids="${opids?CV}" />
```

```
cfg/res/sheetlim/FT-LOAN.json： 
交单融资处理工作台的放款 还款权限指向对应的凭证制单limid：SNA-VM.VouBData：cfg/sheet/SNA-VM.VouBData/limit.xml
交单融资申请单是有自己的limid:cfg/sheet/FT-LOAN.DocuDeliveryFinaApp/limit.xml
	{
		"sheetcode": "FT-LOAN.DocuDeliveryFinaApp",
		"limid": "FT-LOAN.DocuDeliveryFinaApp"
	},
	{
		"sheetcode": "FT-LOAN.DocuDeliveryFinaLI",
		"limid": "SNA-VM.VouBData"
	},
	{
		"sheetcode": "FT-LOAN.DocuDeliveryFinaRI",
		"limid": "SNA-VM.VouBData"
	},


```



## *用户-菜单权限分配*

配置用户对应的菜单权限，是否有这个菜单



## *用户-单据数据权限分配*

配置用户对应某个单据具体的权限，比如议付交单需要C



## *岗位-单据数据权限分配*

简单一点可以直接通过用户-单据权限配置某个用户的权限，但是更好复用的应该是通过配置用户对应某个岗位，再根据某个岗位配置对应的单据权限





## *用户岗位查询*    *用户菜单权限查询*

查询用户的所有岗位；查询用户的所有菜单权限--是否可见

### 权限字段影响

这4个权限字段才是实际SQL里面影响的，比如bcode in   什么，就是在这里配置业务员部门，对于关系类型，影响的实际上也是这4个字段？

```
<flds>
    <fld fldname="astn" fldtitle="${astn}" fldtype="12" fldumatch="#SN-PLAT.BWcodeFieldMatcher" codedata="#SN-PLAT.RBWUType" options1="32"
        tcprops="{editable:false}" fldname2="astn" fldopts="8" />
    <fld fldname="bcode" fldtitle="${bcode}" fldtype="12" btype="01" fldopts="8" fldname2="bcode" fldopts2="0x41" />
    <fld fldname="wcode" fldtitle="${wcode}" fldtype="12" fldname2="wcode" fldopts="8" />
</flds>
```

![image-20250422111438647](E:\Apple\TyporaMD\Img\image-20250422111438647.png)



### astn 关系类型字段影响

这个其实是在用户关系类型这里设置，相当于可以给用户配置多个部门，也有点类似宏定义，设置公共的维护关系

![image-20250603111751164](E:\Apple\TyporaMD\Img\image-20250603111751164.png)



## *单据数据权限分类*

这里添加进去以后，才能在岗位权限的添加单据选择到

![image-20250421141027484](E:\Apple\TyporaMD\Img\image-20250421141027484.png)

## *单据类型与单据数据权限关系*

这里体现的就是json里面的权限关系

```
	{
		"sheetcode": "RPT-PAY.RdcDetailQuery",
		"limid": "RPT-PAY.RdcDetailQuery"
	}
```



## 如何将新开发界面配置成菜单

直接在*菜单模板【数据库】*  里面新增一条记录（手写），填界面号

![image-20250422112410305](E:\Apple\TyporaMD\Img\image-20250422112410305.png)



## 如何给用户配置菜单

> *用户-菜单权限分配*
>
> *岗位-菜单权限分配*



## UIService 的@AuthParam注解字段如何关联到具体的单据权限

```
@AuthParam(sheetCode = TStockBWcodeChangeVou.SheetCode, opids = { LimitConst.Opid_R, LimitConst.Opid_C }, alias = "v")
```

这里会根据TStockBWcodeChangeVou.SheetCode  单据号去`cfg/res/sheetlim/FT-TSM.json`

```
{
    "sheetcode": "FT-TSM.TStockBWcodeChangeVMark",
    "limid": "SNA-VM.VouBData"
  }
```

然后根据limitid去`cfg/sheet/SNA-VM.VouBData/limit.xml` 这里找到具体的权限

```
<?xml version="1.0" encoding="UTF-8"?><limit xmlns="http://www.snsoft.com.cn/schema/SheetConfig" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <ops>
        <op opid="R" opnm="${opid_R}" plusop="C,CV"/>
        <op opid="C" opnm="${opid_C}"/>
        <op opid="CV" opnm="${opid_vou_CV}"/>
    </ops>
    <flds>
        <fld fldname="astn" fldtitle="${astn}" fldtype="12" fldumatch="#SN-PLAT.BWcodeFieldMatcher" codedata="#SN-PLAT.RBWUType" options1="32"
            tcprops="{editable:false}" />
        <fld fldname="corpbcode" fldtitle="${corpbcode}" fldtype="12" btype="02" fldopts="1" />
        <fld fldname="bcode" fldtitle="${bcode2}" fldtype="12" btype="01" fldopts="1" />
    </flds>
</limit>
```

### 按钮配置权限字段影响同理？

```
	<Command cmd="redLiVoucher" title="${FT.cmd_generateRedVoucher}" pstn="bindbar" btndesc="{tbname:'lendtoolbar',icon:'icons-btn-stop'}" 
			tblname="ft_loan_dlydfali" invokeProgress="pane">
			<Control setValue="false" ctrlField="disabled" sheetcode="FT-LOAN.DocuDeliveryFinaLI" dataOpids="${opids?CV}" />
			<ClientInvokers>
				<Invoker code="20">
					new snsoft.ft.loan.dlydfin.app.invoker.CreateDocuDeliveryFinaRedVoucherInvoker({redType:'redLiVoucher'})
				</Invoker>
			</ClientInvokers>
			<UIInvokers>
				<Invoker code="10" method="invoke">
					#FT-LOAN.CreateDocuDeliveryFinaRedVoucherInvoker
				</Invoker>
			</UIInvokers>
		</Command>
```

比如放款红冲凭证里面的，也会根据`sheetcode="FT-LOAN.DocuDeliveryFinaLI" `去找`cfg/res/sheetlim/FT-LOAN.json`

```
<Control setValue="false" ctrlField="disabled" sheetcode="FT-LOAN.DocuDeliveryFinaLI" dataOpids="${opids?CV}" />
```

```
{
		"sheetcode": "FT-LOAN.DocuDeliveryFinaLI",
		"limid": "SNA-VM.VouBData"
	},
```

然后去找SNA-VM.VouBData 下面是否有CV权限，有的话就可以点击，没有就置灰？

![image-20250603093245659](E:\Apple\TyporaMD\Img\image-20250603093245659.png)



## 权限字段 bcode in（'B100002'）如何配置---在当前界面查看F1，有跳转帮助中心！！！



*岗位-单据数据权限分配*  里面找到单据数据权限配置对应的权限字段

> 宏定义如何查看，在当前界面查看F1，有跳转帮助中心！！！
>
> https://ctrm-dev.xiangyu.cn/xycode/help.html?helpFile=help/SN-LIM/Limit/Datalimit.md#%E5%B2%97%E4%BD%8D%E6%93%8D%E4%BD%9C%E6%9D%83%E9%99%90

![image-20250526100924517](E:\Apple\TyporaMD\Img\image-20250526100924517.png)

## 如何给用户分配岗位

在部门/人员关系菜单查询

![image-20250603092154272](E:\Apple\TyporaMD\Img\image-20250603092154272.png)



## 按钮权限（功能权限？）和数据权限区别

按钮权限的话（所以也就是功能权限？），就是你有这个权限就能操作所有数据，不会再去细致查询数据的内容

```
<Control ctrlField="disabled" setValue="false" sheetcode="${sheetcode}" btnOpids="C">
    <Condition uiname="${mainTblname}" colname="status" matchMode="4" matchValue="20"/>
</Control>
```

数据权限，主要用户工作台？  比如交单放款信息需要配置数据权限，这时候会根据每一行数据的内容---比如部门，去校验更细致的权限，比如第一行A部门可以操作，第二行B部门不可以操作

```
<Control setValue="false" ctrlField="disabled" sheetcode="FT-LOAN.DocuDeliveryFinaLI" dataOpids="${opids?CV}" />
```

## 权限验证---看SQL

如果是管理员 会1=1   没权限是1=2？      bcode  in     。。。



## 界面公司码表-普通用户看不到数据

>  码表的权限是如何测试验证的！

这里`cmparams.opids="C"  cmparams.sheetcode="${sheetcode}"`    说明要单据号对应的权限要有C 权限才能查看  

```
<c name="corpbcode" title="${RES.C}" sqltype="12" selectMiddle="true" nmpre="filter" codedata="#SN-PLAT.CorpBcode" mutiple="true" cmparams.sheetcode="${sheetcode}" cmparams.status="~10" cmparams.opids="C" aidInputerBtn="true" showname="true" disableed="true"/>

```

这里也需要配置` limit.xml`,`btype="02" fldopts="1"`

```
<fld fldname="corpbcode" fldtitle="${corpbcode}" fldtype="12" btype="02" fldopts="1"/>
```

实际这里只配置了R权限，所以看不到数据

![image-20250605100729523](E:\Apple\TyporaMD\Img\image-20250605100729523.png)



## 权限修改问题

注解实体+UI 界面的权限

```
@AuthParam(sheetCode = DocuDeliveryFinaLIVMark.SheetCode, opids = {LimitConst.Opid_C, LimitConst.Opid_R})
```

```
<property name="sheetcode" value="FT-LOAN.DocuDeliveryFinaLI" />
${sheetcode}  引用单据权限！！

	<c name="bcode" title="${b_wcode}" sqltype="12" codedata="#FT-ORGZ.BWcode" showname="true" disableed="true" selectMiddle="true" aidInputerBtn="true" mutiple="true"
				cmparams="status:'~10'" cmparams.sheetcode="${sheetcode}" cmparams.opids="C,R"  uiprops.textDir="rtl" nmpre="filter"/>
			<!--公司-->
			<c name="corpbcode" title="${RES.C}" sqltype="12" codedata="#FT-ORGZ.CorpBcode" showname="true" disableed="true" selectMiddle="true" aidInputerBtn="true" mutiple="true"
				cmparams="status:'~10'" cmparams.sheetcode="${sheetcode}" cmparams.opids="R,C" nmpre="filter"/>
```

这两种应该都是会去这里去找对应权限limid，然后找到`SNA-VM.VouBData`下面的权限配置`cfg/sheet/SNA-VM.VouBData/limit.xml`

```
{
		"sheetcode": "FT-LOAN.DocuDeliveryFinaLI",
		"limid": "SNA-VM.VouBData"
},
```

