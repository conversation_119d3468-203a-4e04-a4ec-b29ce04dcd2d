# `genDocuDeliveryCor` 方法详细分析

`genDocuDeliveryCor` 是 `TStockGenDocuDeliveryCorServiceImpl` 类中的核心方法，用于生成议付交单核销数据。

## 方法签名

```java
public <V extends List<TStockSalOutSettObject>> void genDocuDeliveryCor(V settObjects, boolean isBlue)
```

## 参数说明
- `settObjects`: 出仓结算对象列表
- `isBlue`: 是否为蓝单操作

## 方法执行流程

### 1. 参数校验
```java
if (CollectionUtils.isEmpty(settObjects)) {
    return;
}
```
- 检查输入的结算对象列表是否为空，为空则直接返回

### 2. 获取出仓单数据

```java
List<TStockSalOutClient> tStockSalOutClients = tStockOutClientService.querySalOutClient(Collections.singleton(settObjects.get(0).getTsosicoder()), true);
TStockSalOutClient tsoSalOut = tStockSalOutClients.get(0);
```
- 通过结算对象中的出仓单内码查询出仓单客户端对象
- 获取第一个出仓单对象

### 3. 准备数据容器
```java
List<SalShipForDocuDeliverygCorBalance> corBalanceList = new ArrayList<>();
```
- 创建核销余额对象列表，用于存储生成的核销数据

### 4. 构建数据映射
```java
Map<String,TStockSalOutPickerClient> pickerMap = tsoSalOut.getPickerList().stream()
        .collect(Collectors.toMap(TStockSalOutPickerClient::getTsospicoder, Function.identity(), (existingValue, newValue) -> existingValue));
Map<String,TStockSalOutGoodClient> goodMap = tsoSalOut.getGoodList().stream()
        .collect(Collectors.toMap(TStockSalOutGoodClient::getTsosgicode, Function.identity(), (existingValue, newValue) -> existingValue));
Map<String,BusinessClient> prjClientMap = prjClientService.loadBusiness(tsoSalOut.getGoodList().stream().map(TStockSalOutGoodClient::getPrjicode).collect(Collectors.toSet())).stream()
        .collect(Collectors.toMap(BusinessClient::getInnercode, t -> t));
```
- 构建出仓拣配明细映射 (拣配内码 -> 拣配对象)
- 构建出仓商品明细映射 (商品内码 -> 商品对象)
- 构建业务对象映射 (业务内码 -> 业务对象)

### 5. 获取用户信息和付款方式
```java
String userCuicode = AppContext.getUserSession().getUserCuicode();
Set<String> lcPaymode = rpModeClientService.getLcPaymode(userCuicode);
```
- 获取当前用户的企业内码
- 获取所有信用证(LC)付款方式

### 6. 查询发货单数据
```java
List<SalShipClient> shipClientList = shipClientService.getSalShipClientByCorRIds(Collections.singletonList(settObjects.get(0).getSalshipicoder()),
        SspConstant.SALSHIP_ST_TABNAME + "," + SspConstant.SALSHIP_P_TABNAME + "," + SALSHIP_IC_TABNAME);
SalShipClient shipClient = shipClientList.get(0);
if (Objects.isNull(shipClient)) {
    return;
}
```
- 查询发货单及其内控、分摊、拣配明细
- 如果发货单不存在，则直接返回

### 7. 判断发货单类型
```java
boolean isDomSalship = "FT-SSP.DomSalShip".equals(shipClient.getSheetcode()) || "FT-SSP.ConvSalShip".equals(shipClient.getSheetcode());
```
- 判断是否为境内发货单

### 8. 获取发货单收款方式映射

```java
Map<String,SalShipSTClient> stClientHashMap = getSalShipSTClientMap(shipClient, isDomSalship, lcPaymode);
if (MapUtils.isEmpty(stClientHashMap)) {
    return;
}
```
- 调用 `getSalShipSTClientMap` 方法获取发货单收款方式映射
- 如果映射为空，则直接返回

### 9. 遍历结算对象生成核销数据
```java
for (TStockSalOutSettObject tStockSalOutSettObject : settObjects) {
    for (TStockSalOutSTSettObject stSettObject : tStockSalOutSettObject.getStSettObjects()) {
        SalShipSTClient stClient = stClientHashMap.get(stSettObject.getSalshipsicoder());
        if (Objects.isNull(stClient)) {
            continue;
        }
        this.addCorBalance(tsoSalOut, tStockSalOutSettObject, stSettObject, pickerMap, goodMap, prjClientMap, stClient, shipClient, isDomSalship, corBalanceList);
    }
}
```
- 双层循环遍历结算对象及其分摊对象
- 获取对应的收付款方式
- 调用 `addCorBalance` 方法添加核销余额数据

### 10. 生成核销数据
```java
this.genCorData(isBlue, tsoSalOut, corBalanceList);
```
- 调用 `genCorData` 方法生成下游核销数据

### 11. 处理提前交单逻辑
```java
boolean isaossd = isDomSalship || "FT-SSP.OvsSalShip".equals(shipClient.getSheetcode());
if (Objects.equals(isaossd, true)) {
    // TODO 实现
}
```
- 判断是否需要处理提前交单逻辑
- 目前为TODO，尚未实现

## 子方法详解

### 1. `getSalShipSTClientMap` 方法
```java
private Map<String,SalShipSTClient> getSalShipSTClientMap(SalShipClient shipClient, boolean isDomSalship, Set<String> lcPaymode)
```
- 功能：获取发货单收款方式映射
- 参数：
  - `shipClient`: 发货单对象
  - `isDomSalship`: 是否境内发货单
  - `lcPaymode`: 信用证付款方式集合
- 返回：收款方式内码到收款方式对象的映射
- 处理逻辑：
  - 遍历发货单的收款方式列表
  - 注意：代码中有被注释掉的条件判断，目前实际逻辑是无条件添加所有收款方式

### 2. `addCorBalance` 方法
```java
private <V extends TStockSalOutClient> void addCorBalance(V tsoSalOut, TStockSalOutSettObject tStockSalOutSettObject, TStockSalOutSTSettObject stSettObject,
        Map<String,TStockSalOutPickerClient> pickerMap, Map<String,TStockSalOutGoodClient> goodMap, Map<String,BusinessClient> prjClientMap, SalShipSTClient shipSTClient, SalShipClient shipClient,
        boolean isDomSalship, List<SalShipForDocuDeliverygCorBalance> corBalanceList)
```
- 功能：添加核销余额数据
- 参数：包含出仓单、结算对象、分摊对象、各种映射等
- 处理逻辑：
  - 获取出仓拣配商品明细和出仓商品信息
  - 创建核销余额对象并设置各种属性
  - 复制相关对象的属性到核销余额对象
  - 设置销售业务编号、采购业务编号等信息
  - 设置贸易类型、币种、金额等信息
  - 将构建好的核销余额对象添加到列表中

### 3. `genCorData` 方法
```java
private void genCorData(boolean isBlue, TStockSalOutClient tsoSalOut, List<SalShipForDocuDeliverygCorBalance> corBalanceList)
```
- 功能：生成核销数据
- 参数：
  - `isBlue`: 是否为蓝单操作
  - `tsoSalOut`: 出仓单对象
  - `corBalanceList`: 核销余额对象列表
- 处理逻辑：
  - 创建核销参数对象并设置源单据信息
  - 设置核销代码为"FT-DOCU.DocuDeliveryCor"
  - 设置是否为变更操作
  - 如果核销余额列表不为空，则创建数据库任务
  - 使用 `FundPushTaskService` 推送任务异步处理

## 业务流程总结

1. 获取出仓单和发货单数据
2. 构建各种数据映射关系
3. 判断发货单类型和收款方式
4. 遍历结算对象，为每个符合条件的分摊对象生成核销余额数据
5. 异步推送任务生成核销数据
6. 预留提前交单处理逻辑（尚未实现）

该方法是销售出仓单生成议付交单核销的核心实现，通过异步任务方式处理，提高系统性能。