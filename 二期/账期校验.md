```
10准备
70开启
75文员初关
76文员关账
77事业部财务关账
78总账会计关账


必要性检查不可以手工通过
合理性检查可以手工点击通过
```

```
1、应收应付账期判断状态调整-增加系统选项[关账管理-文员节点管控单据]，该选项内的的单据在判定账期是否开启时，按照“账期状态”等于‘70：开启’判定账期为开启。
 判定账期为开启逻辑，应收应付账期状态等于“70：开启”；判定账期关闭逻辑，应收应付账期状态非“70：开启”，涉及单据如下：
 
 2、其他未在选项内的单据，按事业部财务关账判定，涉及如下单据：
判定账期为开启逻辑，应收应付账期状态大于‘10：准备’且小于“77：事业部财务关账”；判定账期为关闭逻辑，应收应付账期状态大于等于“77：事业部财务关账”，涉及单据如下：


/**
 * (1)根据所有【账期部门】、原始单据的事务处理日期对应的年和月取对应的账期状态，如果状态为非‘10：准备’且非‘78：总账会计关账’状态，则事务处理日期默认为原始单据的事务处理日期，只读；
 * (2)根据【账期部门】、原始单据的事务处理业务日期对应的年和月取对应的账期状态，如果状态为‘78：总账会计关账’状态，则根据所有【账期部门】取【应收应付账期】为非‘10：准备’且非‘78：总账会计关账’状态，如果取到，事务处理日期赋值规则如下：
 * ①则取最小账期年月；
 * ②事务处理日期默认取最小账期年月的第一天，只读。
 * (3)否则，赋值为空；
 */
 具体例子：
```

```
场景测试：一个是事务处理日期的赋值，一个是接口或者单据提交的时候账期校验
比如议付交单，事务处理日期赋值规则默认为接口传入的固定值confirmInfo.getOdate()，而不是采用上面的规则判断，
而交单取消的时候需要采用上面的规则取获取事务处理日期FundRelaBmpUtils.getBmpRedTcaptime


1、事务处理日期赋值以后confirmInfo.getOdate()/FundRelaBmpUtils.getBmpRedTcaptime，这里涉及上面规则（开启账期的判断getOpenBusiMonthPeriodPredicate）
2、需要插入账期id数据RelaBmpUtils.insertRelaBmp：实际插入表ft_fund_rela_bmp，里面会有关联账期bmpcode：库存/应收应付账期账期ID,后面校验的时候，拿到这个账期id数据去查询具体的账期状态
3、然后进行校验FundRelaBmpUtils.checkRelaBmpIsValid 这里也涉及上面规则（关闭账期的判断getCloseBusiMonthPeriodPredicate(sheetcode）

```

```
ft_fund_rela_bmp表字段：
relabmpicode	内码
cuicode	商户
status	状态
srcsheetcode	来源单据类型
srcicode	来源单据内码
srccode	来源单据号
bmpcode	库存/应收应付账期账期ID
nobmpdata	未准备账期数据

```

测试

```
//获取事务处理日期
println(snsoft.ft.comm.util.FundRelaBmpUtils.getBmpRedTcaptime("C300","*********",new java.util.Date() ,"C000000001","FT-RDOC.RdocClaim"))
//测试检查账期
snsoft.ft.docu.dlyd.AppleTestService.testRelaBmp()

交单确认取消接口：
事务处理日期采用getBmpRedTcaptime获取，，发现不在配置单据--不满足规则>10且＜77，所以会去找所有满足这个规则的其他账期，取最早日期的一条，1月1号，然后又用这个去校验账期，那肯定是符合的？

交单确认接口：  7月账期为77状态事业部关账
此时接口事务处理日期默认赋值就是接口的传值日期7月份，所以去校验的时候不在配置单据，走规则>10且＜77，不满足所以会报错
```



```
预收冲销单：  合同预收款   发货预收款       应该先冲销发货预收款
先在网银到证工作台生成一个发货预收款，然后可以根据这个发货预收款生成预收冲销单，
上面子表：发货预收款
下面子表：销售出仓单产生的应收未收的数据
冲销单生效以后，会冲销这个发货预收款，把240（原来的发货预收款）  -240（冲销了这个发货预收）   240（转成了实收）

预付冲销单:预付转实付？
```

