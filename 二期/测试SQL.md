```
select <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>Y<PERSON> from  FT_DOCU_DLYDG_CB where SALSHIPSSICODER like '%early%';
select SALSHIPSSICODER,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>Y,<PERSON><PERSON><PERSON><PERSON>  from  FT_DOCU_DLYDG where <PERSON><PERSON><PERSON><PERSON><PERSON> like '%early%' order by <PERSON><PERSON><PERSON><PERSON><PERSON>;
select <PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>  from  FT_DOCU_DLYD where <PERSON><PERSON><PERSON><PERSON><PERSON> like '%early%' order by <PERSON><PERSON><PERSON><PERSON><PERSON>;


--CB
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, COR<PERSON>AG, SALCCODE, SALORDCODE, SALORDICODE, SA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>EETCOD<PERSON>, SRCICODE, SRCCODE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>R, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, <PERSON><PERSON>ING, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>OR<PERSON>CODE, PURPRJICODE, <PERSON>UR<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ER, PURSHIPCODER, GCODE, C<PERSON><PERSON>DESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early111', 0, '**********', '<PERSON>XS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 100.000000, 0.000000, 100.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early222', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 80.000000, 0.000000, 80.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early333', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 120.000000, 0.000000, 120.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
```

## 交单明细不够核销

> 20 30  50  60  ；  100  80  120

```
select salshipssicoder,corflag,fcy,fcying,fcyed,SALSHIPCODER,TSOSCODER from  ft_docu_dlydg_cb where salshipicoder='686f2c16b7ee0d5a7fcfb427';
select salshipssicoder,dlydicode,dlydgicode,fcy,tsosicoder,fcying,idx  from  ft_docu_dlydg where dlydicode like '%early%' order by dlydicode;
select dlydicode,fcy,dlyddate  from  ft_docu_dlyd where dlydicode like '%early%' order by dlyddate;

-- 查询某个交单还剩余多少可提前交单核销对象
select m.dlydcode,
       salshipcoder,
       g.fcying,
       g.fcy,
       idx,
       m.dlydicode,
       salshipssicoder,
       dlydgicode
from ft_docu_dlydg g
         inner join ft_docu_dlyd m on g.dlydicode = m.dlydicode
where g.fcying > 0
  and g.fcy > 0
  and g.TSOSICODER is null;


SELECT sum(fcyed)
FROM FT_FUND.FT_DOCU_DLYDG_CB t
WHERE SALSHIPICODER='6868d76ab8b35d3fbbf29def' 
ORDER BY MODIFYDATE;


select *from ft_docu_dlydg_ct where salshipssicoder like '%early%';

-- 出仓单往来+交单往来+出仓核销负数往来
select '出仓往来' as 场景,dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
                , vdc as 应实, status, fcode, scerate, suerate
                , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode from FT_LRP_LRP where SRCICODE='686f660cb7ee0d5a7fcfe3d7'
union all
select '交单往来' as 场景,dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
     , vdc as 应实, status, fcode, scerate, suerate
     , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode from FT_LRP_LRP where SRCICODE='686f65497f9ee0322bf5c83c'  and DLYDICODE is not null
union all
select '出仓核销负数往来' as 场景,dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
     , vdc as 应实, status, fcode, scerate, suerate
     , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode from FT_LRP_LRP where SRCICODE='686f65497f9ee0322bf5c83c' and DLYDICODE is  null;

select * from ft_lrp_lrp_cb where lrpicodex in(select LRPICODE from ft_lrp_lrp where srcicode='686f65497f9ee0322bf5c83c');
select * from ft_lrp_lrp_cb where lrpicodex in('686b651f50187f312fdfd296');
select * from ft_lrp_lrp_ct where lrpicodex in('68688bba5d46077dcd285642');


--删除
delete from ft_docu_dlydg_cb where salshipssicoder like '%early%';
delete from ft_docu_dlydg where dlydicode like '%early%';
delete from ft_docu_dlyd where dlydicode like '%early%';
delete from ft_docu_dlydg_ct where salshipssicoder like '%early%';





--CB表
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early111', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 100.000000, 0.000000, 100.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early222', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 80.000000, 0.000000, 80.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early333', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 120.000000, 0.000000, 120.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);


--dlyd表
INSERT INTO FT_FUND.FT_DOCU_DLYD (DLYDICODE, DLYDCODE, ADATE, SALCCODE, FCODE, FCY, DLYDDATE, BANKCODE, BANKACCCODE, LCCODE, LCREGICODE, LCREGCODE, ISAOSSD, SENDDATE, SIGNDATE, SIGNSTATUS, REMARK, RDATE, INVDATE, SETTDOCCODE, ACDATE, CCODEADDR, RPBANKCODE, ICCODE, ISFIRST, ISLAST, RPBANKADDR, VDDATE, LSDDATE, ISDIS, DISTXT, LCBANK, NTBANKCODE, EXPRESSCODE, CSCOMPANY, STATUS, WFCODE, WFUID, BCODE, WCODE, CORPBCODE, SHEETCODE, CUICODE, RATIFYDATE, SUBMITDATE, PERFORMDATE, PAYMODELIST, TGBICODE, TGBCODE, ISINIT, ISCREATED, VPREPARE, PREDATE, MODIFIER, MODIFYDATE) VALUES ('early222', 'early222', DATE '2025-06-30', '**********', 'CNY', 110.000000, DATE '2025-07-09', 'KHYH00010628', '4100023014200007578', null, null, null, 'Y', null, null, null, null, null, DATE '2025-07-03', '11', null, '河北省定州市胜利路', null, '123', 'Y', null, null, null, null, null, null, null, null, null, null, '62', null, null, '*********', '0001CC100000000IDXQE', 'C300', 'FT-DOCU.DocuDelivery', 'C000000001', null, TIMESTAMP '2025-06-30 14:35:08', null, '0025', null, null, 'N', null, '0001CC100000001X4HZQ', TIMESTAMP '2025-06-30 14:33:57', '0001CC100000001X4HZQ', TIMESTAMP '2025-06-30 14:34:51');
INSERT INTO FT_FUND.FT_DOCU_DLYD (DLYDICODE, DLYDCODE, ADATE, SALCCODE, FCODE, FCY, DLYDDATE, BANKCODE, BANKACCCODE, LCCODE, LCREGICODE, LCREGCODE, ISAOSSD, SENDDATE, SIGNDATE, SIGNSTATUS, REMARK, RDATE, INVDATE, SETTDOCCODE, ACDATE, CCODEADDR, RPBANKCODE, ICCODE, ISFIRST, ISLAST, RPBANKADDR, VDDATE, LSDDATE, ISDIS, DISTXT, LCBANK, NTBANKCODE, EXPRESSCODE, CSCOMPANY, STATUS, WFCODE, WFUID, BCODE, WCODE, CORPBCODE, SHEETCODE, CUICODE, RATIFYDATE, SUBMITDATE, PERFORMDATE, PAYMODELIST, TGBICODE, TGBCODE, ISINIT, ISCREATED, VPREPARE, PREDATE, MODIFIER, MODIFYDATE) VALUES ('early111', 'early111', DATE '2025-06-30', '**********', 'CNY', 50.000000, DATE '2025-07-06', 'KHYH00010628', '4100023014200007578', null, null, null, 'Y', null, null, null, null, null, DATE '2025-07-03', '11', null, '河北省定州市胜利路', null, '123', 'Y', null, null, null, null, null, null, null, null, null, null, '62', null, null, '*********', '0001CC100000000IDXQE', 'C300', 'FT-DOCU.DocuDelivery', 'C000000001', null, TIMESTAMP '2025-06-30 14:35:08', null, '0025', null, null, 'N', null, '0001CC100000001X4HZQ', TIMESTAMP '2025-06-30 14:33:57', '0001CC100000001X4HZQ', TIMESTAMP '2025-06-30 14:34:51');


--dlydg表
INSERT INTO FT_FUND.FT_DOCU_DLYDG (DLYDGICODE, DLYDGICODER, DLYDICODE, SALSHIPSSICODER, IDX, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, RECFCY, FCY, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, REDFLAG, LCCODE, LCREGICODE, FSERATE, SUERATE, SCERATE, SCY, ZUSD, ZCNY, SRCSHEETCODE, SRCCODE, SRCICODE, SRCGICODE, SRCMODIFYDATE, SRCSTATUS, FCYING) VALUES ('early222', 'early222', 'early111', 'early111', 20, 'NXS30025060047', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW300343002500096', 'early111', 'early111', null, null, '10', '0025', 300.000000, 30.000000, 'CG3002501123', 'N9XYG0000000000355037_prm', 'N9XYG0000000000337074_ptm', 'T3430025N064', 'N9XYG0000000000682893_psm', 'DH3002505287', 'MYSP10000043', '线材', null, 'SPXL00010041', '0001CC100000000IDXQE', '*********', 0, null, null, null, null, null, null, null, null, 'FT-SSP.DomSalShip', 'early111', 'early111', null, null, null, 30.000000);
INSERT INTO FT_FUND.FT_DOCU_DLYDG (DLYDGICODE, DLYDGICODER, DLYDICODE, SALSHIPSSICODER, IDX, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, RECFCY, FCY, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, REDFLAG, LCCODE, LCREGICODE, FSERATE, SUERATE, SCERATE, SCY, ZUSD, ZCNY, SRCSHEETCODE, SRCCODE, SRCICODE, SRCGICODE, SRCMODIFYDATE, SRCSTATUS, FCYING) VALUES ('early111', 'early111', 'early111', 'early111', 10, 'NXS30025060047', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW300343002500096', 'early111', 'early111', null, null, '10', '0025', 60.000000, 20.000000, 'CG3002501123', 'N9XYG0000000000355037_prm', 'N9XYG0000000000337074_ptm', 'T3430025N064', 'N9XYG0000000000682893_psm', 'DH3002505287', 'MYSP10000043', '线材', null, 'SPXL00010041', '0001CC100000000IDXQE', '*********', 0, null, null, null, null, null, null, null, null, 'FT-SSP.DomSalShip', 'early111', 'early111', null, null, null, 20.000000);
INSERT INTO FT_FUND.FT_DOCU_DLYDG (DLYDGICODE, DLYDGICODER, DLYDICODE, SALSHIPSSICODER, IDX, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, RECFCY, FCY, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, REDFLAG, LCCODE, LCREGICODE, FSERATE, SUERATE, SCERATE, SCY, ZUSD, ZCNY, SRCSHEETCODE, SRCCODE, SRCICODE, SRCGICODE, SRCMODIFYDATE, SRCSTATUS, FCYING) VALUES ('early333', 'early333', 'early222', 'early111', 20, 'NXS30025060047', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW300343002500096', 'early111', 'early111', null, null, '10', '0025', 60.000000, 50.000000, 'CG3002501123', 'N9XYG0000000000355037_prm', 'N9XYG0000000000337074_ptm', 'T3430025N064', 'N9XYG0000000000682893_psm', 'DH3002505287', 'MYSP10000043', '线材', null, 'SPXL00010041', '0001CC100000000IDXQE', '*********', 0, null, null, null, null, null, null, null, null, 'FT-SSP.DomSalShip', 'early111', 'early111', null, null, null, 50.000000);
INSERT INTO FT_FUND.FT_DOCU_DLYDG (DLYDGICODE, DLYDGICODER, DLYDICODE, SALSHIPSSICODER, IDX, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, RECFCY, FCY, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, REDFLAG, LCCODE, LCREGICODE, FSERATE, SUERATE, SCERATE, SCY, ZUSD, ZCNY, SRCSHEETCODE, SRCCODE, SRCICODE, SRCGICODE, SRCMODIFYDATE, SRCSTATUS, FCYING) VALUES ('early444', 'early444', 'early222', 'early111', 10, 'NXS30025060047', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW300343002500096', 'early111', 'early111', null, null, '10', '0025', 60.000000, 60.000000, 'CG3002501123', 'N9XYG0000000000355037_prm', 'N9XYG0000000000337074_ptm', 'T3430025N064', 'N9XYG0000000000682893_psm', 'DH3002505287', 'MYSP10000043', '线材', null, 'SPXL00010041', '0001CC100000000IDXQE', '*********', 0, null, null, null, null, null, null, null, null, 'FT-SSP.DomSalShip', 'early111', 'early111', null, null, null, 60.000000);




```

## 交单明细有剩余

> 50 60  30 200 ；  100  80  120

```
select salshipssicoder,corflag,fcy,fcying,fcyed,SALSHIPCODER,TSOSCODER from  ft_docu_dlydg_cb where salshipicoder='686f2c16b7ee0d5a7fcfb427';
select salshipssicoder,dlydicode,dlydgicode,fcy,tsosicoder,fcying,idx  from  ft_docu_dlydg where dlydicode like '%early%' order by dlydicode;
select dlydicode,fcy,dlyddate  from  ft_docu_dlyd where dlydicode like '%early%' order by dlyddate;

-- 查询某个交单还剩余多少可提前交单核销对象
select m.dlydcode,
       salshipcoder,
       g.fcying,
       g.fcy,
       idx,
       m.dlydicode,
       salshipssicoder,
       dlydgicode
from ft_docu_dlydg g
         inner join ft_docu_dlyd m on g.dlydicode = m.dlydicode
where g.fcying > 0
  and g.fcy > 0
  and g.TSOSICODER is null;


SELECT sum(fcyed)
FROM FT_FUND.FT_DOCU_DLYDG_CB t
WHERE SALSHIPICODER='6868d76ab8b35d3fbbf29def' 
ORDER BY MODIFYDATE;


select *from ft_docu_dlydg_ct where salshipssicoder like '%early%';

-- 出仓单往来+交单往来+出仓核销负数往来
select '出仓往来' as 场景,dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
                , vdc as 应实, status, fcode, scerate, suerate
                , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode from FT_LRP_LRP where SRCICODE='686f660cb7ee0d5a7fcfe3d7'
union all
select '交单往来' as 场景,dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
     , vdc as 应实, status, fcode, scerate, suerate
     , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode from FT_LRP_LRP where SRCICODE='686f65497f9ee0322bf5c83c'  and DLYDICODE is not null
union all
select '出仓核销负数往来' as 场景,dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
     , vdc as 应实, status, fcode, scerate, suerate
     , fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode from FT_LRP_LRP where SRCICODE='686f65497f9ee0322bf5c83c' and DLYDICODE is  null;

select * from ft_lrp_lrp_cb where lrpicodex in(select LRPICODE from ft_lrp_lrp where srcicode='686f65497f9ee0322bf5c83c');
select * from ft_lrp_lrp_cb where lrpicodex in('686b651f50187f312fdfd296');
select * from ft_lrp_lrp_ct where lrpicodex in('68688bba5d46077dcd285642');


--删除
delete from ft_docu_dlydg_cb where salshipssicoder like '%early%';
delete from ft_docu_dlydg where dlydicode like '%early%';
delete from ft_docu_dlyd where dlydicode like '%early%';
delete from ft_docu_dlydg_ct where salshipssicoder like '%early%';




--CB表
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early111', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 100.000000, 0.000000, 100.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early222', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 80.000000, 0.000000, 80.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early333', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 120.000000, 0.000000, 120.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);

-- dg表
INSERT INTO FT_FUND.FT_DOCU_DLYDG (DLYDGICODE, DLYDGICODER, DLYDICODE, SALSHIPSSICODER, IDX, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, RECFCY, FCY, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, REDFLAG, LCCODE, LCREGICODE, FSERATE, SUERATE, SCERATE, SCY, ZUSD, ZCNY, SRCSHEETCODE, SRCCODE, SRCICODE, SRCGICODE, SRCMODIFYDATE, SRCSTATUS, FCYING) VALUES ('early111', 'early111', 'early111', 'early111', 10, 'NXS30025060047', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW300343002500096', 'early111', 'early111', null, null, '10', '0025', 60.000000, 50.000000, 'CG3002501123', 'N9XYG0000000000355037_prm', 'N9XYG0000000000337074_ptm', 'T3430025N064', 'N9XYG0000000000682893_psm', 'DH3002505287', 'MYSP10000043', '线材', null, 'SPXL00010041', '0001CC100000000IDXQE', '*********', 0, null, null, null, null, null, null, null, null, 'FT-SSP.DomSalShip', 'early111', 'early111', null, null, null, 50.000000);
INSERT INTO FT_FUND.FT_DOCU_DLYDG (DLYDGICODE, DLYDGICODER, DLYDICODE, SALSHIPSSICODER, IDX, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, RECFCY, FCY, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, REDFLAG, LCCODE, LCREGICODE, FSERATE, SUERATE, SCERATE, SCY, ZUSD, ZCNY, SRCSHEETCODE, SRCCODE, SRCICODE, SRCGICODE, SRCMODIFYDATE, SRCSTATUS, FCYING) VALUES ('early222', 'early222', 'early111', 'early111', 20, 'NXS30025060047', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW300343002500096', 'early111', 'early111', null, null, '10', '0025', 300.000000, 60.000000, 'CG3002501123', 'N9XYG0000000000355037_prm', 'N9XYG0000000000337074_ptm', 'T3430025N064', 'N9XYG0000000000682893_psm', 'DH3002505287', 'MYSP10000043', '线材', null, 'SPXL00010041', '0001CC100000000IDXQE', '*********', 0, null, null, null, null, null, null, null, null, 'FT-SSP.DomSalShip', 'early111', 'early111', null, null, null, 60.000000);
INSERT INTO FT_FUND.FT_DOCU_DLYDG (DLYDGICODE, DLYDGICODER, DLYDICODE, SALSHIPSSICODER, IDX, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, RECFCY, FCY, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, REDFLAG, LCCODE, LCREGICODE, FSERATE, SUERATE, SCERATE, SCY, ZUSD, ZCNY, SRCSHEETCODE, SRCCODE, SRCICODE, SRCGICODE, SRCMODIFYDATE, SRCSTATUS, FCYING) VALUES ('early333', 'early333', 'early222', 'early111', 10, 'NXS30025060047', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW300343002500096', 'early111', 'early111', null, null, '10', '0025', 60.000000, 30.000000, 'CG3002501123', 'N9XYG0000000000355037_prm', 'N9XYG0000000000337074_ptm', 'T3430025N064', 'N9XYG0000000000682893_psm', 'DH3002505287', 'MYSP10000043', '线材', null, 'SPXL00010041', '0001CC100000000IDXQE', '*********', 0, null, null, null, null, null, null, null, null, 'FT-SSP.DomSalShip', 'early111', 'early111', null, null, null, 30.000000);
INSERT INTO FT_FUND.FT_DOCU_DLYDG (DLYDGICODE, DLYDGICODER, DLYDICODE, SALSHIPSSICODER, IDX, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, RECFCY, FCY, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, REDFLAG, LCCODE, LCREGICODE, FSERATE, SUERATE, SCERATE, SCY, ZUSD, ZCNY, SRCSHEETCODE, SRCCODE, SRCICODE, SRCGICODE, SRCMODIFYDATE, SRCSTATUS, FCYING) VALUES ('early444', 'early444', 'early222', 'early111', 20, 'NXS30025060047', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW300343002500096', 'early111', 'early111', null, null, '10', '0025', 60.000000, 200.000000, 'CG3002501123', 'N9XYG0000000000355037_prm', 'N9XYG0000000000337074_ptm', 'T3430025N064', 'N9XYG0000000000682893_psm', 'DH3002505287', 'MYSP10000043', '线材', null, 'SPXL00010041', '0001CC100000000IDXQE', '*********', 0, null, null, null, null, null, null, null, null, 'FT-SSP.DomSalShip', 'early111', 'early111', null, null, null, 200.000000);



--d表
INSERT INTO FT_FUND.FT_DOCU_DLYD (DLYDICODE, FCY, DLYDCODE, ADATE, SALCCODE, FCODE, DLYDDATE, BANKCODE, BANKACCCODE, LCCODE, LCREGICODE, LCREGCODE, ISAOSSD, SENDDATE, SIGNDATE, SIGNSTATUS, REMARK, RDATE, INVDATE, SETTDOCCODE, ACDATE, CCODEADDR, RPBANKCODE, ICCODE, ISFIRST, ISLAST, RPBANKADDR, VDDATE, LSDDATE, ISDIS, DISTXT, LCBANK, NTBANKCODE, EXPRESSCODE, CSCOMPANY, STATUS, WFCODE, WFUID, BCODE, WCODE, CORPBCODE, SHEETCODE, CUICODE, RATIFYDATE, SUBMITDATE, PERFORMDATE, PAYMODELIST, TGBICODE, TGBCODE, ISINIT, ISCREATED, VPREPARE, PREDATE, MODIFIER, MODIFYDATE) VALUES ('early111', 110.000000, 'early111', DATE '2025-06-30', '**********', 'CNY', DATE '2025-07-06', 'KHYH00010628', '4100023014200007578', null, null, null, 'Y', null, null, null, null, null, DATE '2025-07-03', '11', null, '河北省定州市胜利路', null, '123', 'Y', null, null, null, null, null, null, null, null, null, null, '62', null, null, '*********', '0001CC100000000IDXQE', 'C300', 'FT-DOCU.DocuDelivery', 'C000000001', null, TIMESTAMP '2025-06-30 14:35:08', null, '0025', null, null, 'N', null, '0001CC100000001X4HZQ', TIMESTAMP '2025-06-30 14:33:57', '0001CC100000001X4HZQ', TIMESTAMP '2025-06-30 14:34:51');
INSERT INTO FT_FUND.FT_DOCU_DLYD (DLYDICODE, FCY, DLYDCODE, ADATE, SALCCODE, FCODE, DLYDDATE, BANKCODE, BANKACCCODE, LCCODE, LCREGICODE, LCREGCODE, ISAOSSD, SENDDATE, SIGNDATE, SIGNSTATUS, REMARK, RDATE, INVDATE, SETTDOCCODE, ACDATE, CCODEADDR, RPBANKCODE, ICCODE, ISFIRST, ISLAST, RPBANKADDR, VDDATE, LSDDATE, ISDIS, DISTXT, LCBANK, NTBANKCODE, EXPRESSCODE, CSCOMPANY, STATUS, WFCODE, WFUID, BCODE, WCODE, CORPBCODE, SHEETCODE, CUICODE, RATIFYDATE, SUBMITDATE, PERFORMDATE, PAYMODELIST, TGBICODE, TGBCODE, ISINIT, ISCREATED, VPREPARE, PREDATE, MODIFIER, MODIFYDATE) VALUES ('early222', 230.000000, 'early222', DATE '2025-06-30', '**********', 'CNY', DATE '2025-07-09', 'KHYH00010628', '4100023014200007578', null, null, null, 'Y', null, null, null, null, null, DATE '2025-07-03', '11', null, '河北省定州市胜利路', null, '123', 'Y', null, null, null, null, null, null, null, null, null, null, '62', null, null, '*********', '0001CC100000000IDXQE', 'C300', 'FT-DOCU.DocuDelivery', 'C000000001', null, TIMESTAMP '2025-06-30 14:35:08', null, '0025', null, null, 'N', null, '0001CC100000001X4HZQ', TIMESTAMP '2025-06-30 14:33:57', '0001CC100000001X4HZQ', TIMESTAMP '2025-06-30 14:34:51');

```

## 发货单---出仓单，直接核销

> 提前交单测试   100 200；  30  80

```
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early111', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', null, null, '10', '0025', 'CNY', 'N', 100.000000, 0.000000, 100.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early222', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 30.000000, 0.000000, 30.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early444', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', 'early111', 'early111', '10', '0025', 'CNY', 'N', 80.000000, 0.000000, 80.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);
INSERT INTO FT_FUND.FT_DOCU_DLYDG_CB (SALSHIPSSICODER, CORFLAG, SALCCODE, SALORDCODE, SALORDICODE, SALPRJICODE, SALPRJCODE, SRCSHEETCODE, SRCICODE, SRCCODE, SALSHIPCODER, SALSHIPICODER, TSOSICODER, TSOSCODER, ABGOODS, PAYMODE, FCODE, DOMABR, FCY, FCYED, FCYING, PURORDCODE, PURORDICODE, PURPRJICODE, PURPRJCODE, PURSHIPICODER, PURSHIPCODER, GCODE, CNAMEDESC, ENAMEDESC, GVCODE, WCODE, BCODE, CORPBCODE, LCCODE, LCREGICODE, TRADETYPE, STOCKDATE, ISAOSSD, VPREPARE, PREDATE, MODIFIER, MODIFYDATE, CUICODE, STATUS, ISINIT) VALUES ('early333', 0, '**********', 'WXS39825070001', '685519588adeaa21805a0f7a', '685519588adeaa21805a0f78', 'YW30033932500003', 'FT-TSO.TStockSalOut', '6863cb363b78735aea9da2c1', 'WSO398250700001-06', 'early111', 'early111', null, null, '10', '0025', 'CNY', 'N', 200.000000, 0.000000, 200.000000, 'WCG36925070001', '6863b2f03b78735aea9d9319', '6863a54b781a346fe6bfd090', 'YW30033932500003', null, null, 'MYSP10000001', '煅后焦', null, 'SPXL00010001', '2117774337884127234', '********', 'C398', null, null, '40', DATE '2025-07-01', 'N', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:10', '0001CC100000001X4HZQ', TIMESTAMP '2025-07-01 19:49:17', 'C000000001', '70', null);

```

