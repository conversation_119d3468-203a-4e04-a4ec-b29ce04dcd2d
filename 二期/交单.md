# 交单

> 交单的收付款方式    一个交单不会同时有信用证即期和远期，也不会同时有信用证+DP/DA是吧？
>
> 1、不会；2、之前了解不会
>
> 那DP和DA也不会同时把？
>
> 对，之前沟通不会有;有组合的就TT和LC组合



> **发货单生成的收付款方式有多少条？**
>
> 根据发货单商品去拣配，拣配了多少条明细，就用这个明细*收付款方式的数量
>
> 比如一个发货单商品，拣配了2个，收付款方式2个   那就是4个

<img src="E:\Apple\TyporaMD\Img\83b85b4be1fdeb499d4a1736d778ed3e.jpg" alt="83b85b4be1fdeb499d4a1736d778ed3e" style="zoom: 50%;" />

![image-20250707085544114](E:\Apple\TyporaMD\Img\image-20250707085544114.png)



## 待做

```
6、折本位币汇率：隐藏，取〖交单承兑确认〗中“冲销”、“被冲销”标识未被选中行记录“事务处理日期”对应的汇率；
7、折美元汇率：隐藏，取〖交单承兑确认〗中“冲销”、“被冲销”标识未被选中行记录“事务处理日期”对应的汇率；
8、折人民币汇率：隐藏，取〖交单承兑确认〗中“冲销”、“被冲销”标识未被选中行记录“事务处理日期”对应的汇率；
9、折本位币金额：隐藏，交单金额*折本位币汇率；
10、折美元金额：隐藏，折本位币金额*折美元汇率；
11、折人民币金额：隐藏，折本位币金额*折人民币汇率；
```

这个不小心注释了，要恢复？

```
//检查被删除〖交单明细〗的“信用证号”，若删除这些记录后本单据〖交单明细〗中再无含“信用证号”记录，则提示错误“删除后本单据将不存在信用证收款方式，禁止操作！”
		if (!lccodeExists)
		{
			throw new FTPlatException(DocuErrConstants.FT_DOCU_10000016);
		}
```

## 发货单商品+收付款方式生成cb表

>  NSO300250600064发货单

好像是：比如2个商品，收付款方式DP40%+DA60%，那么每个商品都会对应比例的收付款方式，所以就是2*2，会有4条收付款明细记录





## 往来报错`not find LrpCorBalance, lrpicodex=`  思路

>  `snsoft.ft.lrp.service.impl.LrpClientServiceImpl#dealLrpWhenPartRed{}`
>
> 

```

snsoft.ft.lrp.service.impl.LrpClientServiceImpl#dealLrpWhenPartRed{}
List<String> lrpicodexs = lrpclients.stream().map(Lrpclient::getLrpicodex).distinct().collect(Collectors.toList());
List<LrpCorBalance> lrpCorBalances = DAO.newInstance(LrpCorBalance.class).queryListByID(lrpicodexs);
for (Lrpclient lrpclient : lrpclients)
{
    Optional<LrpCorBalance> first = lrpCorBalances.stream().filter(l -> l.getLrpicodex().equals(lrpclient.getLrpicodex())).findFirst();
    if (!first.isPresent())
    {
        if (logger.isDebugEnabled())
        {
            logger.error("grpkey=" + lrpclient.getGrpkey());
        }
        throw new RuntimeException("not find LrpCorBalance, lrpicodex=" + lrpclient.getLrpicodex());
    }
 
根据往来核算项构造grpkey，然后转BASE 64为grpkeyid；

private void dealGrpkey(List<Lrpclient> lrpclientList)
	{
		lrpclientList.forEach(lrpclient -> {
			String grpkey = SetLrpFeeGenUtils.getGrpkey(lrpclient, lrpclient.getCuicode(), true);
			lrpclient.setGrpkey(grpkey);
			lrpclient.setGrpkeyid(SetLrpFeeGenUtils.toBase64ID(grpkey));
			String rgrpkey = SetLrpFeeGenUtils.getGrpkey(lrpclient, lrpclient.getCuicode(), false);
			lrpclient.setRgrpkey(rgrpkey);
			lrpclient.setRgrpkeyid(SetLrpFeeGenUtils.toBase64ID(rgrpkey));
		});
	} 
    
刚刚上面这个    
List<LrpCorBalance> lrpCorBalances = DAO.newInstance(LrpCorBalance.class).queryListByID(lrpicodexs);
这个lrpicodex根据grpkeyids+rgrpkeyids 找到对应的上游往来，接着赋值给lrpicdeox{}
snsoft.ft.lrp.service.impl.LrpClientServiceImpl#dealLrpicodex
List<String> grpkeyids = newLrpclientList.stream().map(Lrpclient::getGrpkeyid).collect(Collectors.toList());
List<String> rgrpkeyids = newLrpclientList.stream().map(Lrpclient::getRgrpkeyid).collect(Collectors.toList());
List<Lrpclient> existLrpclients = getLrpclientsByGrpkeys(grpkeyids, rgrpkeyids);
```



## DP+DA 往来核销

```
--- 这个生成的负数往来，会直接扣减出仓回单时生成的往来，并且lrpicodex=出仓回单的lrpicode
DP/DA的交单明细：
交单提交时： 生成出仓单应收往来负数：状态为10
交单取消提交时：删除交单生成的应收往来
交单确认后：更新交单提交的生成的往来状态为70、
取消交单确认：更新交单提交的生成的往来状态为10



交单确认时：DP/DA的交单明细生成交单应收往来	
```





>  这个LRPICODEX  是往来核销码，用于不同场景生成核销时记录对应核销码，如果是固定单据场景核销，一般就直接采用上游的内码，比如salshipssicoder

```
-- srcicode = '出仓回单内码'
select dlydicode,paymode, fcy, srccode, srcsheetcode, dc as 收付
, vdc as 应实, status, fcode, scerate, suerate
, fserate, scy, zcny, zusd,lrpicode,lrpicodex,lrpicodeo,lrpicoder,srcgicode,GRPKEY,GRPKEYID,RGRPKEYID
from ft_lrp_lrp
where 
srcicode = '68550488c28d6d4c5137bbd2' order by lrpicode asc;



select *from FT_LRP_LRP_CB where LRPICODEX='6855051ac28d6d4c5137bd1e';
select *from FT_LRP_LRP_CT where LRPICODEX='6855051ac28d6d4c5137bd1e';
```

当议付交单提交待审以后，会将出仓回单产生的往来直接扣减，而不是核销成fcyed

```
fcy   fcyed   fcying
1009   0      1009
此时提交待审，不是直接核销金额？
999   0      999
```





## 接口



### 交单确认

（3）若交单明细的收款方式为‘DP’或‘DA’：
①　若“是否提前交单”为‘否’则按对应收款方式的金额，从应收业务往来转为交单往来（此时提交后产生的负数往来生效，并同时产生正数的交单往来） ，  应收账款日期取议付交单的“预计收款日期”，汇率取出仓单日期对应的汇率；
②　若“是否提前交单”为‘是’，此时不处理往来。待出仓单生效且转成出仓单对象表数据后：
a.	冲销发货单对应的交单明细，并重新取由对应出仓单生成的【销售待交单对象表】中的交单明细；注意冲销和重新获取交单明细，要核销对象表金额。
b.	按对应收款方式‘DP’或‘DA’的出仓单金额从应收业务往来转为交单往来，应收账款日期取 议付交单的“预计收款日期”，汇率取出仓单日期对应的汇率；
（4）若“收款方式”为‘即期国际信用证’或‘即期国内信用证’：
①　按对应收款方式为‘即期国际信用证’或‘即期国内信用证’的交单金额生成预收账款往来和交单信用证往来，并生成应收信用证凭证，汇率取交单日期对应的汇率；注意应收信用证不参与核销；
②　若“是否提前交单”为‘是’，待出仓单生效且转成出仓单对象表数据后：冲销发货单对应的交单明细，并重新取由对应出仓单生成的【销售待交单对象表】中的交单明细；注意冲销和重新获取交单明细，要核销对象表金额；

### 承兑确认

（3）若“收款方式”为‘远期国际信用证’或‘远期国内信用证’：
①　若“是否提前交单”为‘否’，按对应收款方式为‘远期国际信用证’或‘远期国内信用证’的交单金额生成预收账款往来和交单信用证往来，并生成应收信用证凭证，汇率取承兑日期对应的汇率；注意应收信用证不参与核销；
②　若“是否提前交单”为‘是’，待出仓单生效且生成对象表数据后：冲销发货单对应的交单明细，并重新取由对应出仓单生成的【销售待交单对象表】中的交单明细；注意冲销和重新获取交单明细，要核销对象表金额；



### 待交单对象

```
snsoft.ft.ssp.bas.service.impl.SalShipBasStatusListener#genDocuCorBalance
snsoft.ft.ssp.utils.SalShipUtils#updateDocuCorBalance


同审部分未实现
```



（1） 销售发货单：若是境内销售发货单，则销售发货单收款方式存在“系统收付款方式”为‘LC’（境外销售发货单不限收款方式），且销售发货单主表“是否提前交单”为‘是’：
①	销售发货单生效或提交到同审时，按收款方式分摊表（若是境内发货单，只有LC的结算方式才要写入对象表；境外发货单不限收款方式）写入对象表；
②	销售发货单红冲完后，同步更新【销售待交单对象表】记录的状态为‘作废’；
③	销售发货单红蓝操作后，同步更新【销售待交单对象表】记录的状态为‘作废’；蓝单提交到‘同审’或‘生效’状态后，重新将对象表状态更新为‘同审’或‘生效’；
④	销售发货单取消提交同审，同步删除【销售待交单对象表】对应记录；
（2） 销售出仓单：若“来源单据类型”为‘境内销售发货单’，且销售出仓单收款方式存在“系统收付款方式”为‘LC’（若为境外销售发货单，不限收款方式）：
①	销售出仓单生效时，按收款方式分摊表（若“来源单据类型”为‘境内销售发货单’，只有LC的结算方式才要写入对象表；若“来源单据类型”为‘境外销售发货单’不限收款方式）写入对象表；
②	若销售出仓单对应的发货单上“是否提前交单”为‘是’，则按出仓发货单金额冲销原发货单产生的【销售待交单对象表】记录；
③	销售出仓单红蓝操作后，同步更新【销售待交单对象表】记录的状态为‘作废’；蓝单提交到‘生效’状态后，重新将对象表状态更新为‘生效’；
④	销售出仓单红冲完后，同步更新【销售待交单对象表】记录的状态为‘作废’；



## 逻辑梳理

> **转往来仅针对DP和DA的**
>
> 其余LC和DP一致

> 做了出仓单的交单确认，只做了交单还没出仓的确认。



**1、发货单--生成交单--交单确认接口--出仓单**

> 当前的交单，此时没有出仓交单明细，所以在交单确认的时候判断没有出仓交单明细，就不转往来
>
> 在出仓单的时候，判断当前发货单对应的交单：是否做了交单确认？  根据发货单号找到做了交单确认，且提前交单剩余金额大于0的记录：
>
> 发货单--生成交单---出仓单
>
> 发货单--生成交单--交单确认接口
>
> 发货单--生成交单---出仓单
>
> 发货单--生成交单--交单确认接口--出仓单

`在出仓单生效时处理已下3点:`
1）提前交单明细转出仓交单明细：取交单明细和出仓待交单对象金额小的冲销，并按此金额重新取出仓待交单对象生成的交单明细

2）核销出仓待交单对象金额 

3）处理往来:出仓单业务往来转交单往来

```
发货单--生成交单---出仓单
发货单--生成交单---出仓单---交单确认接口

发货单--生成交单--交单确认接口  50  60
发货单--生成交单--交单确认接口--30  200 --出仓

只要有做了交单确认，并且交单提前交单金额剩余0的记录，就处理往来？  肯定会有金额可以转交单往来？
```

**2、发货单--生成交单---出仓单---交单确认接口**

> 当前的交单，此时有出仓交单明细，所以在交单确认的时候判断没有出仓交单明细，就不转往来

`在出仓单生效时处理已下2点:`
1）提前交单明细转出仓交单明细：取交单明细和出仓待交单对象金额小的冲销，并按此金额重新取出仓待交单对象生成的交单明细
2）核销出仓待交单对象金额 

`在交单确认接口处理往来：` 出仓单业务往来转交单往来

```
交单确认接口处理： 如果当前交单是由出仓单生成的，并且是提前交单，就需要在此时处理转往来：
将带出仓单号的交单明细  生成往来，负数是不带议付交单号--核销出仓单往来，正数是带交单号--交单往来

如果当前交单没有生成负数交单？     当前交单对应的发货单是否做了出仓单？并且是否已经做了交单确认？

做交单确认的时候，如果没有提前交单不需要？

发货单--生成交单---出仓单
发货单--生成交单---出仓单---交单确认接口


发货单--生成交单--交单确认接口  50  60
发货单--生成交单---出仓单
发货单--生成交单---出仓单---交单确认接口
```

**3、发货单--出仓单--生成交单--交单确认接口**

`在出仓单生效处理：`  发货待交单对象转出仓交待交单对象
**其余同非提前交单一样逻辑？**

```

```

​			



假如发货单已经生成了交单，也已经完成了交单确认接口（比如走的是第1种场景），又根据这个发货单（之前只出了部分）直接去做出仓回单（走第3种场景），是不是又是当成新的未做交单确认处理。

所以不能直接判断是否有没有根据这个发货单生成交单。







# 测试流程

## 场景1：

>  发货单 300，生成完交单1、2，发货单待交单对象还剩余50
>  发货单--交单1: 100--交单确认（此时做交单确认判断，当前交单没有带出仓单号的交单明细，不需要转往来）    
>  发货单--交单2:  150

> **此场景出仓单金额260大于要冲销的交单金额150+100**

**流程1：生成出仓单1：金额260，生成出仓待交单对象：260，出仓往来260**

处理：找交单，按照交单日期+序号转 ，空的交单日期放在最后面
冲销交单-1  -100，100带出仓单号 ； 交单-2  -150，150带出仓单号   
出仓单待交单对象：剩余10，冲销了250

转往来： **只能转做了交单确认里面的带出仓单号的交单明细（此时满足的就是交单1）**，也就是100，出仓往来还剩余160，转了100出仓业务往来给交单往来。
也就是根据带出仓单号的交单明细100，去生成负数的-100往来，再生成正数的100交单往来

**流程2：**

接着继续做交单2的交单确认，此时如果当前交单有出仓单号的交单明细，就需要转往来，所以会生成-150的负数往来，再生成150的正数交单往来。也就是把出仓往来剩余的160转了150给交单2的交单往来。此时剩余出仓往来10。

**流程3：**

接着继续做出仓单2：生成40，此时就相当于发货单--出仓单了。 ，发现没有可以转的提前交单，因为100,150都被刚刚转完了，那就要把发货单剩余的50转40给出仓单这里，其实就是发货单的待交单对象从50变成10。

## 场景2

>  发货单 300，生成完交单1、2，发货单待交单对象还剩余50
>  发货单--交单1: 100--交单确认（此时做交单确认判断，当前交单没有带出仓单号的交单明细，不需要转往来）    
>  发货单--交单2:  150

> **此场景出仓单金额160大于要冲销的交单金额150+100**

**流程1：生成出仓单1：金额160，生成出仓待交单对象：160，出仓往来160**

处理：找交单，按照交单日期+序号转 ，空的交单日期放在最后面

冲销交单-1：交单明细如下，  -100（冲的是原来提前交单明细100，根据这个生成），100带出仓单号,100原来提前交单明细 ； 

冲销交单-2  ：交单明细如下，-60（冲的是原来提前交单明细150，根据这个生成），60带出仓单号，150原来提前交单明细   ；**（此时还剩余90-fcying提前交单明细可以转）**

转往来：**只能转做了交单确认里面的带出仓单号的交单明细（此时满足的就是交单1）**，也就是100，出仓往来还剩余60，转了100出仓业务往来给交单往来。
也就是根据带出仓单号的交单明细100，去生成负数的-100往来，再生成正数的100交单往来

**流程2：**

接着继续做**交单2的交单确认**，此时如果当前交单有出仓单号的交单明细，就需要转往来，所以会生成-60的负数往来，再生成60的正数交单往来。也就是把出仓往来剩余的60转了60给交单2的交单往来。此时剩余出仓往来0。

**流程3：**

接着继续做出仓单2：生成140， **因为此时发现还有90可以转的提前交单明细**，所以不是走发货单--出仓单了，而是**发货单--交单2--交单确认--继续出仓单。**

会继续冲销交单-2  ：交单明细如下，-90（冲的是原来提前交单明细150（此时fcying剩余90），根据这个生成），90带出仓单号，150原来提前交单明细   ；**（此时提前交单明细可以转的金额为0了，待交单对象剩余140-90=50）**

转往来: 此时交单2在流程2做完了交单确认，**满足只能转做了交单确认里面的带出仓单号的交单明细**，所以查询交单2里面本次要插入的带出仓单号的交单明细90，根据这个生成往来，转完后出仓往来剩余50了，而不能去根据之前的60带出仓单号的交单明细生成往来，。



## 场景3

300  100  40
发货单--交单1-100--交单1确认（不转往来）
发货单--交单2-50
--出仓单200：交单1带出仓号明细100，交单2带出仓明细50；转了交单1 100往来，交单2不转往来，

--出仓单60：此时没有可转的提前交单明细，直接把剩余的100发货单待交单对象核销60，剩余40
--交单2确认，转往来，有出仓号1的明细50，此时转的是出仓单1里面的往来，因为出仓明细50是出仓单1号



## 注意点

- 所以我在生成正数的出仓单往来的时候，根据有没有做交单确认，并且是DP/DA，一开始需要排除TT交单明细，去转往来即可
- 还需要考虑一下LC 和DP/DA,TT是否有区别处理的地方
- 交单确认接口，LC 跟原来一样，DP/DA不太一样



# 场景梳理

```
步骤1：
发货单--生成交单1--交单确认接口--做出仓单1  此时只转了当前交单明细的部分，还有部分没转，但是此时交单明细已经有了带出仓号的交单明细

步骤2：接着继续做下面：
发货单--生成交单1--交单确认接口--做出仓单2，会继续转剩余的交单明细，此时不能直接查询所有带出仓号的交单明细，而是应该查询剩余的
```

# 问题记录：

```
出仓单往来采用任务push，导致交单这里查询不到往来，去核销的时候会报错！！！

出仓待交单对象此时还没有存盘，交单明细插入以后调用service存盘，会导致核销查询不到来源，也会报错！！


出仓单往来拿出仓明细业务员     和出仓待交单对象生成的业务员取值是：销售采购商品明细业务员不一致

fcying   更新判断：

同审新增逻辑开发：  发货单

LC测试、DP/DA测试
```



## 代码恢复

```
生成往来  生成cb表---调用方式为task
往来bcode赋值
生成cb表收付款方式判断


同审：
交单明细核销码，存在就更新，不存在就删除？但是删除的话，如果一条数据都没有了也可以？？

```

其他问题修复

```
之前禅道号提前的任务
doctype

发货单刷数据： 要刷2部分数据，一部分是发货单的分摊收款方式表新字段的值。  一个是你那边核销对象得检查看看
```









