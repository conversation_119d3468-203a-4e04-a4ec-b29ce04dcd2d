![image-20240530195358748](E:\Apple\TyporaMD\Img\image-20240530195358748.png)



![image-20240602160135131](E:\Apple\TyporaMD\Img\image-20240602160135131.png)



![image-20240602153341397](E:\Apple\TyporaMD\Img\image-20240602153341397.png)

![image-20240602153558231](E:\Apple\TyporaMD\Img\image-20240602153558231.png)

界面属性控制

![image-20240602155410311](E:\Apple\TyporaMD\Img\image-20240602155410311.png)

权限配置可视化    岗位操作权限

![image-20240602155543529](E:\Apple\TyporaMD\Img\image-20240602155543529.png)



## 目录结构

- snsoft_adk：底层核心代码，项目中必须依赖使用。
- sn_plat：底层业务平台，管理类项目必须依赖使用。
- snadk_product：底层产品，根据项目需求选择性依赖使用。
- snadk_help：底层帮助中心。



- sv里面是微服务 只会有impl和api---可以看pom依赖！
- ui层要访问所有的账套
-   ui下面的wb---js



**调用链路：ui---》api--》impl**

> 这个是客商停用申请入口 
>客商那个表的主键和外码改个名字,详情表也要改下主键

sql不写默认=
ui层控制权限

filter_cd

按钮注册 cmdreg

## 开发步骤

> 一步步创建,很快的,先创VO,在创建查询保存接口,然后实现类,再写UI界面,定义单据sheet,然后定义菜单就可以看到页面了 
>
> 建表--->建VO(里面定义sheetcode)--->

建表--->建VO(里面定义sheetcode)--->建service api以及impl-->建uiservice 不需要接口--->单据定义（放在单独的sheet而不是res下）-->entry.xml：涉及resbundle字段宏，按钮注册-->按钮注册cmdreg（和n9区别）---> [FN-ui.entry.xml](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\sheet\FT-CCODE.StatusChange\FN-ui.entry.xml) 入口监听（包括了按钮注册的监听）--->菜单



**Table  代表界面**

**dataset代表界面的数据**



## 用法积累

### 第一部分

> 该文档内容涉及一个关于n10系统的单据处理和权限配置的开发过程。文档详细讨论了在n10系统中，如何实现客商业务状态变更单的功能，包括单据结构、项目结构和API设计。特别强调了与n9b系统的区别，如业务端整合、API和实现的调整。此外，讨论了多语言处理、建表方式以及权限定义等关键技术问题。
>
> 在技术实现方面，文档提到使用了Vo、Service接口和IMP l等技术元素来构建系统功能。对于多语言支持，文档描述了四种情况：完全支持多语言、部分支持多语言、不支持多语言但有固定英文类、以及同时支持多语言和固定英文类。建表时采用了多元模板和字段大小的定义方法。
>
> 权限配置方面，文档详细说明了如何在n10系统中进行权限定义和用户/岗位权限分配。提到了使用注解来控制权限，以确保数据安全性，并介绍了如何通过配置文件进行操作码和权限码的配置管理。
>
> 此外，还探讨了部署和代码更改对系统功能的影响，特别是关于Java文件更改后是否需要重启服务以使更改生效的问题。文档中提及，非新方法的更改可以直接生效，而新方法或类的添加则需要重启服务。
>
> 整体而言，这份文档为理解和实施n10系统中的单据处理和权限配置提供了全面的指导和技术细节，有助于开发人员更好地集成和部署该系统。



1、N10 单据放在sheet文件夹  而不是res的sheet

2、多语言vo--sheetstatus继承langmainvo

```
@SqlColumn(filterBean = "SN-CORE.LangNameFilterService?[{}]")
private              String   langname;
```

3、子表为空要new arraylist

```
	public List<CcodeChangeDetail> getDetails()
	{
		if (details == null)
		{
			this.details = new ArrayList<>();
		}
		return details;
	}
```

4、table.getColumn  更全   dataset.getColumn更不全

5、页面查询参数初始值

```
<uilisteners><![CDATA[
    #SN-PLAT.BusiUcodeWcodeInitValueListener?[{}];
    #SN-PLAT.BusiFilterDateDefaultUIListener?[{dateColumns:'predatefm'}];
]]></uilisteners>
```

也可以用标签的initVal

```
<c name="fromfile" sqltype="4" title="${fromfile}" width="${E.G.CW.fromfile}" hidden="true" initval="0" sqlexpr="0"/>

```

6、查询子表参数nmpre="filter_cd"

```
   <c name="bcodeg" sqlexpr="bcode" title="${b_wcode}" sqltype="12" codedata="#FT-ORGZ.BWcode" cmparams="status:'~10'" disableed="true" showname="true"
               selectMiddle="true" aidInputerBtn="true" cmparams.sheetcode="FT-CCODE.StatusChange" cmparams.opids="R,C" aiprops="copyMap:{wcodeg:'wcode'}" nmpre="filter_cd"/>
```

对应的Param也会

```
@Nmpre.Nmpres({
			//客商业务状态变更申请单明细
			@Nmpre(nmpre = "filter_cd", pJoinColumn = "ccscicode", joinColumn = "ccscicode", tblname = "ft_cd_cc_cd") })
	class StatusChangeEntryParams extends QueryParams
```

查子表都用这个注解

```
@SubColumn(table = "ft_cd_cc_cd", pJoinColumn = "ccscicode", sqlColumn = @SqlColumn(column = "ccode"))
		private              String ccode;
```

7、按钮注册--N10---采用cmdreg

```
  <m:Toolbar name="scToolbar" uiprops.cellClassName="toolbar-panel" region="north">
    </m:Toolbar>
```

FN-ui.entry.xml

```
<bean code="FT-CCODE.70" sortidx="70" impl="#SN-EXT-UI.CommandRegistryUIListener"/>
<bean code="FT-CCODE.80" sortidx="80" impl="#new snsoft.ext.cmd.CommandRegistryJSListener({})"/>
```

8、title="${RES.C}"在resbundle

9、如何挂载监听上去 ？funccode:'ui.entry'

```
<uilisteners><![CDATA[
#SN-PLAT.PlatFunctionListener?[{sheetcode:'FT-CCODE.StatusChange',funccode:'ui.entry'}]
]]></uilisteners>
```

10、挂载菜单

多语言`@UI?客商业务状态变更单`

```
<menu title="客商变更">
				<menu cmd="ui:FT-CCODE.SheetStatus" cmdid="FT-CCODE.SheetStatus" title="@UI?客商业务禁止状态" author="lax" flags="0x080" />
				<menu cmd="ui:FT-CCODE.StatusChangeEntry" cmdid="FT-CCODE.StatusChangeEntry" title="@UI?客商业务状态变更单" author="lax" flags="0x080" />
				<menu cmd="ui:FT-CCODE.InfoChangeEntry" cmdid="FT-CCODE.InfoChangeEntry" title="@UI?客商信息变更单" author="聂明笛" flags="0x080" />
				<menu cmd="ui:FT-CCODE.StopClEntry" cmdid="FT-CCODE.StopClEntry" title="@UI?客商停用注销申请-入口" author="lax" flags="0x080" />
			</menu>
```

在 [ResUI30-FT-CCODE_zh.inf](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\resbundle\ResUI30-FT-CCODE_zh.inf) 

```
title_FT-CCODE.StatusChangeEntry=客商业务状态变更单-入口
```

entry.xml

```
<B xmlns="http://www.snsoft.com.cn/schema/UI" xmlns:m="http://www.snsoft.com.cn/schema/UI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   title="${}" 
```

11、ResUI30-FT-CCODE_zh.inf和 [ResTbl30-FT-CCODE_zh.inf](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\resbundle\ResTbl30-FT-CCODE_zh.inf) 区别，字段放在ResTbl30，UI的放在ui



12、编写详情：挂载监听，也是对应 [FN-ui.detail.xml](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\sheet\FT-CCODE.StatusChange\FN-ui.detail.xml) 

```
<uilisteners><![CDATA[
    #SN-PLAT.PlatFunctionListener?[{sheetcode:'FT-CCODE.StatusChange',funccode:'ui.detail'}]
    ]]></uilisteners>
```

13、如果字段隐藏hidden了采用ctrl+f1调试可以看到字段

```
      <c name="sheetcode" title="${RES.C}" sqltype="12" hidden="true" />
        <c name="submitdate" title="${RES.C}" sqltype="93" hidden="true" />
        <c name="performdate" title="${RES.C}" sqltype="93" hidden="true" />
        <c name="ratifydate" title="${RES.C}" sqltype="93" hidden="true" />
        <c name="vprepare" title="${RES.C}" sqltype="12" hidden="true" />
        <c name="predate" title="${RES.C}" sqltype="93" hidden="true" />
```

![image-20240602133054026](E:\Apple\TyporaMD\Img\image-20240602133054026.png)



14、查询保存

```
xprops.LoadDataService="FT-CCODE.StatusChangeUIService#queryDetailUI"
xprops.SaveDataService="FT-CCODE.StatusChangeUIService#saveUI"
```

15、打印和审批

```
<include src="SN-PLAT.Doc.SheetDocPrint" props="{mainui:'statuschange'}"/>
        <include src="SN-APPR.PlatTasklist"/>
```

16、详情的按钮也放在 [FN-ui.detail.xml](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\sheet\FT-CCODE.StatusChange\FN-ui.detail.xml) 

```
        <P title="${FT.title.tab.cschangeNote}">
            <GroupPane name="ccodedetail_pane">
<!--            <m:Toolbar name="cd_toolbar" tags="tbtn" hidden="false">-->
<!--                <ToolbarBtn name="ccodedetail_btn_newSheet" title="${RES.$.title.F.btn.addCWcode?选择客商及业务员}" xprops="iconClassName:icons-btn-add" />-->
<!--            </m:Toolbar>-->
                <m:GridTable title="${FT.title.grp.changedetail}" name="ccodedetail" sqlexpr="ft_cd_cc_cd" mainui="statuschange"
                             skipRdOnlyCellOnEnterKey="true" uiprops.minVisibleRows="6" uiprops.fitInDOMHeight="1" uiprops="hideVBar:true" rdonly="true"
                             xprops.LoadDataService="FT-CCODE.StatusChangeUIService#queryChangeDetailUI">
    <!--                <attrs>-->
    <!--                    <attr type="203" name="newSheet" title="${RES.$.title.F.btn.addCWcode?选择客商及业务员}" />-->
    <!--                </attrs>-->
```

17、拷贝主表字段xprops.cpmastercol

```
<c name="ccscicode" sqltype="12" width="${E.G.CW.code}" hidden="true" xprops.cpmastercol=":ccscicode" />

```

18、单据穿透：如果入口到详情不需要配置监听？？别的应该要？
FT-CCODE  是系统号 [ViewDetailFT-CCODE.xml](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\res\viewdetail\ViewDetailFT-CCODE.xml) 

```
<Details>
		<Colname>ccsccode</Colname>
		<TACBlock><![CDATA[
        ccscicode = dataSet.get("ccscicode")
        if ccscicode == null
            return null
        end if
        //确保记录存在
        assertExists("ft_cd_cc_sc",ccscicode)
        info.funcid = "FT-CCODE.StatusChangeDetail"
        info.pm.put("AutoRefresh",1)
        info.pm.put("InitValue.ccscicode",ccscicode)
        return info
        ]]></TACBlock>
		<Remark><![CDATA[
        	说明：客商状态变更单穿透；
        ]]></Remark>
	</Details>
```

比如详情里面点击客商穿透--> [FN-ui.detail.xml](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\sheet\FT-CCODE.StatusChange\FN-ui.detail.xml) 的，

```
<bean code="FT-CCODE.60" sortidx="60" impl="#new snsoft.plat.bas.viewdetail.ViewDetailListener({tgtUINames:['ccodedetail']})"/>

```

tgtUINames是 [StatusChangeDetail.xml](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\ui\res\FT-CCODE\StatusChangeDetail.xml) 面的name？

```
 <m:GridTable title="${FT.title.tab.cschangeNote}" name="ccodedetail"
```

19、配置服务端监听： [FN-sv.save.xml](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\sheet\FT-CCODE.StatusChange\FN-sv.save.xml) 

```

```



20、模块之间调用---client包---分布式服务--对外提供服务使用的，@springbean

其他地方用的是@service

21、固定英文列   ename   ，还有的是include？

多语言VO 继承langmainvo   采用filterBean

多语言监听

```
<bean code="FT-CCODE.70" sortidx="70" impl="#SN-UI.LangNamesUIListener?[{tgtUINames:'ft_cd_cc_ss',colname:'langname'}]"/>

```

22、 [coderefFT-CCODE.xml](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\sheet\FT-CODE.Dependency\coderefFT-CCODE.xml) 

引用检查？  比如删除？

23、ui-> [optctrl](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\ui\optctrl) 

snsoft/schema/OptsCtrl.xsd---说明



### 第二部分

1、审批
VO---SheetStatusVO

```
class CcodeStatusChange extends BasVO implements SheetStatusVO, ApprSheetVO

/**提交时间*/
	@Column
	private              Date                    submitdate;
	/**审批时间*/
	@Column
	private              Date                    performdate;
	/**生效时间*/
	@Column
	private              Date                    ratifydate;
	@Column
	private              String                  wfcode;
	@Column
	private              String                  wfuid;
```

继承SheetApprAccessService

```
 StatusChangeService extends PlatBusiAccessService<CcodeStatusChange>, SheetApprAccessService<CcodeStatusChange>
{
```

- 界面配置：审批链定义；单据审批链关系

- 单据监听

2、编码规则：

 [accode](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\res\accode) 定义，然后再配置单据编码关联关系 [sheetaccode](D:\snsoftn10\CODE\fterp\parents-ft-java\parents-ft-code-java\ft-code\src\main\resources\cfg\res\sheetaccode) --json











## 疑问

界面定义的宏？ title   width



```
-DSN.ID=SNA-ALL
-Dsn.test=true
-DSN.ConfigPath=D:\snsoftn10\TOOLS\snconfig-single-dev
-DAPP.MemoryCache=false
-Dsnsoft.ide.init=true
-Dspring.config.name=application 
```

```
-DSN.ID=SNA-ALL
-DSN.test=true
-DSN.ConfigPath=D:\snsoftn10\TOOLS\snconfig-single-dev
-DAPP.MemoryCache=false
-Dspring.config.name=application
```

