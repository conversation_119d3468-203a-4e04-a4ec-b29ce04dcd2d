# 查询多个子表

```java
return funcQuery(params, null, FTUtils.getDetailClass(AdvPayOff.SheetCode, AdvPayOffDetail.class));

DAO<AdvPayOffDetail> dao = SheetUtils.newDAO(AdvPayOff.SheetCode, "ft_rpadj_offg");
return dao.queryUI(params);
```



# 单据存盘

```java
PayDocService service = SpringBeanUtils.getBeanByName(PayDocService.BeanName);
service.save(updateVoLists);
```



```java
BusiAccessService<AdvPayOff> busiAccessService = SheetUtils.getAccessService(AdvPayOff.SheetCode);
busiAccessService.save(advPayOff);
```



### 主子孙如何存盘

> 主表  子表  孙表可能是更新或者插入

```java
主表写上子表，子表里面写孙表
public class AdvPayOff extends RecPayOff
{
	@Valid
	@OneToMany
	@JoinColumn(name = "officode")
	@ResKey("FT.title.grp.AdvPayOffg")
	private List<AdvPayOffDetail> advpayoffdetails;
}

public class AdvPayOffDetail extends RecPayOffDetail
{
	@Valid
	@OneToMany
	@JoinColumn(name = "offgicode")
	@ResKey("FT.title.grp.AdvPayOffgs")
	private List<AdvPayOffGDetail> advpayoffgdetails;
}


```

- 主、子表最外面是setUpdate  最后面busiAccessService去save；
- setUpdate都需要addStoredColumns

- 孙表根据判断setInsert（不需要addStoredColumns）或者setUpdate

- debug的时候 可以看到 修改  插入关键字  判断出有没有生效

- setInsert()  默认值需要用额外的service，不然主键没有

  ```java
  DefaultValueService defaultValueService = SpringBeanUtils.getBeanByName(DefaultValueService.BeanName);
  defaultValueService.setDefaultValues(objectEntry, true);
  ```

```java
for (String lrpicodex : lrpicodexSet)
{
    AdvPayOffGDetail advPayOffGDetail=lrpicodexAdvPayOffGMap.get(lrpicodex);
    if (advPayOffGDetail != null){
        advPayOffGDetail.setFcy(BigUtils.add(advPayOffGDetail.getFcy(), curFcy));
        advPayOffGDetail.setUpdate();
        advPayOffGDetail.addStoredColumns(new String[] {"fcy" });
    }else{
        //没有就新增，拷贝余额表对应记录，应忽略系统字段以及fcy金额
        AdvPayOffGDetail newAdvPayOffGDetail = new AdvPayOffGDetail();
        BeanUtils.copyProperties(lrpCorBalance,newAdvPayOffGDetail,ignoredProperties);
        newAdvPayOffGDetail.setInsert();
        advpayoffgdetails.add(newAdvPayOffGDetail);
    } 
    //这里统一更新存盘
    advPayOffDetail.setUpdate();
    advPayOffDetail.addStoredColumns(new String[] {"advpayoffgdetails" });
}    
advPayOff.addStoredColumns(new String[] {"advpayoffdetails" });
advPayOff.setUpdate();
busiAccessService.save(advPayOff);

```

