### 1、生成db 定义



~~~java
请根据下面的表格，按照如下格式生成对应的column，比如表格里面的字段名列对应name,字段标题列对应title，数据类型列对应type

```
<column name="fcode"  title="交单币种" type="CHAR(SZFCODE)" />
```



| ***\*字段名\**** | ***\*字段标题\****             | ***\*数据类型\****     |
| ---------------- | ------------------------------ | ---------------------- |
| tgticode         | 目标表主键                     | VARCHAR(SZIBILL)       |
| tgttbl           | 目标表名                       | VARCHAR(SZTABLE)       |
| salshipssicoder  | 销售发货单分摊收款方式原始内码 | VARCHAR(SZIBILL)       |
| fcy              | 原币金额                       | NUMERIC(MNYINT.MNYDEC) |
~~~



### 2、生成inf

请根据下面的数据，将table标签的name拼接上每个column的name作为key，对应每个column的title作为value，
生成类似这样的数据：ft_docu_dlyd.dlydicode=议付交单主表内码 
注意不要拼接引号

```
<table id="38008" name="ft_docu_dlydg_cb" title="发货单对议付交单明细核销余额表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="salshipssicoder" title="销售发货单分摊收款方式原始内码" type="VARCHAR(SZIBILL)" primkey="true"/>
		<column name="corflag" title="标识" type="SMALLINT" />
		<column name="ccode" title="客户" type="VARCHAR(SZCCODE)" />
		<column name="salordcode" title="销售合同号" type="VARCHAR(SZNBILL)" />
		<column name="salordicode" title="销售合同号内码" type="VARCHAR(SZNBILL)" />
		<column name="salprjicode" title="销售业务编号内码" type="VARCHAR(SZNBILL)" />
		<column name="salprjcode" title="销售业务编号" type="VARCHAR(SZNBILL)" />
		<column name="srcsheetcode" title="来源单据类型" type="VARCHAR(SZSHEET)" />
		<column name="srcicode" title="来源单据内码" type="VARCHAR(SZIBILL)" />
		<column name="srccode" title="来源单据号" type="VARCHAR(SZNBILL)" />
		<column name="salshipcoder" title="发货单号" type="VARCHAR(SZNBILL)" />
		<column name="salshipicoder" title="发货单内码" type="VARCHAR(SZIBILL)" />
		<column name="tsosicoder" title="出仓单号内码" type="VARCHAR(SZIBILL)" />
		<column name="tsoscoder" title="出仓单号" type="VARCHAR(SZNBILL)" />
		<column name="abgoods" title="货前后" type="VARCHAR(SZTYPE)" />
		<column name="paymode" title="收款方式" type="VARCHAR(SZRPCODE)" />
		<column name="fcode" title="币种" type="VARCHAR(SZFCODE)" />
		<column name="overseas" title="境内外" type="VARCHAR(SZDICT)" />
		<column name="fcy" title="计划执行金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcyed" title="已执行金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcying" title="待执行金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="purordcode" title="采购合同号" type="VARCHAR(SZNBILL)" />
		<column name="purordicode" title="采购合同号内码" type="VARCHAR(SZIBILL)" />
		<column name="purprjicode" title="采购业务编号内码" type="VARCHAR(SZIBILL)" />
		<column name="purprjcode" title="采购业务编号" type="VARCHAR(SZNBILL)" />
		<column name="purshipicoder" title="到货单号内码" type="VARCHAR(SZIBILL)" />
		<column name="purshipcoder" title="到货单号" type="VARCHAR(SZNBILL)" />
		<column name="gcode" title="商品编码" type="VARCHAR(SZGCODE)" />
		<column name="cnamedesc" title="商品名称" type="VARCHAR(SZGNAME)" />
		<column name="enamedesc" title="商品英文名称" type="VARCHAR(SZGNAME1)" />
		<column name="gvcode" title="商品细类" type="VARCHAR(SZGVCODE)" />
		<column name="wcode" title="业务员" type="VARCHAR(SZWCODE)" />
		<column name="bcode" title="业务员部门" type="VARCHAR(SZBCODE)" />
		<column name="corpbcode" title="公司" type="VARCHAR(SZBCODE)" />
		<column name="lccode" title="信用证号" type="VARCHAR(SZLCCODE)" />
		<column name="lcregicode" title="到证登记及认领内码" type="VARCHAR(SZIBILL)" />
		<column name="trademode" title="贸易方式" type="VARCHAR(SZTYPE)" />
		<column name="stockdate" title="出仓日期" type="DATE" />
		<column name="isaossd" title="是否提前交单" type="CHAR(SZYN)" />
		<column name="vprepare" title="创建人" type="VARCHAR(SZUCODE)" />
		<column name="predate" title="创建时间" type="DATE" />
		<column name="modifydate" title="修改时间" type="DATE" />
		<column name="modifier" title="修改人" type="VARCHAR(SZUCODE)" />
	</table>
```



### 3、生成注释

根据下面的column的name找到对应的DocuDelivery实体VO里面对应的属性，加上注释，注释内容为title,注释格式如下

```java
	/**承兑赎单内码*/
	@Id
	@NotNull
	@Column
	@DefaultValue(DefaultConsts.dft_object_id)
	private String acdicode;
```

特别注意注释格式要按我说的处理，处理好以后将完整带注释的实体VO发给我

```java
<column name="dlydsicode" title="收款方式内码" type="VARCHAR(SZIBILL)" primkey="true"/>
		<column name="dlydicode" title="议付交单主表内码" type="VARCHAR(SZIBILL)" notnull="true" />
		<column name="abgoods" title="货前后" type="VARCHAR(SZTYPE)" />
		<column name="paymode" title="付款方式" type="VARCHAR(SZRPCODE)" />
		<column name="fcy" title="交单金额" type="NUMERIC(MNYINT.MNYDEC)" />
```



```java
public class DocuDeliveryAc{
	@Id
	@Column
	@DefaultValue("Accode:FT-DOCU.InnerCode")
	private              String     dlydsicode;
	@Column
	private              String     dlydicode;
	@Column
	private              String     abgoods;
	@Column
	private              String     paymode;
	@Column
	private              BigDecimal fcy;
```







### 4、生成随机表数据

> 我的表结构如上，请采用oracle的pl/sql生成有规律的6条数据，注意NUMBER类似都是整数，还需要特别注意检查生成的字段长度不能超过定义的最大值



### 5、字段展示成md

收付项目
发货单号
出仓单号
销售合同号
销售业务编号
采购合同号
采购业务编号
到货单号
融资金额
融资利息
融资手续费
商品类目
业务员
业务员部门
汇率
本位币币种
折人民币汇率
折美元汇率
折本位币融资金额

> 将上述字段按要求输出成md表格，第一列是字段名，第二列是字段标题，第三列是数据类型；上述内容对应的是第二列字段标题，第一、第三列放空展示即可



#### 字段匹配md

第一个md表格

| 字段名 | 字段标题         | 数据类型 |
| ------ | ---------------- | -------- |
|        | 交单融资申请单号 |          |
|        | 部门-业务员      |          |
|        | 公司             |          |


第二个md表格

| 字段名      | 字段标题     | 数据类型               |
| ----------- | ------------ | ---------------------- |
| bankacccode | 融资银行账号 | VARCHAR(SZBANKACCCODE) |
| bankcode    | 融资银行     | VARCHAR(SZBANKCODE)    |


将上述第二个md表格去按照同名标题字段匹配第一个md表格，然后匹配上的信息插入到第一个md表格，最后输出匹配好的第一个md表格给我



### 6、去重字段

```java
以下###里面是我列出的所有字段信息，字段名就是前面的冒号：，比如a:ddd；字段名就是a，请注意如果有顿号、分开的话也是代表一个字段，比如a、b、c:ddd,这种字段有a，b,c3个字段,现在请帮我将下面所有字段去重复以后按序号输出，请注意要去重且不能遗漏任何字段
###
1.收付项目：显示名，引用【商户往来费用类型】码表；
2.发货单号、出仓单号、销售合同号、销售业务编号、采购合同号、采购业务编号、到货单号：；
3.融资金额：2位小数；
4.融资利息、融资手续费：虚列，2位小数，显示【费用明细】表对应费用类型的“金额”；
5.商品类目：显示名；
6.业务员、业务员部门：显示名；
7.汇率：隐藏；
8.本位币币种：隐藏，引用【币种】码表；
9.折人民币汇率：隐藏；
10.折美元汇率：隐藏；
折本位币融资金额：2位小数，隐藏，融资金额*汇率，尾差在最后一行处理。
###

```

### 7、初始化字段生成

```
create table MIG_DEF_G
(
    MIGCODE   VARCHAR2(32) not null,
    CUICODE   VARCHAR2(32) not null,
    TGTFLD    VARCHAR2(32) not null,
    TGTFLDNM  VARCHAR2(128),
    SORTIDX   NUMBER(10),
    VALDEF    VARCHAR2(64),
    VALTAC    VARCHAR2(1024),
    RMIGCODE  VARCHAR2(32),
    RJOINCOLS VARCHAR2(256),
    REMARK    VARCHAR2(256),
    DISABLED  NUMBER(3),
    constraint MIG_DEF_G_PK
        primary key (MIGCODE, CUICODE, TGTFLD)
)
/

我的表结构是上面这个，现在我有字段值如下所示，下面字段值的第一个对应字段名为TGTFLD，第二个中文对应TGTFLDNM，请帮我生成对应的insert语句，MIGCODE默认为MD_FT_DOCU_DLYD,CUICODE默认为C000000001，SORTIDX字段为序号，帮我根据下面列出的字段顺序从小到大排序，从10开始，间隔序号为前一个序号+10
dlydicode	议付交单申请单内码
dlydcode	议付交单申请单号
adate	申请日期
ccode	客户
fcode	交单币种
fcy	交单金额
dlyddate	交单日期
bankcode	交单银行
bankacccode	交单银行/账号
lccode	信用证号
lcregicode	到证登记及认领内码
isaossd	是否提前交单
senddate	寄单日期
signdate	签收日期
signstatus	签收状态
remark	备注
rdate	预计收款日期
invdate	发票日期
salinvcode	议付发票号
acdate	承兑日期
ccodeaddr	客户地址
rpbankcode	代收行
iccode	IC编号
isfirst	首次交单
islast	是否最后交单
rpbankaddr	代收行地址
vddate	有效日期
lsddate	最迟交单日期
isdis	是否不符点交单
distxt	不符点
lcbank	开证行
ntbankacccode	通知行
expresscode	寄单快递号
cscompany	快递公司
status	状态
wfcode	审批编码
wfuid	审批节点
bcode	业务员部门
wcode	业务员
corpbcode	公司
sheetcode	单据类型
cuicode	商户
ratifydate	生效时间
submitdate	提交时间
performdate	审核时间
vprepare	创建人
predate	创建时间
modifier	修改人
modifydate	修改时间
```



```
以下字段在第一组字段中存在，但在第二组字段中未找到：

ccode
bankacccode
salinvcode
islast
lcbank
以下字段在第二组字段中存在，但在第一组字段中未找到：

salccode
settdoccode
srcserialno
lcregcode
paymodelist
bankcode（第一组存在，但第二组的顺序靠后）
tgbicode
tgbcode
```

### 8、画界面xml

```
@res  search the codebase，参考里面的Entry结尾命名的xml文件，代码风格完全要一样，字段名字优先找出同名的去参考，找不到同名的就用原型里面的中文展示，帮我写出一个类似的实现，xml文件里面<m:DialogPane  代表的是查询条件；<m:GridTable 代表查询列表表格，现在给出我查询参数和列表字段如下，

资金池相关-【资金池部门余额查询】-查缺补漏

1.1.3.1.	查询参数
1.1.3.1.1.	〖基本参数〗
1、	公司：标准参数组件功能，选择时按本单据权限范围过滤数据，匹配主表同名字段，允许多选，按路径包含匹配单据主表同名字段；
2、	部门：编辑禁止，显示名，选择时按本单据权限范围过滤数据，选择【部门】码表，允许选择中间级，按路径包含匹配单据主表同名字段；
3、	信用证号：模糊匹配主表同名字段；
4、	客户：编辑禁止，显示名，选择权限范围内所有【客商】码表，匹配主表同名字段；
5、	议付交单申请单号：模糊匹配主表同名字段；
6、	未到账金额从、未到账金额到：保留2位小数；
7、	实际收款日期从、实际收款日期到：日期辅助录入；
8、	截止日期：必录项，日期辅助录入，缺省‘当前系统日期’，匹配主表“来款日期”、“融资日期”字段；
9、	预计收款日期从、预计收款日期到：日期辅助录入；
10、	交单日期从：日期辅助录入，匹配主表同名字段，需根据[入口日期查询参数默认当前日期前多少个月]选项值设置缺省值参数值；
11、	交单状态：编辑禁止，显示名，选择{交单状态}系统字典，匹配主表同名字段；
12、	收款方式：编辑禁止，显示名，选择【收付款方式】码表，匹配主表同名字段；

表格
1.1.3.2.	数据
1.1.3.2.1.	数据显示要求
1、 公司：显示名，取【议付交单申请】主表同名字段；
2、 部门：显示名，取【议付交单申请】〖交单明细〗“业务员部门”；（多个时多条展示）
3、 议付交单申请单号：取【议付交单申请】主表同名字段；
4、 交单日期：降序，取【议付交单申请】主表同名字段；
5、 到期月份：取“预计收款日期”的年月；
6、 预计收款日期、承兑日期：取【议付交单申请】主表同名字段；
7、 销售业务编号、销售合同号：取【议付交单申请】〖交单明细〗同名字段值；（多个时多条展示）
8、 交单银行/账号：显示名，引用【外部银行账号】码表，取【议付交单申请】主表“交单银行/账号”字段值；
9、 信用证号：取【议付交单申请】主表同名字段值；
10、 议付发票号：取【议付交单申请】主表同名字段值；
11、 开证行：显示名，引用【开户银行】码表，取【议付交单申请】主表同名字段值；
12、 收证金额：2位小数，依据“到证登记及认领内码”取【到证登记及认领】主表同名字段；
13、 交单状态：  显示名，引用{交单状态}系统字典（已交单、已承兑、已收款、已融资），缺省‘已交单’，并按如下规则赋值：
(1)	用参数“截止日期”比对“”
(2)	【议付交单申请】〖交单承兑确认〗存在‘承兑’时，值修改为‘已承兑’；
(3)	“到账金额”不为0时，值修改为‘已收款’；
(4)	“融资下款金额”不为0时，值修改为‘已融资’；
14、 有效日期：取【议付交单申请】主表同名字段；
15、 客户：取【议付交单申请】主表同名字段；
16、 是否内部客商：显示名，引用{是否}系统字典，依据“客商编码内码”取【客商】码表，“是否内部客商”为‘是’，赋值为‘是’，否则赋值为‘否’；
17、 单价：取“发货单号”“商品名称”对应“单价”，存在多个时按‘，’拼接显示；
18、 数量：取“发货单号”“商品名称”对应“签约数量”，存在多个时按‘，’拼接显示；
19、 商品名称：取【议付交单申请】〖交单明细〗“商品编码”字段值，存在多个时按‘，’拼接显示；
20、 创建人：显示名，取【议付交单申请】主表同名字段；
21、 收款方式：显示名，引用【收付款方式】码表，取【议付交单申请】〖交单明细〗同名字段，存在多个时按‘，’拼接显示；
22、 币种：引用【币种】码表，取【议付交单申请】主表“交单币种”字段值；
23、 交单金额：2位小数，取【议付交单申请】〖交单明细〗同名字段值；
24、 到账金额：2位小数，依据“议付交单申请单号内码”取实收往来原币金额字段值；
25、 未到账金额：2位小数，缺省‘交单金额-到账金额’；
26、 出口扣费：2位小数，依据“议付交单申请单号内码”取实收往来对应的“银行手续费”字段值；
27、 网银流水号：依据“议付交单申请单号内码”取实收往来同名字段；
28、 来款日期：依据“议付交单申请单号内码”取实收往来同名字段；
29、 交单融资类型：显示名，引用{交单融资类型}系统字典（值集参见象屿标准规范管理表-字典），依据“议付交单申请单号内码”取【交单融资处理工作台】〖放款信息〗“交单融资类型”字段值；
30、 申请融资金额：2位小数，依据“议付交单申请单号内码”取【交单融资申请】〖放款分摊明细〗“申请融资金额”字段值；
31、 融资下款金额：2位小数，依据“议付交单申请单号内码”取【交单融资申请】〖放款分摊明细〗“申请融资金额”-“融资利息”-“融资手续费”  ；
32、 是否有追索权：显示名，引用{是否}系统字典，依据“议付交单申请单号内码”取【交单融资申请】〖放款信息〗同名字段值；
33、 融资利息、融资手续费：2位小数，依据“议付交单申请单号内码”取【交单融资申请】〖放款分摊明细〗同名字段值；
34、 融资利率：百分数显示，2位小数，依据“议付交单申请单号内码”取【交单融资申请】〖放款信息〗同名字段值；
35、 融资日期：依据“议付交单申请单号内码”取【交单融资申请】〖放款信息〗“融资起始日”字段值；
36、 融资到期日：依据“议付交单申请单号内码”取【交单融资申请】〖放款信息〗同名字段值；
37、 备注：超长漂浮显示，取【议付交单申请】主表同名字段值。


```











## 必备字段

| remark        | 备注           | VARCHAR(SZTEXT)    |
| ------------- | -------------- | ------------------ |
| status        | 状态           | VARCHAR(SZSTATUS)  |
| wfcode        | 审批编码       | VARCHAR(SZWFCODE)  |
| wfuid         | 审批节点       | VARCHAR(SZWFUID)   |
| ccode         | 客户           | VARCHAR(SZCCODE)   |
| bcode         | 业务员部门     | VARCHAR(SZBCODE)   |
| wcode         | 业务员         | VARCHAR(SZWCODE)   |
| corpbcode     | 公司           | VARCHAR(SZBCODE)   |
| vsn           | 版本号         | SMALLINT           |
| vsnflag       | 版本标记       | SMALLINT           |
| vsntype       | 版本修改类型   | VARCHAR(SZTYPE)    |
| curratifydate | 本版本生效时间 | DATE               |
| sheetcode     | 单据类型       | VARCHAR(SZSHEET)   |
| cuicode       | 商户           | VARCHAR(SZCUICODE) |
| ratifydate    | 生效时间       | DATE               |
| submitdate    | 提交时间       | DATE               |
| performdate   | 审核时间       | DATE               |
| vprepare      | 创建人         | VARCHAR(SZUCODE)   |
| predate       | 创建时间       | DATE               |
| modifier      | 修改人         | VARCHAR(SZUCODE)   |
| modifydate    | 修改时间       | DATE               |

> 内码一定要设置，单据相关字段也设置，避免有问题
>
> 下面应该还有公司：自行判断

```java
<c name="status" title="${RES.C}" sqltype="12" width="${E.G.CW.status}" codedata="#SN-PLAT.status"
showname="true"
cmparams="sheetcode:'FT-DEMO.CcodeStopApply'" rdonly="true"/>
<c name="bcode" title="${b_applywcode}" sqltype="12" width="${E.G.CW.bcode}" codedata="#FT-ORGZ.BWcode" disableed="true"
showfname="true" tipIfOverflow="true" uiprops.textDir="rtl" aidInputerBtn="true" submitOnInput="true" noblankOnSubmit="true"
cmparams.sheetcode="FT-CCODE.StatusChange" cmparams.opids="C" disableDelIfAI="true"
uiprops.renderer="new snsoft.plat.busi.comm.BusiBWcodeNameRender({})" />
<c name="wcode" title="${RES.C}" sqltype="12" codedata="#FT-ORGZ.Wcode" showname="true" width="${G.CW.wcode}" hidden="true"/>

<c name="cuicode" sqltype="12" hidden="true"/>
<c name="sheetcode" sqltype="12" hidden="true"/>

<c name="ratifydate" title="${RES.C}" sqltype="93" width="${E.G.CW.time}" hidden="true"/>
<c name="submitdate" title="${RES.C}" sqltype="93" width="${E.G.CW.time}" hidden="true"/>
<c name="performdate" title="${RES.C}" sqltype="93" width="${E.G.CW.time}" hidden="true"/>
<c name="vprepare" title="${RES.C}" sqltype="12" hidden="true"/>
<c name="predate" title="${RES.C}" sqltype="93" hidden="true"/>
<c name="modifier" title="${RES.C}" sqltype="12" hidden="true" modifierColumn="true"/>
<c name="modifydate" title="${RES.C}" sqltype="93" hidden="true" modifydateColumn="true"/>
<c name="wfcode" title="${RES.C}" sqltype="12" hidden="true"/>
<c name="wfuid" title="${RES.C}" sqltype="12" hidden="true" codedata="#SN-APPR.wfunit" showname="true"
tipIfOverflow="true"
xprops.CodeData.KeyNames="wfcode"/>
```

