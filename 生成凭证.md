## 凭证相关概念

- 接口配置：cfg/sna/model/vminf/FT-LOAN.DocuDeliveryFinaLIVMark.xml
- queryDataSource重写：DocuDeliveryFinaLIVMarkServiceImpl
- 凭证配置：界面配置参考别人的
- 

## 凭证相关VO

> 1. 每个业务单据可能对应一或多个制凭证标记表。
> 2. 每种制凭证标记表都认为是一个单据，应该有自己的sheetcode
>
> 资金组凭证标记表`FundVMark`、`FundVMarkG`,各个业务需求生成凭证标记表时需要继承`FundVMark`类定义自己的sheetcode，子表不需要额外定义

```java
@Setter
@Getter
@Table(name = FundVMark.TableName)
@SheetInfo(DocuDeliveryFinaLIVMark.SheetCode)
public class DocuDeliveryFinaLIVMark extends FundVMark
{
	@Serial
	private static final long	serialVersionUID	= -2394536245089812255L;
	public static final String	SheetCode			= "FT-LOAN.DocuDeliveryFinaLIVMark";
	
}
```



## 红冲凭证

> - 放款凭证一个业务单据，还款一个业务单据，一个sheetcode、一个srcsheetcode。
>
> - 红冲的时候，分2种情况，假设原来平行本部都已做好凭证，此时点击某一行红冲，会将两个都整体一起红冲；假设原来只有其中一个平行或者本部做好凭证，点击某一行红冲会将当前行红冲掉，另一行实际是作废？生成一个vid=-1的数据？

redflag  0：  根据已经生成的凭证，再去红冲这个凭证，会处理redflag--r/o字段都会处理

1：复制标记表，然后根据标记表去生成凭证--不处理redflag,会处理r/o字段 

2：根据红冲的业务单据生成标记表（可能部分红冲），再根据标记表生成凭证 





## 凭证配置

> 不需要配置单币种

<img src="E:\Apple\TyporaMD\Img\image-20241112100433860.png" alt="image-20241112100433860" style="zoom:50%;" />



我的子表有2个，就配置2种分录信息，所以借贷共4个，数据源分别取自不一样的地方

![image-20241112100503291](E:\Apple\TyporaMD\Img\image-20241112100503291.png)

### 数据源定义

queryDataSource里面是采用union all合并2种信息的，这里数据源分开配置

```java
select
			null keptbcode,null vmarkicodex,'FT-LOAN.DocuDeliveryFinaRI' sheetcode,'FT-LOAN.DocuDeliveryFinaRI' srcsheetcode,null srccode,
			ri.dlydfariicode srcicode,ri.corpbcode corpbcode,ri.bcode bcode,ri.year year,ri.month month,ri.tcaptime tcaptime,
			ri.vprepare vprepare,ri.predate predate,null cuicode,'70' status,lrp.lrpicode srcgicode,0 innertype
			from ft_loan_dlydfari ri,ft_lrp_lrp lrp
			where ri.dlydfariicode = lrp.srcicode
			and ri.dlydfariicode in(:dlydfariicode)
			union all
			select
			null keptbcode,null vmarkicodex,'FT-LOAN.DocuDeliveryFinaRI' sheetcode,'FT-LOAN.DocuDeliveryFinaRI' srcsheetcode,null srccode,
			ri.dlydfariicode srcicode,ri.corpbcode corpbcode,ri.bcode bcode,ri.year year,ri.month month,ri.tcaptime tcaptime,
			ri.vprepare vprepare,ri.predate predate,null cuicode,'70' status,bf.bfeeicode srcgicode,1 innertype
			from ft_loan_dlydfari ri,ft_fee_bfee  bf
			where ri.dlydfariicode = bf.srcicode
			and ri.dlydfariicode in(:dlydfariicode)
```

```java
from ft_loan_dlydfari m inner join ft_lrp_lrp g on m.dlydfariicode = g.srcicode
inner join ft_fund_vmark v on m.dlydfariicode = v.srcicode 
where  
v.vidflag = 0 
and v.sheetcode =  'FT-LOAN.DocuDeliveryFinaRI' 
and  m.dlydfariicode in (${SRCICODES}) 
and v.keptbcode = '${KEPTBCODE}' 
and v.vmarkicode in (${VMARKICODES})
```

```java
from ft_loan_dlydfari m inner join ft_fee_bfee g on m.dlydfariicode = g.srcicode
inner join ft_fund_vmark v on m.dlydfariicode = v.srcicode 
where  
v.vidflag = 0 
and v.sheetcode =  'FT-LOAN.DocuDeliveryFinaRI' 
and  m.dlydfariicode in (${SRCICODES}) 
and v.keptbcode = '${KEPTBCODE}' 
and v.vmarkicode in (${VMARKICODES})
```



```java
delete from ft_loan_dlydfali 
delete from ft_loan_dlydfari 
```



```java
update sna_acc_ivou  set  odate=SYSDATE where vid='674ff826353b250ea03938bf'
update sna_acc_hvou   set status='75' where vid='674ff826353b250ea03938bf'
update sna_acc_ivou set odate=SYSDATE    where vexpl like '%交单融资%'
```

```java
delete from sna_acc_hvou  where vremark like '%交单融资%'  or vprepare ='C00000000100047' or modifier ='C00000000100047'
```

### 总账账期开启

- 将对应记账部门账期开启

- *记账部门(本部)与公司及部门关系*   如果要生成农产的，就公司和部门配置在里面的

![image-20241204152421342](E:\Apple\TyporaMD\Img\image-20241204152421342.png)



## 自动化凭证---如何实现

凭证模板界面--->异步回写已制凭证标识,这个如果勾上了，会发异步消息，还需要在消息模板里面去消费，所以在本地的话就不要去勾上了！！

凭证模板界面-》适用范围，在自动生成凭证选项打钩！

### 开发注意点

如果是单据生效自动生成凭证标记表的话，直接配置单据监听即可，

```
<!-- 单据生效生成制凭证标记表 -->
<bean code="FT-FUND.100" sortidx="100" impl="#SNA-VM.VMarkCommSVListener?[{sheetcode:'FT-RDOC.TransIn',noRed:'true'}]"/>
```

如果需要自己手动触发，则可参考`createBWcodeChnageSettVamrk`,手动提交生效`asynSubmitVMarks`,这个方法其实就是将状态改为70

```
List<VMark> vMarks = markService.createVMark(vkParams);
VMarkUtils.asynSubmitVMarks(vMarks);
```

上面已经将凭证标记表提交生效了，此时只需要配置对应的存盘监听，发送消息处理，真正的消费者处理为`VMarkAutoVoucherDBListener`

```
cfg/sheet/FT-TSM.TStockBWcodeChangeVMark/FN-sv.save.xml：

<name>跨部门库存转移单凭证标记表存盘监听</name>
<remark></remark>
<properties>
    <property name="sheetcode" value="FT-TSM.TStockBWcodeChangeVMark"/>
</properties>
<beans>
    <bean code="SNA-ACC.10" sortidx="10" impl="#SNA-VM.VMarkStatutSVListener"/>
</beans>
```

里面会发送消息（MQ）：AsyncUtils.sendMessage(msg);

此时如果消息想要重发的话,可以用TAC直接重发一条!

```
msg = new snsoft.api.async.vo.AsyncSendMessage()
msg.setMessagetype("SNA-VM.VMark")
msg.setRequester("SNA-VM")
msg.setResponder("SNA-VM")
msg.setParameter("sysid", "FT-TSM")
msg.setParameter("cuicode", "C000000001")
msg.setParameter("usercode","C00000000100002")
msg.setParameter("wsid","00")
msg.setParameter("sheetcode", "FT-TSM.TStockBWcodeChange")
msg.setParameter("srcicode","67ea5b3595b27d5d62a06deb")
msg.setParameter("vmarkicodes","67ea5d52f3e0c96c5c534a0e")
snsoft.dx.async.util.AsyncUtils.sendMessage(msg)
```

