> 南北技术WIKi/开发手册/10入门答疑区/事业部与部门关系:    http://wiki.xiangyu.com/xwiki/wiki/sn/view/%E5%8D%97%E5%8C%97%E6%8A%80%E6%9C%AFWIKi/%E5%BC%80%E5%8F%91%E6%89%8B%E5%86%8C/10%E5%85%A5%E9%97%A8%E7%AD%94%E7%96%91%E5%8C%BA/%E4%BA%8B%E4%B8%9A%E9%83%A8%E4%B8%8E%E9%83%A8%E9%97%A8%E5%85%B3%E7%B3%BB/
> [第09章-基础资料](http://wiki.xiangyu.com/xwiki/wiki/sn/view/南北技术WIKi/02开发手册/000N9技术文档/第9章-基础资料/)

- 岗位也叫角色，企事业单位中承担一系列工作职责的某一任职者所对应的角色
- 部门是企事业单位中，分管或管辖一定专门事务而成立的组织机构，例如：财务部、行政部、运行部、人力资源部等。
- 用户即为系统的用户，主要是为系统登录使用



**权限单据概念**

- 权限单据指：南北这边指配置有权限的单据。该单据配置的权限，（可以理解为查询限制）根据操作员所有权限过滤（权限字段包含：公司、部门、业务员、商户），显示操作员可查询范围内的数据。
- 权限配置，主要配置权限单据。权限单据可以与该界面定义挂的单据不相关，案例：27.25.90.10.105.XY   在途查询报表，配置权限过滤 权限单据为27.25.90.10。

> 菜单查看：
>
> - ERP部门
> - ERP法人组织
> - 权限组
> - ERP岗位
> - 岗位定义
> - 岗位-角色
> - ERP人员
> - **菜单模板**

法人组织（单纯展示，全名）--事业部---部门，人员会关联具体的用户，理解为账号信息

```
新增人员的时候wcode,users,wbcode插入新数据，wcode，users表数据同步，并在wbcode表中建立人员和组织的关系数据
```


erp部门下面--挂人员，erp人员-->也会关联部门岗位，具体的岗位去岗位定义查看（或者**岗位-角色**也有？）

> ERP部门 ，可以挂人员：erp部门下面--挂人员
> **Ø 建立部门与人员的关系的时候必须指定对应的岗位**

![image-20240612095715297](E:\Apple\TyporaMD\Img\image-20240612095715297.png)

> ERP人员挂 部门岗位信息：  **类似用户对应了某个具体的角色，这里是人员对应的某个岗位**

![image-20240612095934654](E:\Apple\TyporaMD\Img\image-20240612095934654.png)



> 岗位定义和ERP岗位好像差不多？针对查看的东西：  
> **这里类似就是角色对应某些具体的权限，首先用户关联角色，角色关联权限；这里是人员关联岗位，岗位关联权限组（权限的组合）！！！**
>
>
> 这里的300文员  323文员是指 公司名称---对应的文员权限组？？

![image-20240612100018971](E:\Apple\TyporaMD\Img\image-20240612100018971.png)

> 权限组关联上单据和菜单

![image-20240612105308634](E:\Apple\TyporaMD\Img\image-20240612105308634.png)



> 菜单模板关联  权限组

![image-20240612101331895](E:\Apple\TyporaMD\Img\image-20240612101331895.png)





> corpbcode  里面是公司法人组织的具体信息，它的DTYPE只有=02  代表法人组织
> bcode 是可以关联DTYPE=01 ，02  ，03    	01 部门  02法人
> mbcode是事业部层级

```java
select m.bcode,m.mpath,m.pbcode,m.levl,m.dtype,b.langname1 from mbcode m left join bcode b on m.bcode=b.bcode where m.dtype='01' and m.bcode=:bcode

select pbcode from mbcode where bcode=:bcode

```

![image-20240612101731911](E:\Apple\TyporaMD\Img\image-20240612101731911.png)

```
bcode = 部门信息表

mbcode -组织信息关系表

pbcode = 事业部

其中：bcode 和 mbcode 关系是1比1
```



**所以实际开发完毕，只需要把单据挂到对应的权限组即可？？**

**那按钮权限如何控制？？**

```
监听：snsoft.ftcs.plat.bas.ui.UILimitUIListener.new?prefix=p&sheetCode=27.15.20.60;  
该监听参数说明：prefix  为 主表别名；sheetCode  权限单据号。这个权限单据号需要跟顾问确认，（开发不能给单据配置权限）让顾问确认，配置那个权限单据号过滤该界面查询的权限。
```



权限过滤配置后查看是否生效

登陆非管理员账户，查询后查看Table属性，若查询sql有拼接权限过滤条件，则可以判断权限过滤生效。具体是否正确交由顾问测试

![1534247721400-589.png](E:\Apple\TyporaMD\Img\1534247721400-589.pngwidth=1264&height=531&rev=1.png)
