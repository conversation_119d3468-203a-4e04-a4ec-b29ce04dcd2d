### 1、无效列

- 界面有，但是数据库没有查出来-没有实体类@Column 注解
- 界面挂错了，mainui 指向了子表  报错了子表无效列 sheetcode，实际上要主表才有这个字段！

### 2、文档打印： Cannot invoke "Object.hashCode()" because "key" is null

> 需要配置 `cfg/res/doctype/voref/VORefFT-RPADJ.xml`

### 3、snsoft.ui.ex.UIException: 禁止单独存储子表 [FT-RPADJ.AdvPayOffDetail#ft_rpadj_offg]

```
if (masterUIComp != mUIComponent && masterUIComp.getExtProperty("SaveDataService") != null && mUIComponent.getExtProperty("SaveDataService") == null)
{
/**
* 如果存在主表存盘组件，子表必须与主表一起存盘，否则权限无法校验。
* 同时也是防止URL注入。
* 请在主表增加修改人、修改时间字段，并布局，用以保证存盘时主表数据修改，根据主表进行存盘。
*/
throw new UIException("100", _this.getRootComponent().uiid, mUIComponent.uiname);
}
```

### 4、新建单据默认不会查出子表数据---过滤 is null

- 首先要配置拷贝主表字段
- 其次要 `precompiled = true`

```java
@SqlColumn(column = "officode", precompiled = true)
private String officode;
```

> 总结：对比别人的有啥不一样？？？？   发现这个地方不一样，但是另外一个子表没配置也可以，还是可以试试。。？   

### 5、initValue 部门公司有值，客商为空

要在表头（就是公司部门那个表头！）把对应字段赋值上去

### 6、分析模板按钮一直不显示

```java
<m:Toolbar name="toolbar" uiprops.cellClassName="toolbar-panel" region="north"></m:Toolbar>
```

但是里面配置的属性一直是另外一个导致找不到！！

```java
uiprops.attachTbName="toolbar"
```

### 7、dlyd  undefied 报错： 以及 token 报错

undefied 报错：这个前端 console 有报错，点击前端查看，点进去的时候发现是某个监听读取不到导致报错：snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener

以及 token 报错： 这个前端 console 没有报错，这个就去后端看，后端断点到最顶层，然后跟踪堆栈查看！

### 8、报错 Cannot read properties of null (reading 'getFullYear')

> **原因是因为没有对签收日期判空，测试的时候当做直接有值---前后端都是！**
> 注意判空！！！

```java
docuDelivery.setSigndate(DateUtils.toDate(invokerParam.parameter.get("signdate")));
docuDelivery.setSenddate(DateUtils.toDate(invokerParam.parameter.get("senddate")));
docuDelivery.setSignstatus(StrUtils.obj2str(invokerParam.parameter.get("signstatus")));


Date signdate = this.dataSet.getValue("signdate", r);
if ($bool(senddate)){
    signdate = new Date(signdate.getFullYear(), signdate.getMonth(), signdate.getDate());
}
```

### 9、Cannot invoke "Object.hashCode()" because "key" is null

> 生成的数据流程号workflow默认也有了导致报错

> 附件上传报错这个是因为没有配置对应的 doctype

> include 接口日志配置问题

### 10、看堆栈

> 发现很多同学不懂怎么看异常，java 看异常不是看最上面的，是要看最后一个 `Caused by: ` 后面的异常，这个是根本原因。所以提问或者咨询需要提供全部异常。
> https://easyexcel.opensource.alibaba.com/qa/

