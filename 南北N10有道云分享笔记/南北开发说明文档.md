# 监听

界面中一般会配置三个监听：

- VOListener：默认值 + 录入校验规则

- SheetListener：界面权限控制

- UIOptCtrlListener：界面属性控制

底层提供了多种监听类，可以参考[常用类_方法_标签](note://WEB514b439bd8bc9dd996e013d1f6712585)的“底层监听类”一节

## 声明

监听分为 JS 监听和 UI 监听，在界面需要分别配置

其中 JS 监听需要编译成 JS 文件，UI 监听则不需要

- 监听类声明

- JS 监听类具体声明步骤，参考

- UI 监听声明

1. 在 UI 工程下，java/snsoft/项目名/项目名UIListener.java

1. 继承 snsoft.ui.DefaultUIListener

```java
public class ClassInfoUIListener extends DefaultUIListener {
    String sheetCode;
    public ClassInfoUIListener (Map<String,Object> parameter) {
        sheetCode = (String)parameter.get("sheetCode");
    }
}
```

- 界面配置

- <![CDATA[]]>不是必须，只是为了防止特殊字符

- JS 监听：#new 监听类全路径名，可以加参数，({student:‘eugenema'})

- UI 监听：类全局路径名.new?student=eugenema&name=mayinguang

- 参数可通过构造方法获取

```xml
<m:GridTable>
    <jslistener>
        <![CDATA[
            #new snsoft.start.school_manager.ClassInfoJSListener({})
            #new snsoft.plat.bas.busi.StatusJSListener({})
        ]]>
    </jslistener>
    <uilisteners>
        <![CDATA[
            //多个监听，需要加分号
            snsoft.ui.optctrl.UIOptCtrlListener.new
        ]]>
    </uilisteners>
```

## 界面内JS

可以在不创建监听类的情况下，直接在界面内写 JS 代码

只能为某个底层已有监听方法赋值函数，例如下方例子就是对“onTableCellRender”监听方法写了更改行颜色的函数

同样的，也可以为“dataSetFieldPosted“、”oncmd_XXX“赋值函数

关于工具类方法的调用，可参考用了该方法的监听，在其编译后的 JS 文件中，查看如何调用。下方举例说明 DateUtils 的调用方式

```javascript
<jslistener>
    <![CDATA[
        #new snsoft.plat.bas.viewdetail.ViewDetailListener({});
        onTableCellRender: function(table, e) {
            alert("nnnn");
            // 设置逾期数据背景色为黄色
            let notshipqtc = table.getDataSet().getValue('notshipqtc', e.row);
            let deliverydate = table.getDataSet().getValue('deliverydate', e.row);
            // 调用工具类方法
            let curDate = Xjs.util.DateUtils.nowDay();
            if (deliverydate < curDate && notshipqtc > 0) {
                e.renderInfo.background = 'yellow';
            }
        }
    ]]>
</jslistener>
```

## 监听中的其他监听相关

### 调用其他监听的静态方法

调用其他监听类的静态方法，需要先加载该监听类，否则会报 undefined 异常

```java
JsLoad.loadJsLibsOfVar(静态方法所在监听类.class.getName());
```

### 获取界面上配置的监听

可以拿到界面中已经配置的监听，已经初始化好了。即界面中配置的参数已经传递了

```java
table.getListener(监听类.class)
```

### 反射执行监听方法

拿到其他监听后，若想调用该监听的方法，可以通过反射实现

## 获取界面任意表

在 JS 中，继承了 DefaultListener 之后，会有一个方法：getTable

该方法可以通过根表，获取界面的任意表

```java
Table sonTable = getTable(table.getRootTable(), "表名");

// 也可以通过任意 table 获取其他 table
table.getRootComponent().getMainComponentByName("表名")
```

## 常用判断

### 必须选中行

```java
// 必须勾选且只能勾选一行数据，否则统一提示该错误
// rows 是选中的行号
int[] rows = table.getSelectedRowNumbers();
if (rows == null || rows.length == 0)
{
    throw new js.Error(ResBundle.getResVal("FT.00000048"));
} else if (rows.length > 1)
{
    throw new js.Error(ResBundle.getResVal("FT.00000087"));
}
// 本单据“状态”为“70：生效”
String status = dataSet.getValue("status", rows[0]);
```

## 异常/错误弹窗

在监听类中，可以抛出异常，用来终止错误操作。若在值改变前抛出异常，可以阻断值改变的一切监听

![](images/WEBRESOURCEf91367f8a2797496b6fdf5beab0c2d54截图.png)

```java
//判断日期，若不符合规则，则抛出异常。并且该异常能够阻止光标从当前输入框移除，以及阻止保存
public void dataSetFieldPosted(DataSet dataSet, DataSetEvent event) {
    //1、判断是否为出生日期列改变
    if (event.columnName.equals("birthday")) {
        //2、判断值是否超过当前日期
        Date value = dataSet.getValue(event.columnIndex);
        if ($gt(value, DateUtils.nowDay())) {
            dataSet.setValue(event.columnIndex, null);
            //或者 throw new JSException(String message);
            throw new js.Error("出生日期设置为误，请重新设计！");
        }
    }
}

//值改变后，弹出询问框，阻止值改变触发的一切监听事件，确保询问框用户选择取消后，不会改变数据
public void dataSetFieldPosted() {
    if (startTaxCtrl && this.phyDataSet.getRowCount() > 0) {
         //弹出询问框
         UIUtil.showYesNoDialog(null, "街道与以下仓库重复", null)
    }
    throw JSException.newDummy();
}
```

## 参数传递

ui 监听可以存入参数。在前端 js 监听中获取

```java
// UI 监听
@Override
public void initComponent(UIEvent event, Map<String,Object> values)
{
    setClientEnvParams(event, "_RPModes", rpmodes);
}

// JS 监听
JSObject[] rpModes = window.EnvParameter.$get("_RPModes");
```

## 界面弹出

### 弹窗形式

可以通过 JS 监听，实现点击按钮后，以弹窗形式展示某一界面

![](images/WEBRESOURCE14af518257b53daa385dd38249a32f9aGIF%202023-1-30%2011-16-05.gif)

#### 调用界面

```xml
<ToolbarBtnGroup name="relquery" noClientComponent="true" title="新建" uiprops.className="ui-btn-group head" xprops.dftBtnCls="ui-head-btn" xprops.dftBtnIconCls="icons-btn-query">
    <ToolbarBtn name="ft_set_btn1" noClientComponent="true" title="新建1" uiprops.cellClassName="ui-head-btn"/>
    <ToolbarBtn name="ft_set_btn2" noClientComponent="true" title="新建2" uiprops.cellClassName="ui-head-btn"/>
    <ToolbarBtn name="ft_set_btn3" noClientComponent="true" title="新建3" uiprops.cellClassName="ui-head-btn"/>
</ToolbarBtnGroup>
```

#### 弹窗界面

```xml
<?xml version="1.0" encoding="UTF-8"?>
<B title="${RES.$.title?弹窗}" fullPage="true" xmlns="http://www.snsoft.com.cn/schema/UI" xmlns:m="http://www.snsoft.com.cn/schema/UI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.snsoft.com.cn/schema/UI http://www.snsoft.com.cn/schema/UI.xsd">
    <m:DialogPane name="query" title="查询面板" layoutm="grid" cellcols="4" uiprops.subCompOpts="2" uiprops.className="basic-query-con"
 clayoutwids="${RES.FT-CODE.E.Q.4CW}" region="north" xprops.btnsLayoutMode="2" xprops.showRefreshPane="true"
showToolbar="false" uiprops.backInitValues="true">



        <c name="ccodetrust" title="${RES.FT-TRD.ccodetrust?委托方}" sqltype="12" disableed="true" tipIfOverflow="true"/>

    </m:DialogPane>

    <m:GridTable name="table_name" title="表" sqlexpr="table_name" mainui="query"
 region="center" uiprops.markSeledRow="1" rdonly="true" noCollapseQPaneAfterRefresh="true" disableSave="true"
 uiprops.closeTabOnDelete="false" dsprops.pageRows="10" dsprops.lastSort="['purordgicode desc']"
xprops.LoadDataService="FT.Service#queryUI">
        

   
        <c name="purordicode" sqltype="12" title="采购合同内码" width="${RES.FT-TRD.E.G.CW}" hidden="true"/>
        <c name="purordcode" title="采购合同号" sqltype="12" width="${RES.FT-CODE.E.G.CW.outcode}" asceOrd="true" bottomval="recind"/>
        
        <c name="cnamedesc" title="商品名称" sqltype="12" width="${RES.FT-TRD.E.G.CW.gnamedesc}" mutipleLine="true" aidInputerBtn="true" aidInputableIfRdonly="true" tipIfOverflow="true"/>
        
    </m:GridTable>
</B>
```

#### 弹窗 JS 代码

```java
public void oncmd_newSheetUp () {
    // 设置界面初始值
    // 例如 <GridTable name="tables"><c name="field"/></GridTable>
    // 要为 field 设置默认值，则应该是：$o("tables", $o("field", 1))
    JSObject initVals = $o("要设置默认值列所在组件的名称", $o("默认值列名称", "要设置的默认值"));
    //根据界面号，获取指定界面。即界面 xml 文件所在目录路径（从 res 路径之后开始）
    // 若不需要初始值，则最后一个参数为：$o 即可
    DialogPane dialog = UIUtil.loadDialog("HBNZ-TRD.Inv.PurInv.ArrCanConDialog", 0, null, null, null, initvals);
    dialog.title = "弹出框标题";
    dialog.setSize(1100, 550);
    // 为按钮绑定事件
    FuncCall<Object> onOkCall = bindAsFunctionCall((js.IFunction_V3<DialogPane, Button,String>) this::arrcanconOk);
    dialog.addListener("onOk", onOkCall);
    // 显示弹窗
    dialog.showModal();
}

// 确定按钮事件
public void arrcanconOk(DialogPane dialog, Button btn, String command) {
    if ("ok".equals(command))
    {
        window.alert("点击了确定按钮");
        dialog.getItemByName("文本框/表等元素的 name 属性").getValue();
    }
}



// ================= 上述代码适合填写表单的弹窗，下面代码是选择数据的弹窗
JSObject config = $o("muiid", "HBNZ-TRD.Inv.PurInv.ArrCanConDialog", "tblname", "ft_trd_podg_tsg_cor_view",
"multiRow", true, "selectMid", false, "fixSize", "1100*550");
final SelectTableDataDialog dlgPane = new SelectTableDataDialog(config);
dlgPane.setTitle("选择到票核销合同");
// 设置初始化参数
dlgPane.setInitParamValue("column", value);
dlgPane.addListener("onOk", this.bindAsFunctionCall((js.IFunction_V1<SelectTableDataDialog>) this::arrcanconOk));
dlgPane.showModal();

public void arrcanconOk(SelectTableDataDialog dlgPane) {
    Table dlgTable = dlgPane.getTable();
    DataSet dlgDataSet = dlgTable.dataSet;
    int[] rows = dlgTable.getSelectedRowNumbers();
    if (rows == null || rows.length == 0)
    {
        // 至少选择一条数据
        throw new JSException(ResBundle.getString("ERRFT-TRD", "FT-TRD.E0201110030"));
    }
    // 获取选中数据的指定字段值
    String purordcode = dlgDataSet.getValue("purordcode", rows[0]);
}
```

### 全屏形式

相当于正常打开一个新界面，而不是弹窗形式

![](images/WEBRESOURCE0a029dc942053a9b641f5b84392b339aGIF%202023-8-30%2017-33-16.gif)

```java
String title = null;
String uiid = "FT-SET.Inv.LrpFeePEntry";
UIUtil.wopenUI(title, uiid, $o());
```

## 下拉框手动关闭

下拉框打开后，由于种种原因，无法自动关闭，可在 JS 监听中，手动关闭

```java
AidInputer aidInputer = mainTable.getColumn("invtype").aidInputer;
if (aidInputer instanceof ComboAidInputer)
{
   ((ComboAidInputer)aidInputer).hideAndFocusParent();
}
```

## 动态获取元素并修改属性

元素设置必填之类的，例如：通过 Component 组件的 nonBlank 属性设置必填

设置完之后，一般需要刷新表：table.render(2)

### 监听指定 Table 作用域

JS 监听通常会放在系统功能.xml 文件中，若界面中有多个 GridTable 类标签，JS 监听需要明确应该作用在哪个 GridTable 上

JS 监听的父类：

例如：SalShipPayModeCtrlCliListener 类就会直接作用于 name 属性为 ft_trd_psp_sett 的 GridTable 上

```xml
<Functions> 
  <Functype>SN-PLAT.JS</Functype>  
  <Subtype>2</Subtype>  
  <Funcimpl>#new snsoft.ft.trd.tx.ship.bas.SalShipPayModeCtrlCliListener({"tgtUINames":["ft_trd_psp_sett"],"tblName":"ft_trd_pspg","grpCol":"purordicode","domabr":"20","fcyCol":"fcy",flags:1,"sysId":"FT-TRD.TX.AImpPspCtrlLccodeNotNull"})</Funcimpl>  
  <Remark>JS监听</Remark> 
</Functions> 
```

此时监听类中重写的 initComponent 方法，系统调用该方法时，传递的 table 参数就是 name 为 ft_trd_psp_sett 的 GridTable

```java
@Override
public void initComponent(Table table, JSObject values){
    
}
```

### 获取其他 Table

若想操作其他 table 的列，或者数据，则需要先获取到该 table

```java
public void initComponent(Table table, JSObject values) {
    //获取 JS 监听所在 Table 的根 Table，是整个页面的根
    Table rootTable = table.getRootTable();
    //通过根 Table 获取界面中任意 Table，只需要输入对应 Table 标签的 name 属性即可
    Table neetTable = TableUtils.getTable(rootTable, "ft_trd_psp_busifysys_bas2");
}
```

### 操作元素

拿到 Table 对象后，可以获取要操作的对象，例如列。并对其进行修改

```java
//根据列标签 name 属性，获取 table 中的指定列
TableColumn bmableflds = neetTable.getColumn("bmableflds");
//隐藏该列
bmableflds.setVisible(false);

//上述操作，也可使用封装的工具类实现
//具体参数可参考“常用类_方法_标签”笔记->常用方法->监听层
TableUtils.setColumnsOptionsOfSingleTable(1, neetTable, "bmableflds", UIOptions.o1Invisible, true);
```

如果弹窗界面是 recordTable、GroupPane 等，则 c 标签获取到的对象是 InputField，想要设置必录，则可运行以下代码：

```java
Component component = dialog.getItemByName("inscode");
component.nonBlank = true;
component.updateLabelClass();
```

> JS 监听获取按钮（未实践）
> MenuPaneNode menuNode = table.getMenuNode("attrName");
> MenuNode node = menuNode.node;
> node.setVisible(false)


### 设置行背景色

```java
public void onTableCellRender(Table table, TableEvent e) {
    e.renderInfo.background = "#D3D3D3";
    e.renderInfo.background = "yellow";
}
```

## 监听设置显隐

当界面属性控制的控制显隐无法满足需求时，例如点击按钮时显示某个标签页。可以通过 JS 监听来实现

**注意：**

此时可通过

```java
// 获取 groupPane 组件
GroupPane pane = (GroupPane) table.getItemByName("组件名称");
// 更新组件内的子元素
pane.relayoutItems()
```

## 远程调用

使 JS 监听访问服务端方法

详情请查看官方帮助文档：[远程调用](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-HELP/Xjs/Rinvoke/Rinvoke.md#远程调用)

框架提供了三种远程调用方式：

- 调用 UI 监听中的方法

- 调用远程服务接口

- 调用远程静态方法

### 调用远程服务接口

即 JS 监听调用 UI 服务层的 service 方法

1. UI 服务声明远程调用方法

1. 在 UI 服务 service 接口中，使用 @Remoteable 注解声明该方法能够被远程调用

1. 创建实现类，并增加注解，声明类的 bean name @Service("FT-SET.RecClaimUIService")

```java
//UI 服务/snsoft.start.school_manager/code/service/ClassInfoUIService
public interface ClassInfoUIService {
    @Remoteable
    void test(String nn);
}
```

1. 创建 JS 业务层接口

![](images/WEBRESOURCE26e009f2717d18f63bea9080a4f04503截图.png)

1. 创建 service 接口文件：xjs 项目/snsoft/项目名/模块名/service/*Service.java

1. 文件路径为 JS 监听类同级目录下的 service 文件夹内，如右图

1. 接口顶部声明注解，定义将该接口编译到哪个 js 文件中，要和使用该接口的 js 监听类放在一个 js 文件中

1. 接口上使用类注解@js.JSCode(remoteBean="UI 服务的 service 实现类的 beanName")

1. 定义一个方法，返回值、方法名、参数，与要远程调用的方法保持一致

```java
/*#
    lib=snsoft/start/schoolManager.js
#*/
package snsoft.start.school_manager.service;

/**
 * @Description: TODO
 * @Author: 马寅广
 * @CreateTime: 2022-12-08  15:34
 */
@js.JSCode(remoteBean = "START_SCHOOL_MANAGER.ClassInfoUIService")
public interface RemoteService {
    void test(String nn);
}
```

1. JS 监听类中，远程调用方法

```java
//获取 xjs 声明的 service 接口
RemoteService remoteService = RInvoke.newBean(RemoteService.class);
//调用接口方法
remoteService.test("nnnn");
```

> **注：RInvoke.newBean 只能在构造函数中执行（根据实际操作得知，正确与否待定）**


### 调用 UI 监听中的方法

1. 在 UI 组件中，创建 UI 监听，路径为：UI 组件/src/main/java/snsoft/**/ui

1. 必须有 UIEvent 参数

1. 使用 @Remoteable 注解声明为远程方法

1. 可以使用 @AuthParam 声明权限

```java
@Component("HBNZ-TRD.HbnzDocumentReSeUIListener")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class HbnzDocumentReSeUIListener extends SystemFunctionListener {
    public HbnzDocumentReSeUIListener(Map<String,Object> parameter){
        super(parameter);
    }
    
    @Remoteable
    @AuthParam(sheetCode = HbnzDocumentReSe.SHEET_CODE, opids = { "AR" })
    // UIEvent 参数必须
    public void addReadPerson(UIEvent event, String doreseicode, String[] readpersonArray){}
}
```

1. 界面或系统功能中引用该监听

```xml
<Functions>
    <Functype>SN-PLAT.UI</Functype>
    <Subtype>1</Subtype>
    <Funcimpl>
        <![CDATA[
            HBNZ-TRD.HbnzDocumentReSeUIListener?[{tgtUINames:["nz_trd_documentrese"]}]
        ]]>
    </Funcimpl>
    <Remark>指定阅读人员</Remark>
</Functions>
```

1. js 中调用

```java
// 参数必须对应，否则会报找不到远程方法
table.uiInvoke("远程方法名", 参数);
// 刷新表，可确保远程方法改动数据后，界面能够显示最新数据
table.refreshTable();
```

## SV 监听

同样在系统功能中新建文件，命名“单据号.SV.xml”，例如：FT-TRD.TX.Ord.SePODomFPurOrdHBNZ.SV.xml

### 监听配置：生效后可改

在 API 模块，对应单据的 service 接口中，使用类注解

```java
<?xml version="1.0" encoding="UTF-8"?>
<SystemFunctions xmlns="http://www.snsoft.com.cn/schema/plat-sysfunc" xsi:schemaLocation="http://www.snsoft.com.cn/schema/plat-sysfunc http://www.snsoft.com.cn/schema/plat-sysfunc.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Name>XXX存盘功能</Name>
    <Remark>
        <![CDATA[
        ]]>
    </Remark>

    <Functions>
        <Functype>SN-PLAT.SV</Functype>
        <Subtype>1</Subtype>
        <Funcimpl>
            <![CDATA[
                SN-PLAT.PlatRVSVListener
            ]]>
        </Funcimpl>
        <Remark>单据存盘值替换SV监听</Remark>
    </Functions>
    
    <Functions>
        <Functype>SN-PLAT.SV</Functype>
        <Subtype>1</Subtype>
        <Funcimpl>
            <![CDATA[
                SN-PLAT.SaveCheckSVListener?[{incasinfo:'tbl1:col1/tbl2:col2,+,-/tbl3:col3,+'}]
            ]]>
        </Funcimpl>
        <Remark>
            存盘检查，指定表列在生效状态可修改
            1、指定表列可修改：tblname:col1,col2...
            2、指定表列可修改及增删：tblname:col1,+,-（即【+：可以增加】【-：可以删除】）
            3、指定多表列可以修改：tblname1:col1,col2/tblname2:col3,col4
            4、指定表的所有列可修改：tblname:*
        </Remark>
    </Functions>
</SystemFunctions>
```

### 存盘监听

监听单据存盘后的操作

```java
@Component("SN-PLAT.CopyXcodeListener")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CopyXcodeListener<V extends VO> implements FunctionSaveListener<V> {
    /**
     * 记录存盘前，事务内。
     */
    public void beforeSave(FunctionSaveShareEvent<V> se, SaveParams<V> params, SaveResults results, V record){
    }
}
```

### 状态监听

监听单据生效后进行的操作

```java
@Component("FT-TRD.TX.XXXListener")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class XXXListener extends AbstractStatusListener<XXXVO> {
    /**

	 * 生效后：从小于70的状态 修改为 70的状态


	 */
    @Override
    protected void afterRatify(FunctionSaveShareEvent<XXXVO> se, SaveParams<XXXVO> params, XXXVO vo) {
            
    }
}
```

SV 配置文件引入监听

```xml
<Functions>
    <Functype>SN-PLAT.SV</Functype>
    <Subtype>1</Subtype>
    <Funcimpl>
        <![CDATA[
            HBNZ-TRD.TX.PurOrdStatusListener
        ]]>
    </Funcimpl>
    <Remark>采购合同生效自动生成付款申请单</Remark>
</Functions>
```

# 码表_字典

码表，对应数据库中的一张表，提供编码与中文含义的转换

例如：数据库表中，性别字段一般不会直接存储“男、女”，而是“1、2”，此时需要有一个表格记录值与含义的对应关系，并且在前端展示时，需要将数字转成对应的中文含义

## 码表定义

即指定哪个表是码表，[官方码表帮助文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/Codetbl/Codetbl.md#码表CodeData)

> 可以单独生成一个表作为码表，也可以指定另一个数据表作为码表
> 例如：班级表中，班级编号与班级名即可作为码表，供学生表录入班级信息时使用


创建：UI 工程/resources/cfg/codedata/CodeData项目名.xml

以下示例，对应数据库中的 school_status_code 表，具体创建表步骤，参考[南北软件项目开发](note://WEBeee6fa74264ce7ba5dd9ad697120cf4a)“数据库定义并创建表”一节

```xml
<codedata-list xmlns="http://www.snsoft.com.cn/schema/CodeData"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:schemaLocation="http://www.snsoft.com.cn/schema/CodeData http://www.snsoft.com.cn/schema/CodeData.xsd">
    <!-- id：唯一标识该码表；table：数据库表名；title；对应码表定义界面的说明列；expl：对应码表定义界面的备注列 -->
    <!-- ui.options4="0x020" 表示立即刷新，所有码表建议都配置。确保在码表弹窗打开时自动查询数据 -->
    <codedata id="statusCode" table="school_status_code" title="状态码表" expl="学校管理系统状态码表" ui.options4="0x020">
        <!-- name：字段名；type：列类型，与 c 标签的 sqltype 一致；title：列标题；width：列宽；primkey：主键 -->
        <column name="statuscode" type="12" title="状态编码" width="200" primkey="true" />
        <column name="statusname" type="12" title="状态名称" width="300" />
    </codedata>
</codedata-list>
```

## 前端展示

在前端 xml 指定列标签中，通过属性 codedata 进行指定码表

格式：#项目名.码表 id

```xml
<c name="status" title="启用状态" sqltype="12" width="60" codedata="#START.statusCode"/>
```

## 字典

和码表类似，是一种更倾向于“少数据、少变化”的简单码表

例如，二十四小时的中文名映射，1 对应“一”，这个不会变，前端也需要转义显示，就用到了字典

通过 JSON 文件配置，还可以指定不同语言，用来支持国际化

### 创建字典文件

创建：

使用规范：

- 字典号（dicticode）要求：一般使用对应小写的字段名

- 字典说明（remark）要求：必须详细的说明，包括：含义、使用范围、注意事项等

```json
[
    {
        "dicticode": "unit",//字典编码
        "name": "字典名称",
        "remark": "字典详细说明",
        "isCuicode":"true",//加上该属性后，表示该字段为商户字典，可在二开项目允许用户编辑字段码表等自定义操作
        "infos": [
            {
                "code": "003",
                "name": "辆",
                "sortidx": 10 // 供前端界面展示排序
            },
            {
                "code": "007",
                "name": "个"
            }        
        ]
    },
    {
        "dicticode": "zerostock",
        "name": "库存方式",
        "remark": "",
        "infos": [
            {
                "code": "10",
                "name": "库存方式"
            },
            {
                "code": "20",
                "name": "直运方式"
            }
        ]
    
    }
]
```

### 使用字典

在界面 XML 文件配置中，使用 c 标签中的 codedata 属性可以关联字典和码表

关联字典格式：#DT_项目名.字典号

例如上述代码放在 UI 工程/resources/cfg/dict/DictInfoSTART.json

想要调用其中的 unit 字典，则可以这么写：

```xml
<c name="unit" title="单位" sqltype="12" width="120" codedata="#DT_START.unit" showcode="true" showname="true" disableed="true" aidInputerBtn="true"/>
```

> 其中 showcode 和 showname 分别代表是否显示码值及码名
> disableed 表示是否要禁止编辑，一般码表和字典字段需要启用该属性，确保只能输入字典定义的值
> 可通过 c 标签的 aiprops="cellTextFmt:['${code}/${name}']" 属性，定义字典下拉列表的展示方式


### 字典动态过滤

详情见：[字典动态过滤](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/Dict/Dictdef.md#字典过滤)

1. 界面定义，声明动态过滤参数

1. ${}，代表引用的参数变量，实际的值通过监听类传过来

```xml
<c name="course" title="学科"
   sqltype="12"
   width="120"
   codedata="#DT_START.subjects"
   cmparams.SQLFILTER="code in (${subjectsParam})"
   noblank="true"
   aidInputerBtn="true"
   droplist="true"/>
```

1. 监听类定义方法，当点击辅助输入按钮时触发

```java
/**
 * @description: 点击辅助输入下拉按钮时触发；动态过滤成绩子表学科列字典（码表实时更新问题）
 **/
@Override
public void itemAidInputing(Table table, TableEvent e) {
    super.itemAidInputing(table, e);

    //1、判断是否是成绩子表的学科列
    if ($eq(table.name, GRADE_SUB_GRIDTABLE_NAME) && $eq(e.forTblColumn.name, GRADE_SUB_COURSE_FIELD)) {
        //2、获取教师信息码表数据
        CodeData codeData = AidInfo.createAidInfo(new AidInfoService.AidInfoParam("#START.teacherCode", null)).toCodeData();
        Object[][] teacherCodeArrayData = codeData.getData();
        window.console.log("获取到的教师信息码表=================================");
        window.console.log(teacherCodeArrayData);

        //3、获取教师表中已有学科信息，拼接成过滤参数
        String subjectsParam = "";
        for (int i = 0; i < teacherCodeArrayData.length; i++) {
            subjectsParam += "'" + teacherCodeArrayData[i][2] + "',";
        }
        subjectsParam = subjectsParam.substring(0, subjectsParam.length() - 1);

        window.console.log("拼接好的过滤参数：" + subjectsParam);

        //4、获取学科列字典信息，传递过滤参数
        /** 学科字典 */
        CodeData courseCodeData = (CodeData)e.item.selectOptions;
        courseCodeData.loadParameter.$set("subjectsParam", subjectsParam);
    }
}
```

### 字典级联显示

字典显示的数据可能受其他字典或码表或字段的影响

例如，选择省字典后，市字典就要动态更新

此时，在字典定义中，需要定义额外字段：

若出库方式为“20”，则会过滤掉 var01 不包含 20 的数据

```xml
<c name="scodetype" title="出库方式" codedata="#DT_HBNZ-TRD.Scodetype"/>
<c name="shiptype" title="发货类型" codedata="#DT_HBNZ-TRD.Shiptype"
cmprops.pmFromPane="{var01:'scodetype'}" cmparams.JSONFILTER="{n:'var01',v:'%${var01}%',op:'like'}"/>
```

```json
{
    "dicticode": "Shiptype",
    "name": "发货类型",
    "remark": "发货类型",
    "var01": "扩展字段",
    "infos": [
      {
        "code": "10",
        "name": "自存",
        "var01": "10"
      },
      {
        "code": "20",
        "name": "代存",
        "var01": "10,20"
      },
      {
        "code": "30",
        "name": "销售",
        "var01": "10,20"
      }
    ]
  }
```

## 自定义码表弹窗

若码表不止：码和值两个字段，有多个字段的情况下，该如何赋值？

例如：成绩表需要选择学生信息，除了学生编号外，还要学生姓名、性别、班级等字段信息

![](images/WEBRESOURCE81005cd85f283ddc760a333bf3eb1cec截图.png)

1. 定义码表

```xml
<!-- 学生信息码表 -->
<codedata id="studentCode" table="student_info" title="学生信息码表" expl="马寅广：学生信息码表">
    <column name="stuicode" title="学生编号" type="12" primkey="true"/>
    <column name="stuname" title="姓名" type="12"/>
    <column name="sex" title="性别" type="12"/>
    <column name="classicode" title="班级" type="12"/>
</codedata>
```

1. 界面定义

```xml
<c name="stuicode" title="学生编号" aidInputerBtn="true" codedata="#START.studentCode" aiprops.copyMap="{'stuname':'stuname','sex':'sex'}"/>
<c name="stuname" title="学生姓名"/>
<c name="sex" title="学生性别" codedata="#DT_START.gender" showcode="true" showname="true" aidInputerBtn="true"/>
```

不过上述实现有缺陷，即弹窗中的“性别”列，显示的码值。

如果要正常显示码名，则需要自定义码表弹窗

### 定义码表弹窗

![](images/WEBRESOURCE9d6a47ab9eb66b5add8e0c9b8d3c598e截图.png)

与表数据展示界面类似，相当于一个子界面。与码表文件没有关联，只是其目的与码表一样：为用户提供固定选项，不能自由输入

1. 定义界面

1. 编辑需要展示的列

1. 与定义普通界面无异，和其他界面存放目录一致：UI 工程 resources/ui/res/项目名/StudentCodeData.xml

1. options0="32"：B 标签的属性，指定该界面以弹窗形式出现

```xml
<m:B xmlns="http://www.snsoft.com.cn/schema/UI" xmlns:m="http://www.snsoft.com.cn/schema/UI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.snsoft.com.cn/schema/UI http://www.snsoft.com.cn/schema/UI.xsd" title="弹出界面码表" options0="32">
    <m:DialogPane name="query" title="学生查询" cellcols="2" layoutm="grid"  region="north">
        <c name="stunum" title="学号" sqltype="12" width="120" prefixMatch="true" suffixMatch="true"/>
        <c name="stuname" title="姓名" sqltype="12" width="120" prefixMatch="true" suffixMatch="true"/>
    </m:DialogPane>
    <m:GridTable title="学生列表" mainui="query" name="studentInfoList" sqlexpr="student_info" height="200" rdonly="true" region="center" noCollapseQPaneAfterRefresh="true" xprops.LoadDataService="START_SCHOOL_MANAGER.StudentInfoUIService#queryUI">
        <c name="stuicode" title="学生编号" sqltype="12" width="120"/>
        <c name="stunum" title="学号" sqltype="12" width="100" align="center" noblank="true"/>
        <c name="stuname" title="姓名" sqltype="12" width="80" noblank="true" align="center"/>
        <c name="sex" title="性别" sqltype="12" width="100" codedata="#DT_START.gender" showcode="true" showname="true" disableed="true" aidInputerBtn="true"/>
        <c name="classicode" title="班级" sqltype="12" width="160" disableed="true" codedata="#START.classCode" showname="true" showcode="true" droplist="true" aidInputerBtn="true"/>
    </m:GridTable>
</m:B>
```

1. 界面列定义

1. 在需要调出该码表的列标签上，编辑属性以及需要复制的值

1. **aidinputer 属性**：指定底层类，用来初始化码表弹窗界面

1. **aiprops 属性**：指定调用底层弹窗初始化类时传递的参数，对应 java 类 xjs.ui.util.SelectTableDataDialog

1. **pctsize**：窗口大小，百分比；同类属性有：fixSize，即为固定大小

1. **muiid**：指定弹窗界面的 id，等同于定义菜单文件时的 cmd，即界面存放的路径，从 ui/res 之后截取

1. 例如该弹窗界面的存放路径：UI 工程/resources/cfg/ui/res/START/Code

1. **tblname**：指定弹窗界面中 GridTable 标签的 name 属性

1. **column、rtnNameFld**：弹窗界面中指定列的 name 属性

1. 两个属性缺一不可（不一定，可以只有 column），均为列 name 属性，暂时不知道两者区别

1. 代表  aiprops 属性所在列要取的值，下列代码中，stuicode 列会复制弹窗界面中 stuicode 列的内容

1. **copyMap**：指定其他需要复制值的列

1. 格式：{‘当前表列 name 属性’,'要复制的列 name 属性'}

1. **dlgParam**：弹窗的查询参数赋值，将当前表的指定字段值传递给弹窗的查询面板参数中

1. 格式：{'弹窗查询面板参数列 name 属性', '当前表要传递数值列 name 属性'}

1. **refreshOnOpen**：是否立即刷新

1. 格式：true 或 false

1. dlgParam 只是将参数赋值给查询面板，并不会自动查询。配合该参数，可实现参数传递过来后，自动查询

```xml
<c name="stuicode" title="学生编号" sqltype="12" width="120" aidInputerBtn="true" aidinputer="Xjs.ui.SelectTableDataDialog.new" disableed="true" aiprops="pctSize:'50*80', muiid:'START.Code.StudentCodeData', copyMap:{'stuname':'stuname', 'sex':'sex'}, tblname:'studentInfoList', column:'stuicode', rtnNameFld:'stuicode'"/>
<c name="stuname" title="学生姓名" sqltype="12" width="120"/>
<c name="sex" title="学生性别" sqltype="12" width="120" codedata="#DT_START.gender" showcode="true" showname="true" aidInputerBtn="true"/>
```

## 码表数据更新（缓存处理）

当某个表更新数据后，对应的码表会因为缓存原因，导致数据无法及时更新

具体可查看[官方缓存帮助文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/Cache/Cache.md#缓存)

### 建表 xml 配置缓存（不建议使用）

修改建表的 xml 文件中

- 指定 table 标签的 id 属性 < 20000，或者增加localcache="1"

- cachekeym 属性指定缓存 key

- cachekcols 属性指定某一列为宏，以便 cachekeym 调用

```xml
<!-- CreateDatabaseSTART.xml -->
<!-- 教师表 -->
<table id="33003" name="teacher_info" title="教师表" datasrcid="START_RW" localcache="1" cachekeym="ALL-${teaicode}" cachekcols="teaicode">
    <column name="teaicode" title="教师编号" type="VARCHAR(ICODE)" primkey="true"/>
    <column name="teaname" title="姓名" type="VARCHAR(PEOPLENAME)"/>
    <column name="sex" title="性别" type="VARCHAR(4)"/>
</table>
```

### CacheConfig 文件配置

一个系统只需配置一个即可

创建文件：

| 标签 | 描述 | 
| -- | -- |
| CacheConfigs | 根标签 | 
| 🦾CacheConfig | 对应一个配置，每个表可对应多个 CacheConfig | 
| 🦾🦾Ones | 表示一个规则对应一个缓存 key | 
| 🦾🦾🦾RuleKey | 代表一个缓存更新规则 | 
| 🦾🦾Many | 表示一个规则对应多个缓存 key | 
| 🦾🦾🦾Rules | rule：与 RuleKey 标签的 rule 属性一致 | 
| 🦾🦾🦾keys | key：与 RuleKey 标签的 key 属性一致 | 


```xml
<?xml version="1.0" encoding="UTF-8"?>
<CacheConfigs xmlns="http://www.snsoft.com.cn/schema/CacheConfigs" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.snsoft.com.cn/schema/CacheConfigs.xsd">
   <!-- tblname 表名 -->
   <CacheConfig tblname="users">
      <!-- Ones 表示一个规则对应一个 key，可以配置多个规则 -->
      <Ones>
         <RuleKey rule="{ops:'+-',ups:'username,limbcode'}" key="SQL_ArrayCodeValueMap.[users]|*from users*${cuicode}*" remark="用户码表" />
         <RuleKey rule="{ops:'+-',ups:'wcode'}" key="SQL-WU.[users]|${cuicode}" remark="人员用户关系" />
         <RuleKey rule="{flds:{txtid:'limgrpdef_%'}}" key="SQL_ArrayCodeValueMap.[limgrpdef]|*${cuicode}*" remark="单据数据权限分类" />
         <RuleKey rule="[{ops:'+-'},{tbl:'ui_ext',ops:'+-',ups:'uiname,uiprops'}]" key="SQL-UI.[mui]|${muiid}|*" remark="界面定义" />
      </Ones>
   </CacheConfig>
   <CacheConfig tblname="wcode">
      <!-- Many 表示一个规则对应多个 key -->
      <Many>
         <Rules rule="{ops:'+-',ups:'wname,wname1,wname2,limbcode,bedate,ledate,status'}" />
         <Keys key="SQL_ArrayCodeValueMap.[wcode]|*from wcode*${cuicode}*" remark="人员码表" />
         <Keys key="SQL-BW.[bwcode][wcode]|${cuicode}" remark="部门人员缓存" />
      </Many>
   </CacheConfig>
</CacheConfigs>
```

### LastModifiedSTART.xml

同目录创建 LastModifiedSTART.xml，可用来指定某个表变化时，更新另外一个表的缓存

```xml
<?xml version="1.0" encoding="UTF-8"?>
<ens>
    <en trgtbl="teacher_info"/>
    <!-- 当 table_1 发生变化时，更新 table_2 的缓存 -->
    <en trgtbl="table_1" mdftbls="table_2"/>
</ens>
```

> 虽然设置了码表数据更新，但是当 使用码表的界面 和 码表编辑界面 同时打开
> 此时在 码表编辑界面 修改码表数据，在使用码表的界面无法看到修改后的数据，必须刷新界面才可以
> 可以通过 SetAidClearBufferedCliListener 监听类，实现每次点击下拉框按钮，都会获取最新的码表数据


## CodeData 码表对象

在监听类或者后台获取码表信息时，用 CodeData 对象进行封装

| 获取 CodeData 对象 | 获取任意码表 | 
| -- | -- |
| 方法 | 说明 | 
| void  | 清除缓存（指界面缓存） | 
| Object[][]  | 获取码表数据 | 
| String  | 根据码获取名 | 
| T loadParameter.$set(String, T) | 传递过滤参数值 | 


## JS 调用显示码表弹窗

```java
// 重点：获取 AidInputer 对象，调用其 doAidInput 方法
// 尽量通过界面列去获取码表，而不是通过 AidInfo 去获取。因为码表初始化时，会读取各种参数，这些参数都在 c 标签的属性上进行定义
// 初始化参数的方法：snsoft/sheet/busi/SheetCodeDataJSListener#buildParam

// 点击按钮所在的表，该表中需要有对应码表的列
Table table = (Table) dialog.getItemByName("hbnz_trd_ord_copy");
// 选中该码表列
table.setSelectedColumn(table.indexOfColumn("salordcode"), true);
// 获取码表列
TableColumn column = table.getColumn("salordcode");
//根据码表列，获取码表弹窗
SelectTableDataDialog selectDialog = (SelectTableDataDialog) column.aidInputer;
//设置码表选项
selectDialog.selOptions = selectDialog.selOptions | 4;
//设置公司等过滤条件
selectDialog.setInitParamValue("limitmode", "20");
// 设置码表标题
selectDialog.title = "拷贝集团销售合同|选择销售合同";
// 设置确定按钮方法
IFunction_V1<SelectTableDataDialog> func = this::selectSalOk;
selectDialog.$set("updateParentValue", func.createDelegate(this, new Object[] { selectDialog }, true));
// 显示码表。其中第一个参数指定码表弹窗的上一级组件 Component
selectDialog.doAidInput(table.getItemByName("salordcode"), null, null);

// 如果启动报错，可以尝试以下启动
table.startAidInput(column)
```

还有一种方式，直接通过码表 ID 获取码表

```java
// 获取人员码表
AidInfoService.AidInfoParam param = new AidInfoService.AidInfoParam("#FT-CODE.BWcode", null);
// 添加参数
JSObject<Object> cmparams = $o("sheetcode", "HBNZ-TRD.OT.Exap.DocumentReSe", "opids", "C", "opts", 2);
param.setCmparams(cmparams);
AidInfo aidInfo = AidInfo.createAidInfo(param);
SelectCodeDialog dlg = (SelectCodeDialog) aidInfo.aidInputer;
dlg.defaultCodeData = aidInfo.toCodeData();
dlg.addListener("onAidInputerUpdateParentValue", (js.IFunction_V1<SelectCodeDialog>) this::addReadPerson, this);
dlg.selOptions = 0x0000_0004 | 0x0000_4000;
MenuNode node = table.getMenuNode("addRedPerson").node;
if ($bool(node) && $bool(node.attachedDom))
{
    dlg.setTitle(node.attachedDom.innerText);
}
dlg.doAidInput(table, null, null);
```

## 动态过滤码表

码表的复杂过滤，可以通过 c 标签的 cmparams.FILTER 属性指定 UI 工程下的过滤类

1. 在 UI 工程下，创建过滤器类，继承 DefaultCodeDataRowFilter

1. 码表界面加载时，就会触发该方法，断点可用

```java
/**
 * <p>标题：工资类别码表过滤器</p>

 */
public class SngSalaryTypeCodeDataFilter extends DefaultCodeDataRowFilter
 {
    /** 个性化参数
 */
    private SngSalaryTypeParamV fpv;
    
    // 构造方法必须有
    /** 初始化参数 */
    public SngSalaryTypeCodeDataFilter(Map<String,Object> parameter)
{
        fpv = ObjectUtils.toCompatibleObject(parameter, SngSalaryTypeParamV.class, 8);
    }

    @Override
    public void init(CodeData codeData)
{

}

    
    @Override
    public SqlExpr toSqlExpr(CodeDataDef codeData)
{}
    
    // 返回 true，表示显示该条数据
    @Override
    public boolean filterAccept(Object[] a, CodeData codeData, int row)
{}


	
}
```

1. 界面中引用

```xml
<!-- 注意，码表的弹窗必须是原始弹窗，如果是自定义界面，则可能无法调用该过滤方法 -->
<!-- 例如：#FT-CODE.CcodeLMExt，采用自定义弹窗界面 -->
<c codedata="#FT-SNG.SngSalaryType" cmparams.FILTER="snsoft.ft.sna.sng.codedata.SngSalaryTypeCodeDataFilter.new?mode=1">
```

> 该方法同样使用字典过滤


### 监听过滤

如果弹窗界面是自定义的，无法使用 c 标签属性去引用过滤器，则可以使用监听进行处理

1. 增加 itemAidInputing 监听，辅助输入打开时触发

```java
@Override
	public void itemAidInputing(Table table, TableEvent e)
{
    super.itemAidInputing(table, e);
    
    // 指定列的辅助录入才触发该方法
    if ($eq("purccode", e.item.name))
{
        // 获取列上配置的码表
        SelectTableDataDialog codedata = (SelectTableDataDialog) table.getColumn("purccode").aidInputer;
        // 设置初始参数，对应自定义弹窗界面的查询参数面板里定义的参数。用逗号分割多个参数，会自动使用 in 来过滤
        codedata.setInitParamValue("ccodes", "123,2345");
    }
	}
```

> 思路：
> 在码表加载出来之后，想办法通过 JS 对界面的过滤输入框进行赋值
> 一般弹窗类（dialog）都会有赋值的方法
> 上述代码，最终是调用：table.queryParam.setItemValue(String, value)，即向码表弹窗对应的 table 对象的查询面板参数赋值
> 如果遇到获取 table 为空，可能是因为码表从未打开，而未初始化
> 可以先 doAidInput 显示码表，紧接着再 hideAidInputer 隐藏码表，即可


# 前端界面

## 查询参数面板

通过标签 

### 基础查询面板

![](images/WEBRESOURCE88f5b1df1c27e7d39c2c7c62ee3d3ca5截图.png)

- cellcols：布局列数，一行展示多少个输入框

- showToolbar：是否展示工具栏，即黄色框部分

- uiprops.backInitValues：重置按钮

- xprops.btnsLayoutMode：按钮布局模式

- 1：按钮占一行，放在第一行输入框后面

- 2：按钮占两行，放在第一行输入框后面

- 3：按钮占一行，单独放在最后一行（截图中应用的该布局）

```xml
<m:DialogPane name="param" title="班级查询参数" region="north" cellcols="5" layoutm="grid" showToolbar="false" uiprops.backInitValues="true" xprops.btnsLayoutMode="3">
    <c name="classname" title="班级名称" sqltype="12" width="120"/>
    <c name="remark" title="备注" sqltype="12" width="120"/>
    <c name="status" title="启用状态" sqltype="12" width="60" codedata="#90.status" cmparams.sheetcode="START.ClassInfo" showname="true" hidden="true"/>
</m:DialogPane>
```

### 输入框占位符

输入框不会显示标题，而是在输入框内以占位符的形式展示：

![](images/WEBRESOURCE8aca8afd98a9854e1755f8133b78190a截图.png)

```xml
<c name="concode" titleHidden="true" uiprops.bgLabel="${RES.$.concode?内部交易合同号/合同审批单号}" toUpper="true" prefixMatch="true" suffixMatch="true" sqltype="12" width="215"/>
```

### 复选框查询参数

不显示标题名，仅显示码表值，码表使用枚举，定义 10 的中文名

勾选后，就是“10”，不勾选，就是 null

```xml
<c name="exeablenum" title="${RES.$.nz_trd_grpintqry_view.exeablenum?可执行数>0}" sqlexpr="null" button="true" mutiple="true" codedata="10:可执行数>0" showname="true" titleHidden="true" initval="10" sqltype="12"/>
```

### 码表参数特殊展示

码表作为查询参数时，可以采用下拉框形式，也可以将所有选项进行平铺

![](images/WEBRESOURCE98a7ed313ef724b7db060841404dcdc0截图.png)

![](images/WEBRESOURCE8b1b7152d08f4f5c7515335bb8154c65截图.png)

1. 增加 JS 监听：#new snsoft.plat.bas.busi.ShowRefreshPanelListener({})

1. 具体参数参考

1. 用来指定要平铺的码表字段

1. DialogPane 标签增加 xprops.showRefreshPane="true" 属性

### 更多查询参数

![](images/WEBRESOURCEa4594027ae9265a74bb51042e8a8cdc3截图.png)

当参数过多时，可以分成两个面板进行展示，通过按钮在两个面板间切换

- xprops.showFoldPaneBtn="true" 属性，展示收起查询面板按钮

- uiprops.fixhByContent="1" 属性，将面板高度固定在最小高度，防止两个面板切换时高度变化

- xprops.showAdvancedQueryBtn 属性，显示更多参数按钮，用来切换查询面板

1. dialogpane 标签增加以上属性

1. xprops.showAdvancedQueryBtn 必须加上

1. 标签体由原来的多个 c 标签，改为两个 P 标签

1. name 属性随意，uiprops.className 属性固定

1. basic-query-con：主面板

1. advanced-query-con：副面板

1. layoutm=“grid” 属性必须加上，否则会不显示输入框前的标题

![](images/WEBRESOURCEebd06cc54e441af9174850eb9314e284截图.png)

```xml
<m:DialogPane name="param" title="班级查询参数" region="north" cellcols="5" layoutm="grid" uiprops.backInitValues="true" xprops.btnsLayoutMode="3" xprops.showRefreshPane="true" xprops.showFoldPaneBtn="true" xprops.showAdvancedQueryBtn="true" uiprops.fixhByContent="1">
    <P name="p1" layoutm="grid" cellcols="4" uiprops.className="basic-query-con" clayoutwids="150，150，150，150" uiprops.subCompOpts="2">
        <c name="classname" title="班级名称" sqltype="12" width="120"/>
        <c name="remark" title="备注" sqltype="12" width="120"/>
    </P>
    <P name="p2" uiprops.className="advanced-query-con" layoutm="grid" cellcols="4" clayoutwids="150，150，150，150" uiprops.subCompOpts="2">
        <c name="vprepare" title="创建人" sqltype="12" width="70"/>
        <c name="modifier" title="修改人" sqltype="12" width="70"/>
    </P>
</m:DialogPane>
```

### 高级查询参数

![](images/WEBRESOURCE5d5a023f705ad0c408686c95df16c205截图.png)

可以自定义参数字段以及匹配规则

一般是在副面板展示高级查询参数，代码示例中仅展示副面板内容，主面板参考上面的代码

1. 副面板外套一个 P 标签，将原来的 P 标签包裹，并新增一个 **QParamsEditor1** 标签

1. uiprops.cellColCount 属性：一行展示几个高级查询参数（右图中算一个）

1. uiprops.fixVisFldCount 属性：一共展示几个高级查询参数

1. uiprops.cellWidths 属性：输入框宽度，必输。若没有该属性，将展示不全（一个高级查询参数有三个框宽度需要设置）

1. uiprops.hideDelIcon 属性：隐藏删除图标。在前边展示一个红叉，可以将输入的内容清空（bug：再次点击将删除第三个输入框）

1. uiprops.layoutInCopyFm 属性：指定要复制行布局的标签 name

1. 增加 invoke 子标签，可以将其他面板的查询参数复制到下拉列表中，而不需手动编写

1. method 属性：指定添加参数的方法（snsoft.plat.bas.tools.ui.XMLUICompLoaderTools.addQParamEditorExComps）

1. cpfrom 属性：从哪个面板进行复制。多个 name 属性以逗号分割

1. exclude 属性：需要排除掉的字段，多个属性以逗号分割

1. 除了直接复制其他面板的参数，也可以添加 c 标签增加独有参数

```xml
<P name="p2" uiprops.className="advanced-query-con">
    <P name="more0" layoutm="grid" cellcols="4" clayoutwids="${RES.$.E.Q.4CW}" uiprops.subCompOpts="2">
        <c name="vprepare" title="创建人" sqltype="12" width="70"/>
        <c name="predatefm" title="创建时间从"
 sqltype="91"
 width="100"
 disableed="true"
 aidInputerBtn="true"/>
        <c name="predateto" title="到"
 sqltype="91"
 width="100"
 disableed="true"
aidInputerBtn="true"/>

    </P>
    <QParamsEditor1 name="more1" title="高级查询"
 titleHidden="true"
 uiprops.cellColCount="3"
 uiprops.fixVisFldCount="4"
 uiprops.cellWidths="80,60,105"
uiprops.subCompOpts="2"
uiprops.hideDelIcon="false" uiprops.layoutInCopyFm="more0">
        <invoke method="snsoft.plat.bas.tools.ui.XMLUICompLoaderTools.addQParamEditorExComps"
 cpfrom="p1,more0"
exclude="predatefm,predateto,modifydatefm,modifydateto"/>
        <c name="department" title="系别12"
 sqltype="12"
 width="120"
 codedata="#DT_START.department"
 disableed="true"
 showname="true"/>
    </QParamsEditor1>
</P>
```

## 工具栏按钮

### 按钮与标题并排

![](images/WEBRESOURCE9938eb750c5a9ae054741744403faad6截图.png)

![](images/WEBRESOURCE43956c306f151061463714a3e84ed40e截图.png)

```xml
<!-- 1、Toolbar 标签放在 GroupPane 标签内，即与标题在同一区域 -->
<!-- 2、增加属性：tags="tbtn"，使工具栏与标题在同一行 -->
<!-- 3-1、Toolbar 标签不添加 uiprops.cellClassName="toolbar-panel" 样式，实现文本样式按钮，而非有方框的按钮 -->
<!-- 3-2、Toolbar 标签不需要添加 region 属性 -->
<!-- 4-1、ToolbarBtnGroup 标签需要添加样式属性：uiprops.className="ui-btn-group inner"，否则下拉三角会有错位问题 -->
<!-- 4-2、ToolbarBtnGroup 标签还需要添加以下样式：xprops.dftBtnCls="ui-btn" xprops.dftBtnIconCls="icons-btn-documents"（图标） -->

<m:GroupPane name="backplan_bj" title="还本计划" region="north">
    <m:Toolbar name="toolbar" tags="tbtn">
        <ToolbarBtnGroup name="new" noClientComponent="true" title="${RES.$.title.F.btn.genbackbjplan?生成还本计划1}" uiprops.className="ui-btn-group inner" xprops.dftBtnCls="ui-btn" xprops.dftBtnIconCls="icons-btn-documents">
            <ToolbarBtn name="ft_set_finap_bj_btn_genBackBjPlanByMonth" title="${RES.$.title.F.btn.genbackplanmonth?按月1}" noClientComponent="true"/>
            <ToolbarBtn name="ft_set_finap_bj_btn_genBackBjPlanByQuarter" title="${RES.$.title.F.btn.genbackplanquarter?按季度1}" noClientComponent="true"/>
            <ToolbarBtn name="ft_set_finap_bj_btn_genBackBjPlanByHalfYear" title="${RES.$.title.F.btn.genbackplanhalfyear?按半年1}" noClientComponent="true"/>
            <ToolbarBtn name="ft_set_finap_bj_btn_genBackBjPlanByYear" title="${RES.$.title.F.btn.genbackplanyear?按年1}" noClientComponent="true"/>
        </ToolbarBtnGroup>
        <ToolbarBtn name="ft_set_finap_bj_btn_modifyBackBjPlan" title="${RES.$.title.F.btn.modifybackbjplan?修改还本计划1}" noClientComponent="true" xprops.iconClassName="icons-btn-edit"/>
    </m:Toolbar>
</m:GroupPane>
```

### **下拉列表**

例如新建按钮，可以新建多种类型文档，将多个类型的新建按钮，合并到一个按钮上

ToolbarbtnGroup 标签的 name 属性随意填写，仅作为包裹具体按钮的分组组件

![](images/WEBRESOURCEa3ec6e8678561cf4c56ba4799f917ccd截图.png)

```xml
<m:Toolbar id="classEditToolbar"title="班级操作工具栏" name="classEditToolbar" uiprops.cellClassName="toolbar-panel">
    <ToolbarBtnGroup name="new" noClientComponent="true" title="新建">
        <ToolbarBtn name="sng_wagedata_btn_newSheetUp" title="新建1" noClientComponent="true"/>
        <ToolbarBtn name="sng_wagedata_btn_newSheetCur" title="新建2" noClientComponent="true"/>
        <ToolbarBtn name="sng_wagedata_btn_newSheetTac" title="新建3" noClientComponent="true"/>
    </ToolbarBtnGroup>
    <ToolbarBtn name="classInfoList_btn_refresh" title="刷新" noClientComponent="true" xprops.iconClassName="icons-btn-refresh"/>
    <ToolbarBtn name="classInfoList_btn_save" title="${RES.START.btn_save?保存}" noClientComponent="true" xprops.iconClassName="icons-btn-save"/>
</m:Toolbar>
```

## 原生 HTML 代码

在前端界面中，也可以写原生 HTML 代码

举栗子：ft-settle-ui\********.RELEASE\ft-settle-ui-********.RELEASE.jar!\cfg\ui\res\FT-SET\Inv\

```xml
<attachhtmlbuilder>
    <![CDATA[
       <span id="static_text_id" style="float:right;padding: 0 12px">Total：CNY    0.00</span>
    ]]>
</attachhtmlbuilder>
```

## 分析模板

可以根据分析模板，设置数据的展示方式，具体[查看官网](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/UI/UserTblInfo.md#%E5%88%86%E6%9E%90%E6%A8%A1%E6%9D%BF)

![](images/WEBRESOURCEfd7f7c2751582a35f28bd0dbbc4f80bf截图.png)

![](images/WEBRESOURCE8e7f20329752aee01de6cedc773b9347截图.png)

例如右图，就是按照数据中的“公司名称”列进行分组展示，并设置数量、金额字段进行求和处理。显示效果为：分组行会显示分组列的去重结果、求和列的合计值，展开之后会显示具体的每条数据

1. 导入 js 监听

```xml
<jslistener>
    <![CDATA[
        #new snsoft.ft.code.comm.BusiSelectGroupJSListener({})
    ]]>
</jslistener>
```

1. 界面增加按钮，并在 GridTable 中绑定该按钮（也有其他实现方式，目的就是为了增加一个按钮）

```xml
<m:Toolbar name="btnGroup" uiprops.cellClassName="toolbar-panel" region="north"/>

<!-- groupData：开启分组查询；uiprops.attachTbName：绑定工具栏的按钮 -->
<m:GridTable groupData="true" uiprops.attachTbName="btnGroup">
```

1. 至此，按钮会显示出来，并有两个二级菜单：自定义分组、明细数据

![](images/WEBRESOURCE641d63080e6491182a52b9e1321debb6截图.png)

1. 自定义分组：可视化方式编辑各个字段的分组条件、隐藏等各种选项。列名可拖动。常见的选项如右图：

1. 明细数据：原始显示，无任何分组等特殊显示

1. 设置完之后，可以存储方案

1. 也可以设置默认查询方案，在 UI 工程/resources/cfg/ui/grppm 目录下创建“界面定义号.xml”文件

1. 该文件与界面绑定，且会指向 GridTable 的 name 属性

1. 页面会自动加载，并在“分析模板”按钮下可选择创建的方案

1. 核心内容是：ConfigInfo 标签，定义了该方案的各个字段的展示规则。该规则可通过以下两种方式：

1. 通过第三步可视化操作，点击存储方案，会向 usertblinfo 表中插入一条数据，通过 infoid 字段查询自己定义的方案名称，会得到一个 Json 字符串

1. 自己编写

```xml
<?xml version="1.0" encoding="UTF-8"?>
<GroupParamDef xmlns="http://www.snsoft.com.cn/schema/ui-pm" xsi:schemaLocation="http://www.snsoft.com.cn/schema/ui-pm http://www.snsoft.com.cn/schema/ui-pm.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <UIParams uiname="nz_trd_salesstatistics_view" infoid="按公司分级显示">
        <ConfigInfo>
            <![CDATA[
               {
                "columns": [
                  {
                     "name": "saldate",
                     "title": "销售日期",
                     "sqlType": 0,
                     "groupType": 0,
                     "groupFlags": 1,
                     "groupLevl": 1,
                     "crossTotalCaption": null,
                     "crSumMode": 0,
                     "bcodeType": null
                  },
                    {
                        "name": "salobj",
                        "title": "销售对象",
                        "sqlType": 0,
                        "groupType": 2,
                        "groupFlags": 0,
                        "groupLevl": 0,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    },
                    {
                        "name": "corpbcode",
                        "title": "公司名称",
                        "sqlType": 0,
                        "groupType": 0,
                        "groupFlags": 68,
                        "groupLevl": 1,
                        "width": 180,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    },
                    {
                        "name": "salccode",
                        "title": "销售客户",
                        "sqlType": 0,
                        "groupType": 2,
                        "groupFlags": 0,
                        "groupLevl": 0,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    },
                    {
                        "name": "proinout",
                        "title": "省内省外",
                        "sqlType": 0,
                        "groupType": 4,
                        "groupFlags": 0,
                        "groupLevl": 0,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    },
                    {
                        "name": "gcode",
                        "title": "商品",
                        "sqlType": 0,
                        "groupType": 4,
                        "groupFlags": 0,
                        "groupLevl": 0,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    },
                    {
                        "name": "salnum",
                        "title": "销售数量",
                        "sqlType": 0,
                        "groupType": 4,
                        "groupFlags": 0,
                        "groupLevl": 0,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    },
                    {
                        "name": "salmoney",
                        "title": "销售金额",
                        "sqlType": 0,
                        "groupType": 4,
                        "groupFlags": 0,
                        "groupLevl": 0,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    },
                    {
                        "name": "salinvnum",
                        "title": "销售开票数量",
                        "sqlType": 0,
                        "groupType": 4,
                        "groupFlags": 0,
                        "groupLevl": 0,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    },
                    {
                        "name": "salinvmoney",
                        "title": "销售开票金额",
                        "sqlType": 0,
                        "groupType": 3,
                        "groupFlags": 0,
                        "groupLevl": 0,
                        "crossTotalCaption": null,
                        "crSumMode": 0,
                        "bcodeType": null
                    }
                ],
                "options": 4100,
                "expandToLevl": 0
              }
           ]]>
       </ConfigInfo>
   </UIParams>
</GroupParamDef>
```

## 数据展示

### 树节点

![](images/WEBRESOURCE4df4e93b078825763948f698ccdd6547截图.png)

具体效果可参考“公司”界面，[官方文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/UI/UI.md#%E6%A0%91%E8%8A%82%E7%82%B9%E9%85%8D%E7%BD%AE)

1. 在 GridTable 标签内增加属性

<attr name="IsTreeChildRow" type="102" value="snsoft.cli.dx.impl.CodeIsTreeChildRow.new('sortpath')"/>

<attr name="isTreeChildOf" type="202" value="new Xjs.dx.IsChildCodeOf({codeColumn:'sortpath',delim:'.'})"/>

1. IsTreeChildRow：指向路径节点

1. isTreeChildOf：指向路径节点，指定路径分隔符

1. 例如公司的路径字段：bpath，存储的值为：0001.0002，那么分隔符就是 .

1. 路径字段作为“树节点列”，必须配置升序“asceOrd = ”true“”，其余公司名称作为展示的树节点列，要指定属性“treenode = ”true“”

> 公司的数据，“南北集团”也是一条数据，只是其余字段为空，只有 treenode 列有值
> 如果所有数据的路径字段，都是：0001.*，即父节点是 0001，但是没有该条数据，则可能出现树展示问题


### 交叉列

参见文档：[官方文档：数据交叉](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-HELP/Xjs/Bottomval.md#%E6%95%B0%E6%8D%AE%E4%BA%A4%E5%8F%89)

# 询问框

在用户进行某项操作时，需要弹出确认框，再次确定要执行的操作

![](images/WEBRESOURCE08cf5a19c7c03223ff2d27dca0ce6931截图.png)

| 方法 | 作用 | 
| -- | -- |
| new  | 回调方法 | 
| Function | 创建函数 | 
| static void UIUtil. | 创建弹出框 | 


```java
//定义回调方法
FuncCall call = new FuncCall(this.createDelegate("deleteDetail", new Object[] {param}, true), this);
// 或者如下
FuncCall funcCall = this.bindAsFunctionCall((IFunction_V2<DialogPane, String>) this::disableDef);
UIUtil.showConfirmDialog("提示","本操作将删除开票明细数据，是否继续？", call, null);
      
void deleteDetail(DialogPane dialog, String command , String newVal)
	{
		if ("ok".equals(command))
		{
			phyDataSet.deleteAllRows();
			mainDataSet.postRow();


		}
		mainDataSet.saveChanges();
	}
```

# 特殊列设置

## 计算列

通过标签属性 evalexpr，可以指定计算表达式，以达到控制某一列显示内容的目的

> *注：当表达式中引用的列名发生改变时就会触发
> 若计算列不生效，可能需要将计算列属性放在主表上（即详情页中大部分字段都需要隐藏的那个表），而不是共享表。


| 表达式类型 | 表达式 | 说明 | 
| -- | -- | -- |
| 算术运算 | $列名 * $列名 | 多个列的值进行运算 | 
| 字符串 | $列名 + ‘-’ + $列名 | 多个列的值进行拼接 | 
| 三元运算 | $列名 == '001' ? 'true' : $列名  | 表达式及结果均可使用 $列名 | 
| 复杂运算 | function(参数) { | 参数的个数、顺序，与最后的 $列名保持一致 | 


### 原理

所有表达式，即 evalexpr 属性的值，最终都会转化成 JS 的一个匿名函数

其中只有

转化的匿名函数被包含在 anonymous 函数中，当做返回值进行了返回。返回内容即为要展示的内容

anonymous 函数有两个参数：

- ds：dataset，代表一行数据，可以通过其获取码表值等

- ra：一行数据的 JSON 字符串

> 既然只有$列名被转换，那么表达式可以随意使用任何 JS 函数
> 例如：$desc.substring(0,$desc.indexOf('|'))
> 就代表对 desc 列的值进行截取子串操作，将 desc 列 '|' 之前的内容展示出来
> “中国|美国”，就会截取成“中国”


```html
<c evalexpr="表达式"/>
<!-- ============================转化结果============================ -->
function anonymous(ds,ra) {
    return 表达式
}



<!-- ============================例如：============================ -->
<c name="teaname" title="姓名" evalexpr="alert('123')"/>
<!-- 转化结果 -->
function anonymous(ds,ra) {
    return alert('123')
}


<c name="teaname" title="姓名" evalexpr="function(teaname, ds, ra){ console.log(teaname); }($teaname, ds, ra)"/>
<!-- 转化结果 -->
function anonymous(ds, ra) {
    return function(teaname, ds, ra){
        console.log(teaname);    
    }(this.getValue(ds,ra,'teaname'), ds, ra)
}
```

> 通过 dataset 对象获取某一列的码表值：
> dataset.getColumn('列名').valueMap //{002: "女"}
> 
> 但是该方法**可能**无法实时更新码表，原因如下：
> valueMap 属性不会一开始就加载所有码表值，例如数据的值是“女”，那么 valueMap 中就不会有“男”
> 当动态修改列值，将性别改为“男”时，会先触发 anonymous 方法，而此时 valueMap 属性还是只有“女”，所以会出现“undefined”


### 动态获取码表值并显示

上节最后说，通过 dataset 获取码表值会无法实时更新码表，导致新增行或者选择界面中未出现的码表值时，就会获取到 undefined

此时可以通过 Table 对象获取

不过需要注意：

下列是“姓名-性别”列的 evalexpr 属性，其中定义了以下逻辑：

1. 首先判断性别列是否有值，若无值，则直接显示姓名

1. 通过 dataset 获取 Table 对象，即界面定义 XML 中所有 Table（包括 GridTable、RecordTable）

1. 假设定了了多个 Table，则需要在获取时指定你要操作的 Table

1. 例子中，定义了两个 Table

1. GridTable：教师表数据的展示

1. RecordTable：教师表姓名、性别字段的编辑弹窗，也就是共享表弹窗

1. 通过 Table 对象获取 sex 列的码值

1. 根据冒号分割，根据分割的数组长度能够判断出是否为首次获取

1. 若是首次获取，则取分割的数组的第二个元素

1. 反之，则 codeValue 直接就是码值

```javascript
function(ds,teaname,sex){
    if(typeof(sex) == 'undefined' || sex == null) return teaname;
    /* 获取码表值，通过 DataColumn 对象的 valueMap 属性获取码表会有 undefined 的情况 */
    let codeValue = ds.getTables()[0].getCodeName('sex', sex);
    /* 首次获取的内容为：码值:码名，需要提取出码名 */
    let codeValueArray = codeValue.split(':');
    if(codeValueArray.length > 1) codeValue = codeValueArray[1];
    /* 拼接字段：姓名-性别 */
    return teaname + '-' + codeValue;
}(ds,$teaname,$sex)
```

### 处理界面数据

```javascript
function(ds,presalpri,salqtc){
    const result = presalpri?(presalpri - ds.master.getValue('purupric')) * salqtc:null;
    const total = ds.rows.reduce((amt, row) => amt += row[ds.getColumn('pregromar').indexDataStoreColumn], 0);
    ds.master.setValue('pregromartotal', total + result);
    return result;
}(ds, $presalpri, $salqtc)
```

## 虚拟列

若需要在界面展示的时候，增加一列，用来展示拼接字段等特定内容

其中操作列、计算列可以通过属性进行实现，但如果只是单纯的想要增加一列，展示自定义内容，需要进行以下设置，否则默认的查询会将该列的 name 属性拼接到 select 语句中，然后报字段未定义错误

- name 属性必须以 var 开头，或者直接就是 var

- sqlexpr 属性不能为空，可以指定为空串

```xml
<c name="var" title="姓名-性别" sqlexpr="''"/>
```

## 服务端补数列

若某一列的值需要服务端补数，即值是通过服务端方法生成的

例如在成绩表中需要展示对应的及格科目数，需要联查成绩子表才能计算出来

1. 实体类中增加补数字段，但不用 @Column 注解修饰

```java
public class GradeMaster extends BasVO {
    //……
    /**系别*/
    @Column
    private String department;
    /** 及格科目数 */
    private Integer passnum;
    //passnum 的 get/set 方法
}
```

1. 在查询成绩主表数据的方法中，计算需要补数的值，并赋值到实体类该属性中

```java
public class GradeMasterServiceImpl{
    @Override
    public QueryResults<GradeMaster> query(GradeMasterParams params) {
        //查询数据库，获取成绩主表数据
        QueryResults<GradeMaster> gradeMasterQueryResults = super.uiQuery(params);
        //从封装结果集中取出数据
        List<GradeMaster> data = gradeMasterQueryResults.getData();
        //为每个数据的补数列赋值
        for (GradeMaster datum : data) {
            datum.setPassnum(20);
        }
        //返回数据至前端
        return gradeMasterQueryResults;
    }
}
```

1. 前端界面定义 XML 中，增加补数列。与空白列不同，sqlexpr 需要设置为 null

```xml
<c name="passnum" sqlexpr="null" title="及格科目数" sqltype="4" align="center" width="90"/>
```

## 操作列

![](images/WEBRESOURCE4b124e12068bd82ed273eab7211f9af0截图.png)

一般情况下，不允许用户直接在表格上进行数据编辑。系统提供一列按钮，用来操作对应数据，即操作列

具体可参考[官方文档_界面操作列](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=help/SN-HELP/Demo/UI/ModuleDemo.md#界面操作列)

其中删除、编辑等方法提供底层默认实现，只需要在界面中添加对应的编辑弹窗并引用即可

1. 界面 xml 中增加操作列

1. name 必须为 operate，除非通过 JS 监听参数进行修改

```xml
<c name="operate" title="操作" sqlexpr="null" sqltype="12" width="60" align="center" staticText="true" disableEnterEd="true"/>
<!-- 或者以下 -->
<Operate name="operate" title="${RES.$.operate?操作}" sqlexpr="null" sqltype="12" width="120" align="center" staticText="true" disableEnterEd="true" fixedLeft="true"/>
```

1. 增加 JS 监听

1. cmds：代表要显示的按钮

1.  "add", "delete", "edit", "copy", "search", "upload", "download", "switchUp", "switchDown"

1. popedUiname：设置编辑按钮要弹出的窗口

1. 复制按钮需要重写 OperateJSListener 类的 copyOperate 方法

```xml
<jslistener>
    <![CDATA[
        #new snsoft.sheet.comm.OperateJSListener({tgtUINames:["nz_trd_salshipg"],cmds:["delete","edit"],popedUiname:'editRecordTable'})
    ]]>
</jslistener>

<!-- 或者这个 -->
<Functions>
    <Functype>SN-PLAT.JS</Functype>
    <Subtype>2</Subtype>
    <Funcimpl>
        <![CDATA[
            #new snsoft.sheet.comm.OperateJSListener({tgtUINames:["nz_fa_pcassetcardg"],'cmds':['delete']})
        ]]>
    </Funcimpl>
    <Remark>
        <![CDATA[
            界面操作列监听JS
        ]]>
    </Remark>
</Functions>
```

```java
String notcopyColumns = "rptype,status,vprepare,predate,modifier,modifydate";
@Override
protected void copyOperate(Table table, TableEvent e)
{
    if (table.getDataSet().isNewRow() && !table.getDataSet().isChanged(false))
    {
       return;
    }
    table.dataSet.postRow();
    int row = table.dataSet.getRow();
    table.dataSet.insertRow(3);
    DataColumn[] columns = table.dataSet.getColumns();
    for (int i = 0; i < columns.length; i++)
    {
       DataColumn column = columns[i];
       if (!JSString.isStrIn(notcopyColumns, column.name))
       {
          table.dataSet.setValue(column.name, table.dataSet.getValue(column.name, row));
       }
    }
}
```

1. 增加编辑弹窗和新增弹窗

1. mainui 指向对应的 GridTable

1. 即使不需要新增弹窗，也尽量两个都创建

1. 因为若只有一个，可能出现当弹窗关闭，会删除当前行的情况

```xml
<!-- 编辑弹窗 -->
<m:RecordTable name="editRecordTable" region="null" title="${RES.$.retable_edit}" mainui="classInfoList" cellcols="2" layoutm="grid" dialogMode="true" layoutOnClient="true" skipRdOnlyCellOnEnterKey="true">
</m:RecordTable>

<!-- 新增弹窗 -->
<m:RecordTable name="addRecordTable" region="null" title="${RES.$.retable_edit}" mainui="classInfoList" cellcols="2" layoutm="grid" dialogMode="true" layoutOnClient="true" skipRdOnlyCellOnEnterKey="true" disableDelete="true" uiprops="disablePgNav:1">
</m:RecordTable>
```

> 弹窗中的状态列，不支持彩色状态转换属性，即：
> uiprops.renderer="new snsoft.plat.bas.comm.render.StatusCellRender({})"


# 共享表弹窗

可以通过弹窗，展示数据详情或者编辑数据

可以参考[官方帮助文档：DOM 组件](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-HELP/Demo/UI/ModuleDemo.md#共享表)

例如，姓名和性别合并成一列后，可以通过点击合并列，弹出窗口，对姓名和性别进行修改

![](images/WEBRESOURCE0e20ed2351564a16faead4df22f37530截图.png)

1. 在界面定义 XML 中，声明共享窗口

1. mainui：要共享数据的表 name，即 GridTable 的 name 属性

1. region：null 值

1. title：弹窗标题

1. 子标签里，直接复制 GridTable 中声明的列即可

1. GridTable，也就是共享数据的表，增加属性

1. uiprops.editUiname="RecordTable 的 name 属性"

1. uiprops.layerPopupEdit="true"，弹窗或者浮层，默认是 false（弹窗）

1. 以上两个属性，会增加操作列，点击操作列的编辑按钮，即可触发弹窗

```xml
<!-- 姓名、性别列编辑弹窗 -->
<m:RecordTable cellcols="2" layoutm="grid" mainui="teacherInfoList" name="teacherInfoListEditor" region="null" title="编辑姓名-性别" dialogMode="true">
    <c name="teaname" title="姓名" sqltype="12" width="120"/>
    <c name="sex" title="性别" sqltype="12" width="120" codedata="#DT_START.gender" showcode="true" showname="true" aidInputerBtn="true"/>
</m:RecordTable>
```

## 自定义弹窗触发方式

若不想通过操作列去触发共享表弹窗，或者不显示操作列，可以自定义触发方式，只需在对应的事件监听方法中，增加打开弹窗的方法即可

![](images/WEBRESOURCE6c9170ae0512fc44e6112d0e31c06755截图.png)

- GridTable 对象的方法

- public void onClickEdit(int oldRow,int r,String popedUiname)

- **oldRow**：上次点击的行

- **r**：当前行

- **popedUiname**：界面定义 XML 中，RecordTable 标签的 name 属性

- 前两个参数，用来判断是否为同一行多次调用

- 当共享表以浮层形式出现，两次点击同一行，会关闭浮层

- Table 对象的方法

- public void openPopupEdit(String editUiname)

- **editUiname**：界面定义 XML 中，RecordTable 标签的 name 属性

例如：重写表格列点击的监听方法，当点击“姓名-性别”列时，弹出编辑窗口

```java
@Override
public void onTableCellClick(Table table, TableEvent e) {
    //1、获取被选中的列
    TableColumn selectedColumn = table.getSelectedColumn();

    //2、判断是否是“姓名-性别”列
    if(selectedColumn != null && $eq(selectedColumn.name, "name-gender")){
        //或者执行这个：table.openPopupEdit("teacherInfoListEditor");
        ((GridTable)table).onClickEdit(table.dataSet.getRow(), e.row, "teacherInfoListEditor");
    }
}
```

> 当点击列，弹出共享表弹窗，关闭弹窗后，再次点击该列，就不会触发 onTableCellClick 事件
> 可以在该列上，增加属性：disableEnterEd
> 与 disableed 不同，它可以让该列多次触发 onTableCellClick


# 主子表数据联动

选中主表数据，子表根据外键，展示对应的数据

例如成绩主子表，选中成绩主表中某个学生的成绩总览数据，就会展示子表中该学生各科成绩详情数据

1. 在主表同界面中，定义另一个展示 Table，目前只测试了建立同级的 GridTable

1. 子表 GridTable 标签

1. mainui 属性指定主表 GridTable 标签的 name 属性，用来绑定查询条件

1. sqlexpr 属性指定子表的表名

1. 子表外键列 c 标签

1. xprops.cpmastercol 属性：拷贝主表字段

1. 字段名：在新建行时，将对应主表的列值设置为当前行的列值

1. :字段名：除了上述功能外，在加载表数据时，还会取主表的值对子表进行过滤

![](images/WEBRESOURCE314b897ae8ac1251a6225ca7a430d7f2截图.png)

```xml
<m:GridTable title="成绩主列表" name="gradeMasterInfoList" mainui="param" sqlexpr="grade_master" mutiple="false">
    <c name="scoreicode" title="成绩主表内码" sqltype="12" width="120"/>
</m:GridTable>

<m:GridTable title="成绩子列表" name="gradeSubInfoList" mainui="gradeMasterInfoList" sqlexpr="grade_sub">
    <c name="scoreicode" title="成绩主表内码" sqltype="12" width="120" disableed="true" xprops.cpmastercol=":scoreicode"/>
</m:GridTable>
```

# 默认值

## 介绍

[默认值帮助文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=help/SN-CMC/Service/VO/VOListener.md#默认值)

在实体类中，使用

| @DefaultValue | 
| -- | -- | -- |
| 默认值 | value = “默认值” |   | 
| 分码（自增值） | value = “ |   | 
| 底层宏变量 | %EnvParam(USERCODE) | 当前用户名 |   | 
| CURTIME | 当前时间，带时分秒 |   | 
| CURDATE | 当前时间，年月日 |   | 
| processBean | 用于特殊的默认值处理
 |   | 
| fix | 固定值，插入时设置值，禁止修改 |   | 


> Tips
> 默认值也用于为单据内码、外码赋值
> 具体赋值规则，在【单据->单据编码】一节描述


## 注意

- 必须调用自定义新增数据方法

- 前端必须在 GridTable 标签中，通过 xprops.SaveDataService 指定自己的新增数据方法

- 若使用工具条中默认的保存方法，则不会起作用

- 需要在存盘后刷新界面，否则自增值/默认值无法在界面显示，会影响下一次新增数据

## 业务层获取默认值

```java
/** 默认值设置服务 */
DefaultValueService defaultValueService = SpringBeanUtils.getBeanByName(DefaultValueService.BeanName);
//获取默认值，第一个参数为要获取默认值的实体类 class，第二个参数是具体要查询默认值的字段名
Object object = defaultValueService.getDefaultValue(GradeMaster.class, "scoreicode");
//设置所有字段默认值
defaultValueService.setDefaultValues(objectEntry, true);
```

## 序号默认值

通过

在通过服务端新增数据时，可能无法继续编号，即会从 10 开始设置序号

此时需要获取已有数据的序号最大值，然后依次设置新增数据的序号

```java
/** 已有子表数据 */
List<VO> details;
/** 已知最大序号 */
int maxIdx = details.stream().mapToInt(detail -> VOUtils.getFieldValue(detail, "idx")).max().orElse(0);
AtomicInteger maxIdxA = new AtomicInteger(maxIdx);
// 新增子表数据
for (String newDeatil : newDetails)
{
    maxIdx.set(maxIdx.get() + 10);
    detail.setIdx(maxIdx.get());
}
```

# 界面属性控制

当界面中某一列设置为某值时，编辑按钮禁用。当某一列为空时，后一列禁止编辑……

这些功能，均可以用界面属性控制来实现，详细帮助文档参见：[界面属性控制官方文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/OptCtrl/OptCtrl.md#界面属性控制)

## 快速配置及细节

1. 编写控制文档，存放目录与界面 xml 文件相似

1. 例如界面 xml 文件存放在ui 工程/resources/cfg/ui/res/系统号/***.xml

1. 则控制文档的存放目录就是：ui工程/resources/cfg/ui/optctrl/系统号/***.xml

1. 除 optctrl 与 res 不同外，其余均相同，包括文件名称

1. 即，为 xxx 界面编写的控制文档，名称要和该界面文档名称保持一致

```xml
<?xml version="1.0" encoding="UTF-8"?>
<UIOptCtrl xmlns="http://www.snsoft.com.cn/schema/OptsCtrl" xsi:schemaLocation="http://www.snsoft.com.cn/schema/OptsCtrl http://www.snsoft.com.cn/schema/OptsCtrl.xsd"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <!-- 代表一个控制逻辑，可以有多个 -->
    <UIOpts>
        <!-- 要控制的表名，GridTable 标签的 name 属性 -->
        <uiname>gradeSubInfoList</uiname>
        <!-- 要控制的 tab 页，使用 tab 页内部的 table 的 name 属性。需要搭配 uiname 标签一起配置 -->
        <tabpane>ft_pay_appdoc</tabpane>
        <!-- 要控制的列名 -->
        <colname>score</colname>
        <!-- 还可以控制按钮，标签体填写 attr 的 name 属性，多个逗号分割 -->
        <!-- 也可以控制 GroupPane，标签为：<border> -->
        <button>statusStart</button>
        <!-- 设置背景色 -->
        <ctrlFields>cb=#ffae41</ctrlFields>
        <!-- 满足 UIVals 条件后，true 代表执行 ctrlFields 属性 -->
        <setValue>true</setValue>
        <!-- 触发条件：grp 默认为0，分组。同组与，异组或 -->
        <UIVals grp="1">
            <!-- 表名，可以不写，默认为当前表（一般是第七行指定的表） -->
            <uiname>gradeSubInfoList</uiname>
            <!-- 判断条件列 -->
            <colname>course</colname>
            <!-- 列的值，可以不写，代表列为空值。多个值时，用逗号分割，匹配关系用“0” -->
            <matchValue>004</matchValue>
            <!-- 列的当前值与 matchValue 的逻辑关系。0 代表等于，即当列值等于 004 时触发 -->
            <matchMode>0</matchMode>
            <!-- 取反 -->
            <negmatch>1</negmatch>
        </UIVals>
        
        <!-- 获取系统选项 -->
        <UIVals grp="2">
            <uiname>ft_set_inv_rc</uiname>
            <!-- 系统选项 key -->
            <colname>FT-SET.Inv.CommRecRealControl</colname>
            <!-- 自定义匹配实现 Bean -->
            <matchValType>snsoft.ft.set.inv.bas.match.CommStartPhyOptionMatchValue</matchValType>
            <!-- 也可以直接写 JS -->
            <matchValType><![CDATA[
                {
                    findMatch:function()
                    {
                        var vprepare = this.table.dataSet.getValue("vprepare");
                        var usercode = window.EnvParameter.USERCODE;
                        console.log(vprepare + "==================" + usercode);
                        return vprepare==usercode;
                    }
                }
            ]]></matchValType>
        </UIVals>
    </UIOpts>
</UIOptCtrl>
```

1. 界面配置 UI 监听

1. 在官方帮助文档中，写了**系统功能**的配置文件，经过测试，该文件可以不写

1. 若写了该文件，可以在原有 UI 监听后，拼接参数?funccode=系统功能配置文件名

```xml
<uilisteners>
    <![CDATA[
        snsoft.ui.optctrl.UIOptCtrlListener.new
    ]]>
</uilisteners>
```

> 若界面中存在多个表，即多个 GridTable 标签，一般只需要在任意一个中配置 UI 监听即可


## 操作列属性控制

若要控制界面中的操作列按钮，需要进行特殊配置

修改控制文档 xml

```xml
<UIOpts>
    <uiname>studentInfoList</uiname>
    <!-- 指定操作列，不能为空 -->
    <colname>operate</colname>
    <!-- 按钮的 id，可以是直接的命令号，也可以是 operate_cmd 指定格式，多个逗号分割 -->
    <operateid>operate_delete,operate_edit</operateid>
    <ctrlFields>disabled</ctrlFields>
    <setValue>true</setValue>
    <UIVals>
        <uiname>studentInfoList</uiname>
        <colname>status</colname>
        <matchValue>60</matchValue>
        <matchMode>6</matchMode>
    </UIVals>
</UIOpts>
```

## 自定义 Bean

若基础界面属性控制无法满足需求，可以自己创建一个类，进行自定义控制

1. 在 XJS 模块中，新建一个类，继承TableOptsCtrlListener.MatchValue

1. 在类文件开头，加上要导出到的 JS文件 路径。注：该 JS 文件必须先在系统功能配置文件或界面中引入（新版本不再需要）

1. 重写 matchColumn / match 方法

1. 在界面属性控制配置文件中，通过<matchValType>标签引入该类的全局路径名即可使用该 Bean

```java
/*#
 lib=snsoft/hbnz/trd/bas/match/CommCheckInnerCcode.js
#*/
package snsoft.hbnz.trd.bas.match;

import js.JSObject;
import xjs.table.sample.TableOptsCtrlListener.MatchValue;
/**
 * <p>标题：检查是否为内部客商</p>
 * <p>
 * 功能：
 * </p>
 * <p>其他说明：</p>
 * <p>作者：马寅广</p>
 * <p>审核：</p>
 * <p>重构：</p>
 * <p>创建日期：2023/8/31 9:43</p>
 */
public class CommCheckInnerCcode extends MatchValue
{
	public CommCheckInnerCcode(JSObject config)
	{
		super(config);
	}

	@Override
	protected boolean matchColumn(String column)
	{
		window.console.log("Nnnsljflsdjflks");
		return true;
	}

 // value 是界面属性控制中指定的 colname 的值，row 是行
 // 父类中的 values[] 是界面属性控制中指定的 matchValue 的值
	@Override
	public boolean match(Object value, int row)
	{
		
		return true;
	}
}
```

# 单据

一张表对应一个单据

详情请查看官方帮助文档：[单据功能](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/Busheet/Busheet.md#单据功能)

## 创建文件

创建单据文件：UI 工程/resources/cfg/res/sheet/系统号.表名.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Sheet sheetflags="4" xmlns="http://www.snsoft.com.cn/schema/Sheet" xsi:schemaLocation="http://www.snsoft.com.cn/schema/Sheet http://www.snsoft.com.cn/schema/Sheet.xsd"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <!-- 单据名称 -->
    <name>班级信息</name>
    <!-- 主表表名 -->
    <tblname>class_info</tblname>
    <!-- 主表内码字段 -->
    <innerfld>classicode</innerfld>
    <!-- 主表外码字段，若没有则和内码保持一致即可 -->
    <outerfld>cuicode</outerfld>
    <!-- 当前单据访问服务 -->
<!--    <acsbean></acsbean>-->
    <!-- 主表对应的 VO 类，必须继承自【snsoft.bas.sheet.busi.entity.BusiVO】 -->
    <voclass>snsoft.start.school_manager.code.vo.ClassInfo</voclass>
    <remark>
        <![CDATA[
            1、负责人：马寅广
        ]]>
    </remark>
</Sheet>
```

## 实体类引入单据

可以继承

```java
@Table(name = "class_info")
@SheetInfo(ClassInfo.SHEETCODE)
public class ClassInfo extends BasVO {
    private static final long serialVersionUID = -6108093887010119472L;
    public static final String SHEETCODE = "START.ClassInfo";
}
```

## 状态

每个单据都有状态，底层提供了统一的单据状态码表（码表 ID：#90.status），可以定义状态 json 文件，用来声明单据所需要的状态

前端使用，可以在使用底层码表的基础上，通过 c 标签的 cmparams 属性，指定单据号，以使用定义的状态 json 文件去过滤底层的状态码表值

也可以直接使用定义的状态 json 文件作为码表

### json 文件

创建文件：UI 工程/resources/cfg/res/status/单据文件名.json

单据文件名是该 json 文件对应的单据文件的名字

```json
[
  {
    "code": "10",
    "name": "草拟"
  },
  {
    "code": "60",
    "name": "休学"
  },
  {
    "code": "70",
    "name": "生效"
  },
  {
    "code": "90",
    "name": "停用"
  }
]
```

> code 需要按照从小到大顺序进行排序，否则界面展示可能不会展示名称


### 引用一：过滤底层状态码表

codedata 引用底层码表，cmparams.sheetcode 指定单据号（单据状态 json 文件名）

```xml
<c name="status" title="启用状态"
   sqltype="12"
   width="60"
   codedata="#90.status"
   cmparams.sheetcode="START.ClassInfo"
   showname="true"
   aidInputerBtn="true"
   droplist="true"
   showcode="true"
   disableed="true"
   initval="10"
   uiprops.renderer="new snsoft.plat.bas.comm.render.StatusCellRender({})"/>
```

### 引用二：使用 json 状态文件做码表

在 codedata 定义 xml 文件中，声明一个新的 codedata

```
<codedata id="stuStatus" title="学生表单据状态" CodeDataLoader="#SN-CORE.StatusCodeDataLoader?[{type:'单据号'}]" ui.options4="0x20" ui.aidinputer="Xjs.ui.ComboAidInputer.new" ui.aiprops="selOptions:1,selOptions2:16,promptInfo:''" expl="正常状态(单据号.json)">
    <column name="code" type="12" title="编码" width="200" primkey="true"/>
    <column name="name" type="12" title="名称" width="300"/>
</codedata>
```

```
<c name="staus" title="启用状态" sqltype="12" width="60" codedata="码表 ID"/>
```

> 该方法不推荐使用，在 codedata90.xml 配置中内置了引入单据状态的码表
> 只需要在前端界面通过 cmparams 设置单据号即可使用自己定义的单据状态
> <c name="status" title="启用状态"
 codedata="#90.status"
 cmparams="sheetcode:'START.StudentInfo'"/>


## 权限

[官方帮助文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/Limit/Datalimit.md#操作权限)

通过声明权限文件、单据与权限关联文件，实现将单据与权限的

即一个单据可以拥有多个权限文件，而一个权限文件也可以绑定多个单据

权限文件主要是定义权限操作，即某个功能一共需要多少种权限，和公司的岗位类似。而岗位具体能做的事，是通过代码来声明

在界面中，可以为岗位、部门、用户分配权限

### 文件配置

1. UI 工程/resources/cfg/res/limit/系统号.***.xml

1. 文件名规则等同于单据命名规则，可以与单据命名一致

1. 用来声明权限操作以及权限字段

1. 权限操作：设定有多少种权限，例如读取权限、写入权限等，权限的作用要根据实际界面属性控制及代码来决定

1. 权限字段：细分控制内容。例如可以控制用户只能查看某个单据中指定公司的数据，而不是所有数据

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!-- limnm：权限名称，非空。${}解析为${RES.[sysid].title_[limid]} -->
<datalimdef limnm="${}" xmlns="http://www.snsoft.com.cn/schema/Datalimdef" xsi:schemaLocation="http://www.snsoft.com.cn/schema/Datalimdef http://www.snsoft.com.cn/schema/Datalimdef.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
   
   <!-- 权限操作 -->
   <!-- opid：编码；opnm：名称 -->
   <datalimop opid="R" opnm="${RES.$.opid_R}" />
   <datalimop opid="C" opnm="${RES.$.opid_C}" />
   <datalimop opid="B" opnm="${RES.$.opid_B}" />
   
   <!-- 权限字段 -->
   <!-- fldname：名；fldtitle：标题；fldtype：类型（4整数，12文本）；fldumatch：匹配类（snsoft.xx.xx.YY.new）或Bean（#BeanName）; -->
   <!-- codedata：码表；options1：辅助录入属性（0x10显示码，0x20显示名，0x40显示全名）；tcprops：属性（例如宽度：width:100） -->
   <!-- btype：组织类型（00运维组织，01部门组织）；fldopts：选项（0x001路径，0x002多选，0x020空值，0x040双向，0x200上级单据过滤忽略） -->
   <datalimflds fldname="astn" fldtitle="${RES.$.astn}" fldtype="12" fldumatch="#SN-PLAT.BWcodeFieldMatcher" codedata="#SN-PLAT.RBWUType" options1="32" tcprops="{editable:false}"/>
   
   <!-- 该字段决定了权限应用到的单据，如果一个单据权限对应多个单据，则需要为 sheetcode 字段加上 defaultValue，值为所有要应用该单据权限的单据号 -->
   <datalimflds fldname="sheetcode" fldtitle="${RES.$.sheetcode}" fldtype="12" tcprops="{nonBlank:true,defaultValue:'FT-SET.Inv.CommFeeRec,FT-SET.Inv.RedCommFeeRec,FT-SET.Inv.IvlCommFeeRec',visible:false}"/>
   <datalimflds fldname="bcode" fldtitle="${RES.$.bcode}" fldtype="12" btype="01" fldopts="1" />
   <datalimflds fldname="wcode" fldtitle="${RES.$.wcode}" fldtype="12"/>
   <datalimflds fldname="corpbcode" fldtitle="${RES.$.corpbcode}" fldtype="12" btype="02" fldopts="1" />
   <datalimflds fldname="vprepare" fldtitle="${RES.$.vprepare}" fldtype="12" />
</datalimdef>
```

1. UI 工程/resources/cfg/res/sheetlim/系统号.json

1. 用来声明单据与权限的关联关系

1. sheetcode：单据号，limid：单据权限号

1. 单据与权限是多对多关系

```json
[
  {
    "sheetcode": "FT-SET.Def.CheckfldDef",
    "limid": "FT-SET.Def.CheckfldDef"
  },
  {
    "sheetcode": "FT-SET.Inv.Pay",
    "limid": "FT-SET.Inv.FeePay"
  },
  {
    "sheetcode": "FT-SET.Inv.Pay",
    "limid": "FT-SET.Inv.CommFeePay"  
  },
  {
    "sheetcode": "FT-SET.Inv.CommFeePay",
    "limid": "FT-SET.Inv.CommFeePay"
  }
]
```

### 界面读取配置并设置权限

00 系统中，通过界面，可以为岗位、部门、用户设置权限

![](images/WEBRESOURCE32bce5475f2e746067b253f51f671fe3截图.png)

界面中，选中左侧的岗位，右侧上半部分选择要设置的单据权限。若列表为空，可单击“选择单据数据权限”，进行选择

右侧下半部分选择要赋予该岗位的权限操作（

![](images/WEBRESOURCE178b08cbaf2efc9818db80dbc56f7dcf截图.png)

> 如果找不到对应单据，需要在“单据数据权限分类”界面进行权限分类绑定


## 撤单、红冲、红冲生成新单据

详情请查看官方帮助文档：[单据红冲](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-PLAT/Sheet/SheetRed.md#单据红冲)

单据一般都会有这三种操作，用来对错误的单据进行修正

- **撤单**：单据已经提交并生效，但是未进行下一步操作。此时可通过撤单将单据状态撤回到“草拟”状态

- **红冲**：单据已经制作凭证或者已经跨账期，但是未录入其他后续操作。此时可通过红冲，系统自动生成一笔生效状态，数量和金额为负数的红冲单据

- **红冲生成新单据**：单据已进行其他后续操作。此时可通过红冲生成新单据，系统自动生成红冲单据，以及“草拟”状态到货单

红冲配置文件在 

![](images/WEBRESOURCE2914ecf985a9dde64809df62cad92a84截图.png)

## 单据校验（单据提交检查）

当单据提交时，需要根据配置来处理一些校验逻辑。也可以处理审批时单据提交的前置逻辑判断，[官方文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-PLAT/Sheet/SheetCheckDef.md#%E5%8D%95%E6%8D%AE%E6%A0%A1%E9%AA%8C)

### 单据方法校验配置

定义公共校验方法，需要实现

```java
@Component(PayAppCreditmodeCheckListener.BeanName)
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
//泛型可决定 check 方法的参数类型，一般是单据实体类
public class PayAppCreditmodeCheckListener implements SheetCheckListener<PayApp>
{
    public static final String BeanName = "FT-SET.PayAppCreditmodeCheckListener";

    public PayAppCreditmodeCheckListener(Map<String,Object> params){
        
    }

   @Override
   public List<SheetCheckInfo> check(PayApp main, Object extParams){
        throw new BaseException("商品拣配数量过多，请检查！");
        // 只提示，用户仍可继续提交
        SheetCheckInfo sheetCheckInfo = new SheetCheckInfo();
        sheetCheckInfo.setChktype(0);
        sheetCheckInfo.setMessage("出现错误，是否继续？");
        return Collections.singletonList(sheetCheckInfo);
   }
}
```

### 单据校验配置

1. 创建单据公共校验配置文件，UI模块/resources/res/sheetcheckref/

1. 指定单据校验方法或TAC代码

```xml
<?xml version="1.0" encoding="utf-8" ?>
<SheetCheckRef xmlns="http://www.snsoft.com.cn/schema/SheetCheckDef" xsi:schemaLocation="http://www.snsoft.com.cn/schema/SheetCheckDef http://www.snsoft.com.cn/schema/SheetCheckDef.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" chkrefname="付款申请检查‘先货后款信用方式’" type="1">
   <TacFormula><![CDATA[
        #FT-SET.PayAppCreditmodeCheckListener?[{}]
    ]]></TacFormula>
   <Remark><![CDATA[
       内贸合同审批单(敞口采购)检查付款金额、借款金额禁止大于合同金额
    ]]></Remark>
</SheetCheckRef>
```

1. 创建单据校验配置文件，UI模块/resources/res/sheetcheck/单据号.xml

1. 指定单据要调用的检查节点

1. TacFormula

```xml
<SheetCheckDef xmlns="http://www.snsoft.com.cn/schema/SheetCheckDef" xsi:schemaLocation="http://www.snsoft.com.cn/schema/SheetCheckDef http://www.snsoft.com.cn/schema/SheetCheckDef.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" chkname="出口付货款申请" chktype="SN-APPR.10" sheetcode="FT-SET.Pay.ExpApp">
   <Nodes dtcode="20" dtname="付款申请检查无单预付的收款单位" chkrefcode="FT-CODE.Credit.OccCheck">
       <Remark>
       <![CDATA[ ]]>
       </Remark>
    </Nodes>
   <Nodes dtcode="10" dtname="付款申请检查停用客商" chkrefcode="单据公共检查编号，指向 sheetcheckref 的文件">
      <TacFormula>
         <![CDATA[
         proc start(main)
             status = main.getStatus()
             if (snsoft.commons.util.StrUtils.obj2int(status) < 30 )
                 return true
             else
                 return false
             end if
         end proc
         ]]>
     </TacFormula>
      <Remark><![CDATA[
        ]]></Remark>
   </Nodes>
</SheetCheckDef>
```

## 单据编码

生成单据内码、外码，具体参考[帮助文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/Accode/Accode.md#%E7%BC%96%E7%A0%81%E8%A7%84%E5%88%99)

1. UI 工程/resources/cfg/res/accode 创建编码规则文件，文件名任意

```xml
<!-- SN-XG.InnerCode.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<Codelike xmlns="http://www.snsoft.com.cn/schema/Accode" xsi:schemaLocation="http://www.snsoft.com.cn/schema/Accode http://www.snsoft.com.cn/schema/Accode.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <name>SN-XG-内码编码规则</name>
    <remark>
        <![CDATA[
            共用编码规则，可以自定义
        ]]>
    </remark>
    <codeinfo>
        <![CDATA[
		proc getCodeLike(refData)
			prefix="ASSE"
			if startCuicode()
				cuicode = refData.get("cuicode")
				if(cuicode == null)
					cuicode = getUserSession(true).getUserCuicode()
				end if
				prefix=cuicode
			end if
			return prefix + inc(12)
		end proc
	]]>
    </codeinfo>
</Codelike>


<!-- SN-XG.Man.Contract.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<Codelike xmlns="http://www.snsoft.com.cn/schema/Accode" xsi:schemaLocation="http://www.snsoft.com.cn/schema/Accode http://www.snsoft.com.cn/schema/Accode.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <name>合同编号编码规则</name>
    <remark>
        <![CDATA[
        ]]>
    </remark>
    <codeinfo>
        <![CDATA[
            proc  getCodeLike(refData)
                return "SOyyMM"+inc(4)
            end proc
        ]]>
    </codeinfo>
</Codelike>
```

1. 之后在实体类中，添加注解引用即可：

```java
@DefaultValue("Accode:SN-XG.InnerCode")

private String conicode;

@DefaultValue("Accode:SN-XG.Man.Contract")

private String concode;
```

```
由于上述方法会将单据与编码规则绑定死，所以可以通过以下方式进行灵活绑定
系统功能 SV 引入监听、UI 引入两个监听

实体类增加类注解：@SheetInfo(单据号)，编码规则列增加注解：@DefaultValue("SheetAccode:@SheetInfo")
界面进行编码规则绑定：【编码规则【数据库】】
新增一条编码规则、设置规则定义、绑定单据
最终生成的编码是规则定义中，按照序号拼成的字符串

```

## 单据复制

[官方文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-PLAT/Sheet/SheetCopy.md#%E5%8D%95%E6%8D%AE%E5%A4%8D%E5%88%B6)

一些业务场景，可能需要频繁创建单据，且每个单据的数据基本一致。单据复制可以将已生效的单据进行复制生成新单据，并能指定不复制哪些单据

1. 界面增加按钮，按钮号：sheetCopy

1. 增加 JS 监听

```xml
<!-- 举例：系统功能文件中增加 -->
<!-- sheetCode：单据号 -->
<Functions>
    <Functype>SN-PLAT.JS</Functype>
    <Subtype>2</Subtype>
    <Funcimpl>
        <![CDATA[
           #new snsoft.plat.bas.sheet.copy.SheetCopyListener({tgtUINames:["sn_xg_contract"],sheetCode:"SN-XG.Man.Contract"})
        ]]>
    </Funcimpl>
    <Remark>入口JS监听：单据复制</Remark>
</Functions>
```

1. Impl 工程创建单据拷贝监听（具体功能暂时未涉及）

```java
public class ContractSheetCopyListener implements SheetCopyListener<Contract>
{
    public ContractSheetCopyListener(Map<String,Object> param)
{
    
    }
}
```

1. UI 工程/resources/sheetcopy 下创建单据号命名的 xml 文件

```xml
<!-- 举例：SN-XG.Man.Contract.xml -->

<?xml version="1.0" encoding="UTF-8"?>
<SheetCopyDef copyflags="1"
xmlns="http://www.snsoft.com.cn/schema/SheetCopyDef" xsi:schemaLocation="http://www.snsoft.com.cn/schema/SheetCopyDef http://www.snsoft.com.cn/schema/SheetCopyDef.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Title>${RES.$.title_sheetcopy_contract?合同单复制}</Title>
    <Remark><![CDATA[
        业务说明：合同单复制
        1、生成草拟单据
        负责人：马寅广
	]]></Remark>

     <!-- 拷贝监听 -->
    <Listeners>
        <![CDATA[
		  snsoft.ft.asse.man.service.impl.ContractSheetCopyListener.new
		]]>
    </Listeners>
    
     <!-- 指定某个表的某个字段不复制；主表的基本字段（内码、创建时间等不复制） -->
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>conicode</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>status</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>vprepare</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>predate</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>modifier</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>modifydate</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>ratifydate</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>signdate</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contract</NotCopyTableName>
        <NotCopyColumnName>freightfcy</NotCopyColumnName>
    </Details>

    <!-- 子表有内码以及主表关联字段时，也不复制 -->
    <Details>
        <NotCopyTableName>myg_contractg</NotCopyTableName>
        <NotCopyColumnName>congicode</NotCopyColumnName>
    </Details>
    <Details>
        <NotCopyTableName>myg_contractg</NotCopyTableName>
        <NotCopyColumnName>conicode</NotCopyColumnName>
    </Details>
</SheetCopyDef>
```

## 单据拷贝

与单据复制不同，单据拷贝是依靠 A 单据的数据生成 B 单据，[官方文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-PLAT/Tools/DataCopy.md#%E6%95%B0%E6%8D%AE%E6%8B%B7%E8%B4%9D)

通过增加监听，即可绑定指定界面的“拷贝新建”按钮，以下步骤仅实现在界面中自定义按钮并调用拷贝监听方法

1. 增加拷贝定义：UI 工程/src/main/resources/cfg/res/datacopy/任意名称.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<CopyDef xmlns="http://www.snsoft.com.cn/schema/CopyDef" xsi:schemaLocation="http://www.snsoft.com.cn/schema/CopyDef http://www.snsoft.com.cn/schema/CopyDef.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Name>拷贝内贸销售合同（敞口销售）生成内贸合同审批单（敞口采购）</Name>
    <!-- 数据拷贝类型，指向 datacopy/copytype 目录下的文件 -->
    <CopyType>SN-PLAT.05</CopyType>
    <Remark><![CDATA[
    ]]></Remark>
    <!-- Attrs 属性配置，具体属性解释及作用在 copyType 文件中有说明 -->
    <!-- 资源加载监听，获取要拷贝的单据数据 -->
    <Attrs>
        <Aname>srcbean</Aname>
        <Stdvalue>FT-TRD.TX.Prj.SePODomProjectBySeSMDomSalOrdCopyDataLoader</Stdvalue>
    </Attrs>
    <!-- 要生成的单据号 -->
    <Attrs>
        <Aname>sheetcode</Aname>
        <Stdvalue>FT-TRD.TX.Prj.SePODomProject</Stdvalue>
    </Attrs>
    <!-- 拷贝监听，可以在各个时机对拷贝生成的数据进行修改 -->
    <Attrs>
        <Aname>listeners</Aname>
        <Stdvalue><![CDATA[
            snsoft.ft.trd.tx.prj.sepodom.service.impl.SePODomProjectBySeSMDomSalOrdCopyDataListener.new
        ]]></Stdvalue>
    </Attrs>
    <Attrs>
        <Aname>exttablenames</Aname>
        <Stdvalue>ft_trd_prj_extpe</Stdvalue>
        <Remark>辅表</Remark>
    </Attrs>
    <Attrs>
        <Aname>gtablejoincolumn</Aname>
        <Title>子表关系字段</Title>
        <Stdvalue><![CDATA[
             ft_trd_prj_peg.intrsalordgicode=ft_trd_sodg.salordgicode
        ]]></Stdvalue>
    </Attrs>

     <!-- 主要部分：指定两个单据的表之间的字段对应关系，特殊规则参考 copyType 文件 -->
     <!-- 目标表=源表 -->
    <Attrs>
        <Aname>copyfldinfo</Aname>
        <Stdvalue><![CDATA[
            ft_trd_prj.UP=ft_trd_sod:
                *;
            ft_trd_prj_extpe.UP=ft_trd_sod:
                *;
		]]></Stdvalue>
    </Attrs>
</CopyDef>
```

1. 建立拷贝数据选择弹窗：以下代码为销售合同选择对话框

```xml
<?xml version="1.0" encoding="UTF-8"?>
<B title="销售合同选择对话框" fullPage="true" options0="32" all_c_columns="true"
xmlns="http://www.snsoft.com.cn/schema/UI" xmlns:m="http://www.snsoft.com.cn/schema/UI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.snsoft.com.cn/schema/UI http://www.snsoft.com.cn/schema/UI.xsd">

    <m:DialogPane layoutm="grid" name="query" cellcols="4" uiprops.subCompOpts="2" clayoutwids="${RES.$.E.Q.4CW}" region="north" title="查询面板" xprops.btnsLayoutMode="2" xprops.showRefreshPane="true" showToolbar="false" uiprops.backInitValues="true">

        <uilisteners><![CDATA[
				snsoft.ft.code.bcode.bcode.FTUcodeWcodeInitValueListener.new;
				snsoft.ft.comm.busi.FTBusiFilterDateDefaultUIListener.new?dateColumns=etdfm&type=1
        ]]></uilisteners>

        <c name="salprjcode" sqltype="12" suffixMatch="true" prefixMatch="true" title="" toUpper="true" uiprops.bgLabel="销售合同号/合同审批单号" uiprops.cellClassName="td-input-align" hidden="true" nmpre="filter" />
        <c name="sumoutcode" sqltype="12" suffixMatch="true" prefixMatch="true" title="" uiprops.bgLabel="${RES.$.ft_trd_sod.query_sumoutcode}" uiprops.cellClassName="td-input-align" nmpre="filter" />
        <c name="bcode" title="${RES.$.bcode}" sqltype="12" codedata="#FT-CODE.BWcode" cmparams="status:'~10'" showfname="true" tipIfOverflow="true" uiprops.textDir="rtl" titleLeft="true" cmparams.sheetcode="FT-TRD.TX.Ord.SeSMDomSalOrd" cmparams.opids="R,C" disableed="true" showname="true" selectMiddle="true" aidInputerBtn="true" nmpre="filter" />
        <c name="wcode" title="${RES.$.wcode}" sqltype="12" codedata="#FT-CODE.Wcode" cmparams="status:'~10'" disableed="true" showname="true" aidInputerBtn="true" cmparams.sheetcode="FT-TRD.TX.Ord.SeSMDomSalOrd" cmparams.opids="R,C" nmpre="filter" />
        <c name="corpbcode" title="${RES.$.corpbcode}" sqltype="12" codedata="#FT-CODE.CorpBcode" cmparams="status:'~10'" cmparams.sheetcode="FT-TRD.TX.Ord.SeSMDomSalOrd" cmparams.opids="R,C" disableed="true" showname="true" selectMiddle="true" aidInputerBtn="true" nmpre="filter" />
        <c name="vprepare" sqltype="12" title="${RES.$.vprepare}" codedata="#FT-CODE.Users" cmparams="status:'~10'" aidInputerBtn="true" disableed="true" cmparams.sheetcode="FT-TRD.TX.Ord.SeSMDomSalOrd" cmparams.opids="R,C" nmpre="filter" />
        <c name="signdatefrom" title="${RES.$.ft_trd_sod.signdatefrom}" sqltype="91" aidInputerBtn="true" nmpre="filter" />
        <c name="predatefrom" title="${RES.$.predatefm}" sqltype="91" aidInputerBtn="true" nmpre="filter" />
        <c name="signdateto" title="${RES.$.dateto}" sqltype="91" dateDelayToNextDay="true" aidInputerBtn="true" hidden="true" nmpre="filter" />
        <c name="cuicode" title="${RES.$.cuicode}" sqltype="12" hidden="true" nmpre="filter" />
        <c name="status" hidden="true" title="${RES.$.status}" sqltype="12" codedata="#90.status" cmparams="sheetcode:'FT-TRD.TX.Ord.SeSMDomSalOrd'" droplist="true" aidInputerBtn="true" droplistWithSearchPane="true" disableed="true" aiprops="promptInfo:'',cellTextFmt:['${code}-${name}']" listSBoxOnTop="true" nmpre="filter" />
        <c hidden="true" name="status2" sqlexpr="status" title="${RES.$.status}：" sqltype="12" codedata="#90.status" cmparams="sheetcode:'FT-TRD.TX.Ord.SeSMDomSalOrd'" cliInitAllCodes="true" nmpre="filter" />
    </m:DialogPane>

    <m:GridTable mainui="query" name="ft_trd_sod" sqlexpr="ft_trd_sod" title="${RES.$.title_FT-TRD.TX.Ord.SeSMExpSalOrd}"
xprops.LoadDataService="FT-TRD.TX.SeSMDomSalOrdUIService#queryEntryUI" mutiple="true" region="center"
rdonly="true" disableSort="true" disableSave="true"  noCollapseQPaneAfterRefresh="true" disableTblEnterEd="true">

        <c name="salordicode" sqltype="12" title="${RES.C}" width="60" hidden="true"/>
        <c name="salordcode" sqltype="12" title="${RES.$.salordcode}" width="${RES.$.E.G.CW.outcode}"/>
        <c name="sheetcode" sqltype="12" title="${RES.C}" hidden="true" width="100"/>
        <c name="status" sqltype="12" tipIfOverflow="true" title="${RES.$.status}" fixedLeft="true" codedata="#90.status" cmparams="sheetcode:'FT-TRD.TX.Ord.SeSMDomSalOrd'" showname="true" width="${RES.$.E.G.CW.status}" uiprops.renderer="new snsoft.plat.bas.comm.render.StatusCellRender({})"/>
        <c name="wfcode" title="${RES.$.wfcode}" sqltype="12" hidden="true" width="${RES.$.E.G.CW.status}"/>
        <c name="wfuid" title="${RES.$.wfuid}" sqltype="12" hidden="true" width="${RES.$.E.G.CW.status}" codedata="#SN-APPR.wfunit" showname="true" tipIfOverflow="true" xprops.CodeData.KeyNames="wfcode"/>
        <c name="signdate" title="${RES.C}" sqltype="91" width="${RES.$.E.G.CW.date}"/>
        <c name="salccode" sqltype="12" tipIfOverflow="true" title="${RES.$.ft_trd_sod.salccode}" disableed="true" codedata="#FT-CODE.CcodeLMExt" showname="true" aidInputerBtn="true" width="${RES.$.E.G.CW.ccode}"/>
        <c name="ccodetrust" sqltype="12" tipIfOverflow="true" title="${RES.C}"  disableed="true" codedata="#FT-CODE.CcodeLMExt" showname="false" aidInputerBtn="true" width="${RES.$.E.G.CW.ccode}" hidden="true"/>
        <c name="fcode" sqltype="12" title="${RES.$.fcode}" disableed="true" width="${RES.$.E.G.CW.fcode}" align="center"/>
        <c name="fcy" sqltype="2" title="${RES.C}" width="${RES.$.E.G.CW.fcy}" mindeci="2"/>
        <c name="prjcodelist" sqltype="12" title="${RES.$.prjcode}" width="${RES.$.E.G.CW.outcode}"/>
        <c name="outordcode" sqltype="12" title="${RES.C}" width="${RES.$.E.G.CW.outcode}"/>
        <c name="busimode" title="${RES.$.busimode}" sqltype="12"  width="${RES.$.E.G.CW.busimode}" aidInputerBtn="true" codedata="#DT_FT-CODE.Busimode" showname="true" disableed="true" />
        <c name="psmode" title="${RES.$.psmode}" sqltype="12"  width="${RES.$.E.G.CW.psmode}"  tipIfOverflow="true" aidInputerBtn="true" codedata="#DT_FT-CODE.PsMode" disableed="true" showname="true"/>
        <c name="trademode" title="${RES.$.trademode}" sqltype="12" width="${RES.$.E.G.CW.trademode}" aidInputerBtn="true"  codedata="#DT_FT-CODE.TradeMode" disableed="true" showname="true"/>
        <c name="purmode" title="${RES.$.purmode}" sqltype="12"  width="${RES.$.E.G.CW.busimode}"   codedata="#DT_FT-CODE.PurMode" showname="true" tipIfOverflow="true" />
        <c name="wcode" sqltype="12" title="${RES.$.wcode}" codedata="#FT-CODE.Wcode" showname="true" width="${RES.$.G.CW.wcode}"/>
        <c name="bcode" sqltype="12" title="${RES.$.bcode}" codedata="#FT-CODE.Bcode" showname="true"  showfname="true" tipIfOverflow="true" uiprops.textDir="rtl" width="${RES.$.E.G.CW.bcode}"/>
        <c name="corpbcode" title="${RES.$.corpbcode}" sqltype="12" width="${RES.$.E.G.CW.bcode}" aidInputerBtn="true" codedata="#FT-CODE.CorpBcode" cmparams.sheetcode="FT-TRD.TX.Ord.SeSMDomSalOrd" cmparams.fromBtype="&quot;01&quot;" cmparams.opids="R,C" showname="true"/>
        <c name="costbcode" title="${RES.$.costbcode}" width="${RES.$.E.G.CW.costbcode}" disableed="true" aidInputerBtn="true" codedata="#FT-CODE.CostBcode" showname="true" sqltype="12" hidden="true" tipIfOverflow="true"/>
        <c name="vprepare" title="${RES.$.vprepare}" sqltype="12" width="${RES.$.G.CW.ucode}" codedata="#FT-CODE.Users" showname="true"/>
        <c name="predate" title="${RES.$.predate}" sqltype="93" width="${RES.$.E.G.CW.time}" descOrd="true"/>
        <c name="modifier" title="${RES.$.modifier?修改人}" sqltype="12" width="${RES.FT-CODE.G.CW.ucode}" codedata="#FT-CODE.Users" hidden="true" showname="true" modifierColumn="true" rdonly="true"/>
        <c name="modifydate" title="${RES.$.modifydate?修改时间}" sqltype="91" width="${RES.FT-CODE.E.G.CW.time}" hidden="true" modifydateColumn="true" rdonly="true" align="center"/>
        <c name="cuicode" title="${RES.$.cuicode}" sqltype="12" hidden="true"/>
    </m:GridTable>
</B>
```

1. 在要添加“拷贝新建”按钮的界面，增加按钮

```xml
<ToolbarBtn name="ft_trd_prj_btn_copyData" title="${RES.$.title.F.btn.datacopy?拷贝新建}" noClientComponent="true"/>

<attr type="203" name="copyData" title="${RES.HBNZ-TRD.title.F.btn.copyData?拷贝新建}" _rlog="true"/>
```

1. 在要添加“拷贝新建”按钮的界面，增加监听

1. SheetDataCopyListener 相关参数解释，参见

```xml
<Functions>
    <Functype>SN-PLAT.JS</Functype>
    <Subtype>2</Subtype>
    <!-- command:'copyData' 表示将监听的方法绑定到界面的 copyData 按钮上 -->
    <Funcimpl><![CDATA[
        #new snsoft.ft.comm.datacopy.SheetDataCopyListener({copyMode:'10',commands:[{command:'copyData',matchCol:'salordicode',cpid:'FT-TRD.TX.Prj.SePODomProjectBySeSMDomSalOrd',isDistinct:false,muiid:'FT-TRD.TX.Prj.SePODomProjectBySeSMDomSalOrd',sheetCode:'FT-TRD.TX.Prj.SePODomProject',copytype:'20',modifyCells:{'innercode':'salordicode','outcode':'salordcode'}}]})
    ]]></Funcimpl>
    <Remark>入口JS监听：创建入口拷贝按钮</Remark>
</Functions>
```

1. 如果想在某个监听方法中直接调用拷贝方法，而不是让其自动绑定到界面上的某个按钮，则可以这么做：

```java
// 任意监听类
// 弹窗界面的确定按钮方法
public void copySalComOk(DialogPane dialog, Button btn, String command){
    // 1、获取拷贝定义的父级组件，一般是要弹出拷贝定义数据选择框的上一级 table
    // 该组件需要已经通过 <jsListener> 标签或系统功能文件声明了 sheetDataListener 监听
    Table table = (Table) dialog.getMainComponentByName("hbnz_trd_ord_copy");
    // 2、获取单据拷贝监听
    SheetDataCopyListener listener = table.getListener(SheetDataCopyListener.class);
    // 3、若想动态设置拷贝定义数据选择框的查询参数，则可进行如下设置
    // 获取指定按钮的配置参数，示例代码获取的第一个，因为我的监听只配置了一个按钮，即 SheetDataCopyListener 的 commands 参数
    BusiSheetCopyParamsInfo[] commands = (BusiSheetCopyParamsInfo[]) listener.$get("commands");
    BusiSheetCopyParamsInfo copyData = commands[0];
    // 设置查询参数
    JSObject<Object> refreshParams = $o();
    // 参数一：单据
    refreshParams.$set("sheetcode", "FT-TRD.TX.Ord.SeSODomSalOrd");
    // 参数二：销售客户

    refreshParams.$set("salccode", ccode);
    copyData.refreshParams = refreshParams;
    // 4、调用该按钮方法进行弹窗
    listener.performCommand(table, "copyData", listener);
}
```

1. 可以设置拷贝监听，在状态改变后、改变前进行操作

```java
/**
 * <p>标题：拷贝内贸销售合同（敞口销售）生成内贸合同审批单（敞口采购）拷贝监听</p>

 * <p>作者：马寅广</p>

 */
public class SePODomProjectBySeSMDomSalOrdCopyDataListener extends DefaultDataCopyListener<PlatBusiSheetCopyParams,PlatBusiSheetCopySource,PlatBusiSheetCopyTarget,String>
{
    /**
     * 字段赋值
     *
     * <AUTHOR>


     * @param target


    **/
    @Override
    public void beforeSave(PlatBusiSheetCopyTarget target)
{
        super.beforeSave(target);
        SePODomProjectHBNZ sePODomProjectHBNZ = (SePODomProjectHBNZ) target.getTarget();
    }
}
```

## 单据撤单

单据从生效状态撤回，一般需要更改状态为“草拟”，校验是否能撤单等，具体参考[帮助文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-PLAT/Sheet/Retract.md#%E5%8D%95%E6%8D%AE%E6%92%A4%E5%8D%95)

1. Impl 工程新建撤单 Bean，用于处理特殊操作，如果仅仅是状态改变，则不需要新建

```java
// Contract 是撤单的单据实体类
@Service("SN-XG.ContractRetractBean")
public class ContractRetractBean implements RetractBean<Contract>
{
    // 撤单检查
    @Override
    public List<RetractEntity> check(RetractContext context, RetractEvent<Contract> event, String innerCode)
{
        return Arrays.asList(new RetractEntity()
{
            // 返回是否能撤单。例子中单据金额超过一万则不能撤单
            @Override
            public boolean canRetract()
{
                Contract record = event.getRecord(innerCode);
                return record.getSignfcy().compareTo(new BigDecimal(10000)) < 0;
            }
        
            // 检查不通过后，要返回的错误信息
            @Override
            public BaseException getError()
{
                return new BaseException("SN-XG.Man.0001", ExceptionUtils.getErrTextByModuleCode("SN-XG", "Man.0001"));
            }
        });
    }
    
    // 撤单后要进行的处理。例子中通过调用业务层方法，进行操作
    @Override
    public void retract(RetractContext context, RetractBean.RetractEvent<Contract> event, String innerCode)
{
        Contract vo = event.getRecord(innerCode);
        ContractService contractService = SpringBeanUtils.getBeanByName(ContractService.BeanName);
        contractService.retract(vo);
    }
}
```

1. UI 工程/resources/cfg/res/retract 增加撤单配置 xml，文件名为单据名

```xml
<?xml version="1.0" encoding="UTF-8"?>
<SheetRetractDef xmlns="http://www.snsoft.com.cn/schema/SheetRetractDef" xsi:schemaLocation="http://www.snsoft.com.cn/schema/SheetRetractDef http://www.snsoft.com.cn/schema/SheetRetractDef.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Title>合同表</Title>
    <Remark>
        <![CDATA[
            1、负责人：马寅广；
            2、撤单后将单据状态改为10；
            3、主表签约金额超过一万时禁止撤单
            4、撤单完成后清空主表运费
        ]]>
    </Remark>
    <!-- 1:异步撤单 -->
    <RetractFlags>0</RetractFlags>
    <Retracts>
        <BeanName>SN-PLAT.StatusRetractBean</BeanName>
        <Remark>
            <![CDATA[
                单据状态：
                1、：非生效状态不允许撤单；
                2、：撤单后将状态改为草拟；
            ]]>
        </Remark>
    </Retracts>

    <!-- 引用刚刚定义的 Bean -->
    <Retracts>
        <BeanName>SN-XG.ContractRetractBean</BeanName>
        <Remark>
            <![CDATA[
                数据：
                1、：主表签约金额超过一万时禁止撤单；
                2、：撤单完成后清空主表运费；
            ]]>
        </Remark>
    </Retracts>
</SheetRetractDef>
```

1. UI 系统功能增加监听

```xml
<Functions>
    <Functype>SN-PLAT.JS</Functype>
    <Subtype>2</Subtype>
    <Funcimpl>
        <![CDATA[
            #new snsoft.ft.comm.retract.FTRetractListener({})
        ]]>
    </Funcimpl>
    <Remark>撤单监听</Remark>
</Functions>
```

## 单据补数（查询工具）

在入口处，可能需要显示子表数量统计等不是主表字段且需要稍微复杂一些查询的数据，具体参考[帮助文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-PLAT/Tools/QueryToolDef.md#%E6%9F%A5%E8%AF%A2%E5%B7%A5%E5%85%B7)

补数的字段，需要在对应实体类中有同名属性（可能不需要），该属性不需要配置 @Column（可能不需要）

1. UI 工程/resources/cfg/res/qrytool 新建单据补数配置文件，文件名：单据名 + QR

```xml
<?xml version="1.0" encoding="UTF-8"?>
<QueryToolDef xmlns="http://www.snsoft.com.cn/schema/tool-query" xsi:schemaLocation="http://www.snsoft.com.cn/schema/tool-query http://www.snsoft.com.cn/schema/tool-query.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Title>销售合同入口子表总数量补数</Title>
    <Remark><![CDATA[
        销售合同入口补 子表总数量
    ]]></Remark>
    <Details>
        <Title>销售合同入口子表总数量补数</Title>
        <Remark><![CDATA[
            销售合同入口补 子表总数量
        ]]></Remark>
        <!-- 匹配列名，查询到的数据根据该列名与界面中的每一行数据进行匹配 -->
        <!-- 例如：查出每个学生的总分，根据界面中的学号，将每个总分补充到对应学生数据行上 -->
        <MatchColumns>conicode</MatchColumns>
        <!-- 数据要补充到界面哪一列 -->
        <ResultColumns>sontotal</ResultColumns>
        <!-- 拷贝的查询参数，用来替换 sql 里的 <cmfilter> -->
        <CMFilter>s.cuicode=:cuicode</CMFilter>
        <!-- 数据加载实现（参见接口） -->
        <!-- 实现 snsoft.plat.bas.qrytool.service.QueryToolDataLoader -->
        <!-- 1、可以是Bean，格式为【#BeanName】 -->
        <!-- 2、可以是Tac，默认Tac； -->
        <LoadImpl>#FT-CODE.FTCreditRegQueryToolService</LoadImpl>
        <!-- 要执行的 sql，<infilter> 替换成界面中所有行的 FilterColumn 列的值 -->
        <LoadSql><![CDATA[
            select s.conicode, sum(qty) as sontotal
            from myg_contract s, myg_contractg c
            where s.conicode = c.conicode and c.conicode in(<infilter>)  <cmfilter> group by s.conicode
        ]]></LoadSql>
        <!-- 原始数据取数列 -->
        <FilterColumn>conicode</FilterColumn>
        <!-- 数据源表名 -->
        <TableName>myg_contract</TableName>
        <!-- 聚合列 -->
        <!-- 可以通过对主键进行“分组”聚合，其余字段“去重逗号分割”聚合，实现查询子表的某个字段的汇总值。例如主表界面显示对应子表的所有销售合同号 -->
        <!-- 即 Aggs 至少有两个：分组列、聚合列 -->
        <Aggs>
            <!-- 要聚合的列，如果有查询语句中有别名，则写别名 -->
            <ColumnName>purordlist</ColumnName>
            <!-- 聚合方式 -->
            <AggType>7</AggType>
        </Aggs>
    </Details>
</QueryToolDef>
```

1. UI 系统功能增加监听

```xml
<Functions>
    <Functype>SN-PLAT.UI</Functype>
    <Subtype>2</Subtype>
    <Funcimpl><![CDATA[
        snsoft.plat.bas.qrytool.QueryToolListener.new?qrycode=SN-XG.Man.ContractQR&tgtUINames=sn_xg_contract
    ]]></Funcimpl>
    <Remark>补数</Remark>
</Functions>
```

1. 上述方法可能会有更新不及时的问题。可以使用以下方法进行补数

1. 创建文件：resources/cfg/res/sysfunc/单据号.QR.xml（名称任意），代替 UI 文件指向补数配置文件

```xml
<?xml version="1.0" encoding="UTF-8"?>
<SystemFunctions xmlns="http://www.snsoft.com.cn/schema/plat-sysfunc" xsi:schemaLocation="http://www.snsoft.com.cn/schema/plat-sysfunc http://www.snsoft.com.cn/schema/plat-sysfunc.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Name>【付款申请单】查询</Name>
    <Remark><![CDATA[
        备用
    ]]></Remark>
    <Functions>
    <Functype>SN-PLAT.QR</Functype>
    <Subtype>1</Subtype>
    <Funcimpl>SN-PLAT.QueryToolQRListener?[{qrycode:'FT-SET.Pay.PayAppListFldsQR'}]</Funcimpl>
    <Remark>上级单据号list字段补数</Remark>
    </Functions>
</SystemFunctions>
```

1. Impl 层的 service 查询方法进行调用刚新建的 QR.xml 文件

```java
public QueryResults<MergePayApp> queryPayApp(PayAppEntryParams params)
{
    return super.uiQuery(params, "FT-SET.Pay.MergeApp.QR");
}
```

### UI 监听补数

有一些复杂逻辑的取数、处理，可以使用 UI 监听，在 java 类中实现补数

```xml
<Functions>
    <Functype>SN-PLAT.UI</Functype>
    <Subtype>1</Subtype>
    <Funcimpl>
       <![CDATA[
           HBNZ-TRD.TStock.TStockDomInMegUIListener?[{tgtUINames:'ft_trd_tstock',joinColumnName:'tsicode'}]
       ]]>
    </Funcimpl>
    <Remark>部门经理字段补数</Remark>
</Functions>
```

```java
/**
 * <p>标题：采购入库单 UI 监听补数[部门经理]</p>
 * <p>
 *  功能：
 * </p>
 * <p>其他说明：</p>
 * <p>作者：马寅广</p>
 * <p>审核：</p>
 * <p>重构：</p>
 * <p>创建日期：2024/4/7 下午5:17</p>
 */
@Component("HBNZ-TRD.TStock.TStockDomInMegUIListener")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class TStockDomInMegUIListener extends SystemFunctionListener {
    /** 部门经理岗位的所有人员编码 */
    private static Set<String> wcodesBcodeManager = new HashSet<>();
    // 组织结构服务
    private static PlatUCodeService ucodeService = UCodeServiceFactory.impl.getUCodeService();

    public TStockDomInMegUIListener(Map<String,Object> parameter)
    {
        super(parameter);
        // 读取参数
        StrUtils.obj2str(parameter.get("joinColumnName"));
        /** 部门经理岗位的所有用户编码 */
        Set<String> usersBcodeManager = ucodeService.getUserCodesByGWcode("019");
        // 将用户编码映射成人员编码
        wcodesBcodeManager = usersBcodeManager.stream().map(ucodeService::getWCodeByUserCode).collect(Collectors.toSet());
    }

    @Override
    public ReadDataSet onDataLoaded(UIEvent event, ReadDataSet rs) {
        ArrayReadDataSet ars = ArrayReadDataSetImpl.toArrayReadDataSet(rs);
        // 部门经理字段索引
        int targetColIdx = ars.columnAt("bcodemanager");
        // 业务员部门字段索引
        int srcColIdx = ars.columnAt("bcode");
        if(targetColIdx >= 0)
        {
            /** 所有数据 */
            Object[][] values = ars.getValues();
            // 遍历界面每条数据
            for(Object[] value : values)
            {
                // 获取该部门有岗位的人员
                String[] usersHasGw = ucodeService.getWcodesByBcode((String) value[srcColIdx]);
                // 查找该部门有岗位人员，是部门经理岗位的所有用户，用逗号拼接成字符串
                String usersBcodeManagerCur = Arrays.stream(usersHasGw).filter(wcodesBcodeManager::contains).distinct().collect(Collectors.joining(","));
                // 部门经理字段赋值
                value[targetColIdx] = usersBcodeManagerCur;
            }
        }
        return rs;
    }
}
```

## 单据附件

官方文档：[单据文件存档管理](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-PLAT/Sheet/SheetDoc.md#单据文件存档管理)

![](images/WEBRESOURCEa17db43104c238350e217748df5fafdaimage.png)

1. 界面引入模块

1. 其中nz_fa_pcassetcardg是关联的主表 uiname

1. 如果关联子表，则上传的附件是与子表数据进行绑定

```xml
<m:T title="${RES.$.title.F.tab.print?打印及附件}" uiprops.cellClassName="ui-tabpane-level2">
    <H name="sheetPrt" title="${RES.$.title.F.tab.prt?打印}" uiprops.leftSize="430" height="450">
        <GroupPane name="torder_dylx" title="文档类型" align="left">
            <include src="SN-PLAT.Doc.SheetDocResPub#printToolbar"/>
            <include src="SN-PLAT.Doc.SheetDocResPub#prtdoctype" mainui="nz_fa_pcassetcardg"/>
        </GroupPane>
        <GroupPane name="torder_dyfj" title="存档附件" align="left">
            <include src="SN-PLAT.Doc.SheetDocResPub#prtplat_sheetdoc" mainui="nz_fa_pcassetcardg"/>
        </GroupPane>
    </H>
    <H title="${RES.$.title.F.tab.docup?附件上传}" uiprops.leftSize="355" height="450">
        <GroupPane name="torder_fjlx" title="文档类型" align="left">
            <include src="SN-PLAT.Doc.SheetDocResPub#atdoctype" mainui="nz_fa_pcassetcardg"/>
        </GroupPane>
        <GroupPane name="torder_fjfj" title="存档附件" align="left">
            <include src="SN-PLAT.Doc.SheetDocResPub#atplat_sheetdoc" mainui="nz_fa_pcassetcardg"/>
        </GroupPane>
    </H>
    <P title="${RES.$.title.F.tab.template?范本}">
        <GroupPane name="torder_fblx" title="文档类型" align="left">
            <include src="SN-PLAT.Doc.SheetDocResPub#moldoctype" mainui="nz_fa_pcassetcardg"/>
        </GroupPane>
    </P>
</m:T>
```

1. 引入监听

1. 其中nz_fa_pcassetcardg为绑定的 uiname

1. 如果要绑定到子表，则 uiname 需要更换，且要单独为子表创建一个单据

1. 生成的监听数据，通过单据号、内码进行数据的绑定

1. 绑定的单据中，声明的实体类，必须继承自BasVO。外码可以没有，service 任意

```xml
<Functions>
    <Functype>SN-PLAT.UI</Functype>
    <Subtype>1</Subtype>
    <Funcimpl>
        <![CDATA[
            SN-PLAT.SheetDocSaveUIListener?[{'tgtUINames':'nz_fa_pcassetcardg','sheetCodes':'HBNZ-FA.Card.PcAssetCardg'}]
        ]]>
    </Funcimpl>
    <Remark>单据文档存盘监听</Remark>
</Functions>
<Functions>
    <Functype>SN-PLAT.JS</Functype>
    <Subtype>2</Subtype>
    <Funcimpl>
        <![CDATA[
            #new snsoft.plat.bas.sheet.doc.DocTypeResBtnJSListener({tgtUINames:["prtdoctype"],sheetCode:'HBNZ-FA.Card.PcAssetCardg'})
        ]]>
    </Funcimpl>
    <Remark>单据文档类型JS监听</Remark>
</Functions>
<Functions>
    <Functype>SN-PLAT.JS</Functype>
    <Subtype>2</Subtype>
    <Funcimpl>
        <![CDATA[
            #new snsoft.plat.bas.sheet.doc.SheetDocBtnJSListener({tgtUINames:["prtplat_sheetdoc"],sheetCode:'HBNZ-FA.Card.PcAssetCardg',aidColumn:'docpath',isOlViewImg:false,autoSave:false})
        ]]>
    </Funcimpl>
    <Remark>单据文档附件JS监听</Remark>
</Functions>
```

1. 声明附件实体类

1. UI 模块/cfg/res/doctype/voref 目录下，创建 VORef系统号.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<SheetDocTypeVoRef xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.snsoft.com.cn/schema/SheetDocTypeVoRef" xsi:schemaLocation="http://www.snsoft.com.cn/schema/SheetDocTypeVoRef http://www.snsoft.com.cn/schema/SheetDocTypeVoRef.xsd">
    <TypeVoRef sheetcode="HBNZ-FA.Card.PcAssetCardg" refvoclass="snsoft.ft.comm.doc.vo.FTSheetDoc"/>
</SheetDocTypeVoRef>
```

1. 单据附件复制

1. TRD 模块可使用工具类：TrdCommUtils.copySheetDoc(FTSheetVO oldRecord, FTSheetVO newRecord);

1. 方法的参数类型任意，只需要能够提供单据号和内码即可。因为附件就是根据这两个参数存储的

```java
public void copySheetDoc(VO oldRecord, VO record) {
    SheetDocService<?> docSercice = SpringBeanUtils.getBeanByName(SheetDocService.BeanName);
    SheetDocService.SheetDocCopyParams fmParam = new SheetDocService.SheetDocCopyParams();
    fmParam.setSheetcode("旧单据号");
    fmParam.setInnercode("旧内码");
    SheetDocService.SheetDocCopyParams toParam = new SheetDocService.SheetDocCopyParams();
    toParam.setSheetcode("新单据号");
    toParam.setInnercode("新内码");
    docSercice.copySheetDocRecords(fmParam, toParam);
}
```

# 数据库相关操作

## 多数据库统一

### 函数统一

底层可以接入多种数据库，各个数据库对于一些常用函数可能有些许不同。所以底层提供了宏定义函数，用来抹平各个数据库之间的函数差异

比如：通过 %SQLDATEYEAR$1 获取日期的年

具体可参考：

```sql
SELECT %SQLISNULL$2(fldname,fldname) as fldname FROM tableName
```

### 跨数据源统一

视图可能需要联查不同数据源的表，可以通过以下方式进行不同数据源表的联查

```sql
select * from %SQLDS(UCODECFG).ft_cd_cc_cc left join ft_trd_tstockg
```

## 服务端操作数据方法

在业务层中，想要操作数据库，可以调用父类的 uiQuery 等方法。也可以使用 dao 对象去操作

参见：[DAO 帮助文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=help/SN-CMC/Service/Persistence.md#DAO)

**操作步骤：**

1. 构造 VO，包括主键、要更新的字段等

1. 设置存盘模式（可以直接设置 saveMode 属性，也可以调用 setUpdate() 等方法）

1. 设置修改字段（通过 setStoredColumn() 或者 addStoredColumn() 方法）

1. 执行 save 方法

```
使用 Dao 操作，会忽略实体类中的默认值配置，所有属性均需自己赋值
Dao 操作会自动将子表数据插入
```

**获取 Dao 方法**

```java
// service 层若继承了 DefaultBusiAccessService.java，则可以调用 newDAO() 方法获取
DAO<T> gradeMasterDAO = newDAO();

// 也可以通过单据获取，底层均是通过 DAO.newInstance()
DAO<Object> classInfoDao = SheetUtils.newDAO("单据号");

// 通过实体类获取
DAO.newInstance(HbnzTStockDomOut.class);
```

| 方法 | 说明 | 
| -- | -- |
| int  | 存盘方法 | 
| int  | 更新方法 | 


> 注：save 或 insert 方法在保存具有主子表关系的实体类时，会自动将实体类中对应子表的数据也插入进去
> 例如成绩主表实体类中有 list 属性作为成绩子表的数据，insert 方法传入一个成绩主表实体类后，会将该实体类的成绩子表属性 list 读取并插入到成绩子表中
>  
> 通过主表 service 插入子表数据，即提供的是主表，要插入的子表数据通过主表成员变量赋值，需要注意以下几点：
> 1、主表设置 update，且增加子表属性成员变量的 storedColumn
> 2、子表设置为 insert，内码、主表内码不需要设置
> 3、通过默认值服务（参考本文“默认值”）对主表 vo 设置默认值，此时会自动对子表数据的内码、主表内码设值
> 4、调用 service 即可


```java
// 查询表总数
// 其中 Contract 为实体类，fcy 是实体类的 BigDecimal 属性，选择该属性仅是因为要字段映射到数字类型字段中
// 查询出的 SQL：select top 1 count(*) as fcy from 
myg_contract where 1=1
DAO<Contract> dao = SheetUtils.newDAO(Contract.SHEETCODE);
Contract contract = dao.queryOne(new String[]{"count(*) as fcy"}, SqlExpr.columnEqValue("1", 1));
return Integer.parseInt(contract.getFcy().toString());
```

## 不映射实体类的自定义查询

DAO 必须映射到某个实体类，有时候只需要查自定义的 SQL，返回一个值。则可以使用 DBUtils

```java
import snsoft.dx.DBUtils;
import snsoft.dx.Database;
import snsoft.dx.dataset.ArrayReadDataSet;

try (Database db = DBUtils.newReadDatabaseByTable("表名")) {
    String sql = "select * from 表名";
    ArrayReadDataSet dataSet = db.queryA(sql);
    // 获取第一行第一列的值
    Object value = dataSet.getValue(0 , 0);
    while (dataSet.nextRow()) {
        dataSet.getValue(0);
        dataSet.getValue(1);
    }
}
```

## 开启事务

```java
DAO.newInstance(VO实体类.class).trans(dao -> {
    // 方法体
})
```

# 资源文件定义 ResBundle

在 UI 工程/resources/cfg/resbundle 文件夹下，默认有两个文件：

- ResBundleERR系统号.inf：异常资源文件

- ResBundle系统号.inf：资源文件

用来存放界面展示内容的，例如按钮的中文名称，提示信息等

## 资源定义

```
#引用其他资源文件
$PARENT$=资源文件名

#变量名=中文名
predatefm=创建时间从
predateto=到
modifydatefm=修改时间从
modifydateto=到
```

## 前端引用

| 格式 | 作用 | 示例 | 
| -- | -- | -- |
| ${} | 解析为： |   | 
| ${RES.C} | 获取当前单元标题 |   | 
| ${RES.F} | 获取当前单元标题 |   | 
| ${RES.系统号.变量名} | 获取资源文件中的值 |   | 
| @UI | 获取界面定义标题 |   | 


> 可以在引用部分外增加其他字符，表示与引用的值进行拼接展示
> title = "---${RES.$.status?状态}："
> 上面代码是“状态”前边拼接“---”，后边拼接“：”，?表示在找不到资源定义时要展示的值
> ***注：必须是详细指定某一个 key，若是 RES.C 或者 RES.F 等依赖底层匹配 key，则不会拼接花括号后面的内容**


## 异常提示信息

ResBundleERR系统号.inf 文件存放异常提示信息，一般会在 JS 监听或者服务端使用，向客户端抛出异常

```
# 异常资源文件
unselect_row_error_tip=选择行有误，请重新选择后再进行操作！
status_error_tip=状态有误，无法执行该操作！
```

### 动态参数

异常提示信息可以设置变量，在服务端获取提示信息时，可以传递参数，达到动态设置提示信息的目的

例如：

```
# 异常资源文件
status_error_tip=%0状态有误，%1无法执行该操作！
```

## 代码获取资源文件

| 方法 | 说明 | 
| -- | -- |
| String | JS 监听获取异常信息 | 
| String | 同上，区别为多了一个可变参数 | 
| ResBundle | 与上面两个方法相同 | 
| String | 服务端获取异常信息 | 


# 默认提供功能

## 界面默认按钮

界面中可以通过配置按钮名称，就能调用底层默认实现方法以及属性控制

例如在界面没有做任何改动的情况下，保存按钮是置灰状态

1. toolbarbtn 声明按钮（也可以不声明，只用 attr 的，此时会没有图标）

1. toolbarbtn 的 name 属性是在 attr 的基础上，增加前缀：attr 所在 gridtable 的 name 属性 + “_btn"

```xml
<ToolbarBtn name="classInfoList_btn_add"
            title="${RES.START.btn_add}"
            noClientComponent="true"
            xprops.iconClassName="icons-btn-add"/>
```

1. attr 声明按钮

```xml
<attr type="203" name="refresh" title="刷新"/>
```

attr 的 name 设置为以下值，即可使用对应默认功能

```
refresh：刷新
save：保存
delete：删除
newSheet：新建，需要配合 OperateJSListener 列按钮监听，指定点击新建后的弹窗
```

## 工具栏隐藏

dialogPane 标签的 showToolbar 属性设为 false 即可隐藏

![](images/WEBRESOURCEc0dd185d59416177b7930575d8a6999c截图.png)

```xml
<m:DialogPane name="param"
              title="${RES.START.class_param}"
              region="north"
              cellcols="5"
              layoutm="grid"
              showToolbar="false"
              uiprops.backInitValues="true"
              xprops.btnsLayoutMode="2">
```

## Excel 导入

[Excel 导入官方文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/BasTool/ExcelImport.md#Excel%E5%AF%BC%E5%85%A5)

一般在入口处，增加“Excel 导入”按钮，点击后，弹出窗口，上传 Excel 文件后，导入数据

1. 界面创建按钮

1. 界面导入按钮的 name 属性格式为：XLSIMP_[xlscode]（注意：xlscode中的点号[.]使用下划线[_]代替）

1. xlscode 为导入模板的文件名

```xml
<!-- 假设生成的导入模板文件名为：HBNZ-SET.ft_set_rclm_excelImport.xml -->
<attr type="203" name="XLSIMP_HBNZ-SET_ft_set_rclm_excelImport" title="${RES.HD-CODE.gcodeimport?Excel导入}"/>
```

1. 引入监听

1. 该监听具体参数，可查看

1. 可通过：runParam 传入按钮所在页面的值，以便导入监听使用

1. 注：T.** 表示取界面中某个表的字段值，“表名”是页面的 name 属性，而不是真实数据库表名

```xml
<Functions>
    <Functype>SN-PLAT.JS</Functype>
    <Subtype>2</Subtype>
    <Funcimpl><![CDATA[
        #new snsoft.bastool.excel.ExcelStrategyImportListener({"tgtUINames":["hd_futures_detail"],xlscode:'HBNZ-SET.ft_set_rclm_excelImport','runParam':{'clmicode':'T.rclm#clmicode'}})
    ]]></Funcimpl>
    <Remark>Excel导入</Remark>
</Functions>
```

1. 创建导入模板

1. 路径：UI 模块/cfg/res/excel/

```xml
<?xml version="1.0" encoding="UTF-8"?>
<ExcelImportDef xmlns="http://www.snsoft.com.cn/schema/ExcelImportDef" xsi:schemaLocation="http://www.snsoft.com.cn/schema/ExcelImportDef http://www.snsoft.com.cn/schema/ExcelImportDef.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Title>固定资产卡片导入</Title>
    <!-- 01:单表导入;02:主子表导入;06:maplist -->
    <ImpType>SN-PLAT.02</ImpType>
    <!-- 0x01:设置默认值；0x02:校验合法性；0x04:存盘；0x08:错误不抛出；0x10:不走afterParse方法； -->
    <ImpOpts>0x01+0x02+0x04</ImpOpts>
    <VOClass>snsoft.hbnz.sna.fa.card.vo.HbnzFaCard</VOClass>
    <SheetCode>FT-FA.Card</SheetCode>
    <Optids>C</Optids>
    <ReadOptions><![CDATA[
    "skipRows":1,
    "sheetName":"固定资产卡片"
    ]]></ReadOptions>
    <TacFormula><![CDATA[
       #snsoft.ft.sna.fa.card.service.impl.FaCardExcelImportListener.new
    ]]></TacFormula>
    <ExcelTemplate>FT-FA.Card.xlsx|固定资产卡片.xlsx</ExcelTemplate>
    <Remark><![CDATA[
    单表：
    1、主表新建；
    2、处理默认值；
    3、处理码表转换；
    ]]></Remark>
    <Fields>
       <TableColumn>facode</TableColumn>
       <Title>资产编号</Title>
       <ExcelColumn>A</ExcelColumn>
       <CtrlOpts>0x0040</CtrlOpts>
    </Fields>

    <!-- 部门表 -->
    <Fields>
       <TableColumn>fa_cardb.bcode</TableColumn>
       <Title>部门</Title>
       <ExcelColumn>AJ</ExcelColumn>
    </Fields>

    <!-- 设备表 -->
    <Fields>
       <TableColumn>fa_cardeq.eqcode</TableColumn>
       <Title>设备编号</Title>
       <ExcelColumn>AN</ExcelColumn>
    </Fields>
</ExcelImportDef>
```

1. 创建监听类

1. IMPL 模块，增加监听类，用来对数据进行特殊处理

```java
public class HbnzInitClaimImportListener<V extends InitRecClaim> implements ExcelImportListener<V>
{
    /**
     * 应收票据登记导入构造
     *
     * <AUTHOR>
     * @date 2023/12/29 14:43
     *
     * @param params
     *
     * @return
     **/
    public HbnzInitClaimImportListener(Map<String,Object> params)
    {
    }

    /**
     * Excel 读取后，数据修正
     * 获取前端传入的参数
     * 
     **/
    @Override
    public boolean stopParse(ExcelImportEvent event, ExcelReader.ExcelRow row)
    {
        // 获取前端传入的参数：收款初始化主键
        clmicode = event.getStringParamValue("clmicode");
        // 获取“到账银行账号”
        if (row.getExcelRow() == 3)
        {
            bankacccode = row.getValue(1);
        }
        return false;
    }

    /**
     * 所有数据解析完：数据判重、设置数据默认值
     * <br/> 导入策略如果是“一次性导入”，此时数据已经导入完成
     * <br/> 如果是其他策略，则数据未导入
     *
     **/
    @Override
    public void afterAll(ExcelImportEvent event, List<V> dataList)
    {

    }
}
```

# 穿透配置_详情页

通过穿透配置功能，可以实现双击界面某一列，带数据跳转到详情页

详细配置参见[官方文档_穿透配置](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=help/SN-PLAT/Tools/ViewDetail.md)

## 详情页组成

详情页可以更好的展示复杂数据，让首界面仅展示主要内容，对数据的操作以及详细信息均挪至详情页

![](images/WEBRESOURCE48a9e5dc652d245046e6f5a659ef08be截图.png)

详情页的构成随意，但是有以下几点需要注意：

- 根标签为 PageBody

- 与主页面有区分

- xprops.showPageTitle：A4 纸显示页面标题，即页面首行中间的标题

- 标题右侧展示的“马寅广，草拟”，为 PageTitleBarJSListener 监听实现的，具体配置信息可查看本文“底层监听类”一节

- 隐藏的查询面板

- 用来接收双击穿透时传递的参数，以便确定要展示的成绩详情

- 一般是数据的主键

- 操作工具栏

- 详情页的工具栏和主页面的工具栏样式有一定区别

- Toolbar 标签指定该属性：uiprops.cellClassName="head-toolbar-panel"

- ToolbarBtn 标签指定该属性：uiprops.cellClassName="ui-head-btn"

- 基础信息

- 使用 RecordTable 标签展示表数据，并增加一个属性：uiprops.resetKeyFilterOnRefresh="1"

- 所有列均用 GroupPane 标签包裹，可指定一行显示的列数，并规定每列的宽度

- 明细

- 用 P 标签包裹，使用 GroupPane 将 GridTable 包裹，展示分组标题

- 系统信息

- 一般是展示主表的创修人信息，用 RecordTable 标签包裹 GroupPane 标签

```xml
<m:PageBody xmlns:m="http://www.snsoft.com.cn/schema/UI" xmlns="http://www.snsoft.com.cn/schema/UI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.snsoft.com.cn/schema/UI http://www.snsoft.com.cn/schema/UI.xsd" title="${RES.START.title_START.GradeDetail?成绩详情}" xprops.hasFixToolbar="true" xprops.showPageTitle="true" options0="32" all_c_columns="true">
            
    <!-- 隐藏筛选条件 -->
    <m:DialogPane name="param" region="north" layoutm="grid" hidden="true">
        <c name="scoreicode" title="成绩主表内码" sqltype="12"/>
    </m:DialogPane>
    
    <!-- 操作工具栏 -->
    <m:Toolbar name="gradeDetailToolbar" title="${RES.START.gradeDetailToolbar?成绩操作工具栏}" uiprops.cellClassName="head-toolbar-panel">
        <ToolbarBtn name="gradeDetailInfoList_bas_btn_save" title="${RES.START.btn_save?保存}" noClientComponent="true" xprops.iconClassName="icons-btn-save" uiprops.cellClassName="ui-head-btn"/>
        <!-- ……省略…… -->
    </m:Toolbar>
    
    <!-- 基础信息 -->
    <m:RecordTable name="gradeDetailInfoList_bas" title="${RES.START.gradeDetailInfoList_bas?基础信息}" sqlexpr="grade_master" mainui="param" autoRefresh="true" cellcols="1" xprops.LoadDataService="START_SCHOOL_MANAGER.GradeMasterUIService#queryUI" xprops.SaveDataService="START_SCHOOL_MANAGER.GradeMasterUIService#saveUI" uiprops.resetKeyFilterOnRefresh="1" skipRdOnlyCellOnEnterKey="true" noCollapseQPaneAfterRefresh="true">
        <jslistener><!-- ……省略…… --></jslistener>
        <uilisteners><!-- ……省略…… --></uilisteners>
        <attrs><!-- ……省略…… --></attrs>
        
        <GroupPane name="gradeDetailInfoListBas"
 title="${RES.$.gradeDetailInfoList_bas?基础信息}"
 layoutm="grid"
 cellcols="4"
 clayoutwids="100,100,90,90"
 uiprops.subCompOpts="2">
           <c name="scoreicode" title="成绩主表内码" sqltype="12" width="120" hidden="true"/>
           <!-- ……省略…… -->
       </GroupPane>
    </m:RecordTable>
           
    <!-- 成绩明细 -->
    <m:P>
        <GroupPane title="${RES.$.gradeDetailInfoList_detail?成绩明细}">
            <m:GridTable name="gradeDetailInfoList_detail"
 sqlexpr="grade_sub"
 title="${RES.$.gradeDetailInfoList_detail?成绩明细}"
 mainui="gradeDetailInfoList_bas"
 noCollapseQPaneAfterRefresh="true"
 autoRefresh="true"
 disableAppend="true"
 uiprops="hideVBar:true"
 uiprops.minVisibleRows="5"
 uiprops.fitInDOMHeight="0">
                <jslistener><!-- ……省略…… --></jslistener>
                <uilisteners><!-- ……省略…… --></uilisteners>
                <c name="scoregicode" title="成绩表内码" primaryKey="true" sqltype="12" width="120" hidden="true"/>
                <!-- ……省略…… -->
            </m:GridTable>
        </GroupPane>
    </m:P>
    
    <!-- 系统信息 -->
    <m:RecordTable name="gradeDetailInfoList_sys" mainui="gradeDetailInfoList_bas" cellcols="1">
        <GroupPane title="${RES.$.gradeDetailInfoList_sys?系统信息}" layoutm="grid" cellcols="4">
            <c name="vprepare" title="创建人" sqltype="12" width="70" rdonly="true"/>
            <c name="predate" title="创建时间" sqltype="91" width="130" rdonly="true"/>
            <c name="modifier" title="修改人" sqltype="12" width="70" rdonly="true" modifierColumn="true"/>
            <c name="modifydate" title="修改时间" sqltype="91" width="130" rdonly="true" modifydateColumn="true"/>
        </GroupPane>
    </m:RecordTable>
</m:PageBody>
```

## 穿透配置

1. 增加 JS 监听

1. 详情页中使用 ViewDetailListener

1. 入口使用 PlatEntryListener，该监听会加载单据，用来获取外码列和内码列。并且双击某行的任意一列，均可以跳转

```xml
<jslistener>
    #new snsoft.plat.bas.viewdetail.ViewDetailListener({})
</jslistener>


<jslistener>
    <![CDATA[
        #new snsoft.plat.bas.busi.PlatEntryListener({})
    ]]>
</jslistener>
<uilisteners>
    <![CDATA[
        snsoft.plat.bas.busi.PlatEntryListener?sheetCode=START.GradeMaster
    ]]>
</uilisteners>
```

1. 增加穿透配置文件

1. ui 工程/resources/cfg/res/viewdetail/ViewDetail系统号.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<ViewDetails xmlns="http://www.snsoft.com.cn/schema/tool-viewdetail" xsi:schemaLocation="http://www.snsoft.com.cn/schema/tool-viewdetail http://www.snsoft.com.cn/schema/tool-viewdetail.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Details>
        <!-- 双击列名 -->
        <Colname>tscode</Colname>
        <JSBlock>
            <![CDATA[
                //防止双击空白列
                scoreicode = dataSet.get("scoreicode")
                if scoreicode == null
                    return null
                end if
                
                //功能号，一般是 系统号.界面文件名
                info.funcid = "START.GradeDetail"
                //自动刷新
                info.pm.AutoRefresh = 1
                //传递初始值给查询面板
                info.pm["InitValue.scoreicode"] = data.get("scoreicode")
                return info
            ]]>
        </JSBlock>
        
        <!-- 或者使用 Tac 代码块 -->
        <TACBlock>
            <![CDATA[
                tsicode = dataSet.get("tsicode")
                if tsicode == null
                    return null
                end if

                //确保记录存在
                assertExists("ft_trd_tstock",tsicode)
                sheetCode = dataSet.get("sheetcode")
                sheetEntry = dataSet.get("sheetEntry")
                if sheetCode==null || (sheetEntry!=true && slicode!="<NULL>")
                    tableName="ft_trd_tstock"
                    sheetCode = getSheetCode(tableName,tsicode)
                end if
                if sheetCode=="HBNZ-TRD.TStock.VouInCan"
                    info.funcid = "HBNZ-TRD.TStock.In.TStockVouInCanDetail"
                end if
                info.pm.put("AutoRefresh",1)
                info.pm.put("InitValue.tsicode",tsicode)
                return info
            ]]>
        </TACBlock>
        
        <Remark>
            <![CDATA[
                1、负责人：马寅广
                2、功能：成绩详情页穿透
            ]]>
        </Remark>
    </Details>
</ViewDetails>
```

## 调用穿透定义

可以在 JS 中调用穿透定义，实现跳转某个单据的详情页

```java
public void onTableDblClick(Table table, TableEvent e) {
    if ($eq(getClickCol(table, e), "columnName")) {
        SheetService sheetService = RInvoke.newBean(SheetService.class);
        BusiObject busiObject = sheetService.getBusiObject("要跳转到的单据号");
        JSObject<Object> data = $o("sheetcode", busiObject.sheetcode);
        data.$set(busiObject.innerfld, "内码");
        ViewDetailListener.invokeViewDetail(data, busiObject.outerfld);
    }   
}

/**
 * 原获取列有问题，所以改为这样获取
 * 获取双击穿透的列
 * @param table
 * @param e
 * @return
 */
protected String getClickCol(Table table, TableEvent e)
{
    String colname;
    if ($bool(e))
    {
        colname = $bool(e.column) ? e.column.name : null;
    } else
    {
        colname = $bool(table.getSelectedColumn()) ? table.getSelectedColumn().name : null;
    }
    if ($bool(colname))
    {
        table.postPending(true);
    }
    return colname;
}
```

# 系统功能

详细介绍参见[官方文档_系统功能配置](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-PLAT/Tools/SystemFunction.md#系统功能配置)

将界面中引用的 JS 和 UI 监听整合到一个文件中，便于管理

## 配置文件

存放路径：UI 工程/resources/cfg/res/sysfunc/系统号.界面.UI（或者 SV 等）.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<SystemFunctions xmlns="http://www.snsoft.com.cn/schema/plat-sysfunc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.snsoft.com.cn/schema/plat-sysfunc http://www.snsoft.com.cn/schema/plat-sysfunc.xsd">
    <!-- 系统功能名称 -->
    <Name>成绩管理</Name>
    <!-- 系统功能说明 -->
    <Remark>
        <![CDATA[
            成绩管理
         ]]>
    </Remark>
    <!-- 系统功能列表 -->
    <Functions>
        <!-- 功能类型 -->
        <!-- SN-PLAT.UI：UI 监听 -->
        <!-- SN-PLAT.JS：JS 监听 -->
        <!-- SN-PLAT.QR：服务查询监听 -->
        <!-- SN-PLAT.SV：服务存盘监听 -->
        <!-- SN-PLAT.WF：服务审批监听 -->
        <Functype>SN-PLAT.UI</Functype>
        
        <!-- 功能子类型，与功能类型相关联 -->
        <!-- 功能类型为 QR/SV/WF 时，1：bean，2：class，3：tac -->
        <!-- 功能类型为 UI 时，2：class，3：tac -->
        <!-- 功能类型为 JS 时，2：class，4：JavaScript -->
        <Subtype>2</Subtype>
        
        <!-- 功能实现 -->
        <!-- 1、Bean：beanName -->
        <!-- 2、Class：类全路径名，可后拼接参数?key=value，等同于界面配置 -->
        <!-- 3、Tac：Tac 代码块 -->
        <!-- 4、JavaScript：JS 代码块 -->
        <Funcimpl>
            <![CDATA[
                #new snsoft.plat.bas.busi.PlatEntryListener({})
            ]]>
        </Funcimpl>
        
        <!-- 功能说明 -->
        <Remark>界面属性控制</Remark>
    </Functions>
    <!-- <Functions> 省略 </Functions> -->
</SystemFunctions>
```

## 界面引用

```xml
<uilisteners>
   snsoft.plat.bas.busi.PlatFunctionListener.new?funccode=系统功能文件名
</uilisteners>
<jslistener>
    <![CDATA[
        #new snsoft.plat.bas.busi.PlatFunctionListener({})
    ]]>
</jslistener>
```

# TAC

[TAC 工具官方文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/Tac/Exectac.md#Tac工具)

在界面中，可以在 stacscript 标签内编写 Tac 脚本

> 获取 bean 时，若 bean 不存在，不会报错，即使调用方法也不会报错


```java
println("=======")
db = getDatabaseByTable(("ft_set_inv_rc"))
//查询应收费用发票表的 内码 及 开票单位 字段
selectSql = "select invicode,ccode from ft_set_inv_rc where ccode is not null"
queryResult = db.query3(selectSql)

//{开票单位:内码}
relationMap = newHashMap()
//开票单位（去重后）
ccodeList = newArrayList()

for singleBean in queryResult
    relationMap.put(singleBean[1], singleBean[0])
    ccodeList.add(singleBean[1])
end for

//查询客商主表参数
queryParam = "('"
for ccode in ccodeList
    queryParam = queryParam + ccode + "','"
end for

queryParam = queryParam.substring(0, queryParam.length() - 2) + ")" 

//根据查询条件，查询客商主表数据
db1 = getDatabaseByTable(("ft_cd_cc_cc"))
selectSql1 = "select ccode, socialid, invaddrtel, invaccount from ft_cd_cc_cc where ccode in " + queryParam
queryResult1 = db1.query3(selectSql1)

//要更新的数据
updateList = newArrayList()
//业务层操作类
serviceBean = getBean("FT-SET.CommFeeRecInvService")
//需要更改的字段
updateFields = new java.lang.String[3]
updateFields[0] = "invtaxidentnum"
updateFields[1] = "invaddrtel"
updateFields[2] = "invaccount"
   
for singleBean1 in queryResult1
    //创建实体类
    entity = new snsoft.ft.set.inv.rec.vo.CommFeeRecInv()
    entity.setInvicode(relationMap.get(singleBean1[0]))
    entity.setInvtaxidentnum(singleBean1[1])
    entity.setInvaddrtel(singleBean1[2])
    entity.setInvaccount(singleBean1[3])
    entity.setUpdate()
    entity.addStoredColumns(updateFields)
    
    updateList.add(entity)
end for

serviceBean.save(updateList)
```

## 客户端造数据

在界面 xml 中，GridTable 中新增 <stacscript> 标签，写以下代码。界面点击查询后，会执行代码

```java
proc onDataLoaded(e,rs)
    ars = snsoft.dx.ReadDataSetFactory.impl.toArrayReadDataSet(rs,true)
    rows = 60
    cols = 10
    values = new java.lang.Object[rows][0]
    r = 0
    while r < rows
        c = 0
        vs = newArray(cols)
        while c < cols
            vs[c] = java.lang.String.format("R[%1$03d]C[%2$02d]",r,c)
            c = c + 1
        end while
        values[r] = vs
        r = r + 1
        println(c)
    end while
    return ars.setValues(values)
end proc
```

# 系统选项

[官方文档](https://n.esnsoft.cn/N9Bhelp/help.html?helpFile=/help/SN-CMC/SysOpTionsFile/SYSOPTIONSFILE.md#%E7%B3%BB%E7%BB%9F%E9%80%89%E9%A1%B9)

新增系统选项【平台】：ui 工程/app/SysOption系统号-CODE.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<SysOptions xmlns="http://www.snsoft.com.cn/schema/SysOption" xsi:schemaLocation="http://www.snsoft.com.cn/schema/SysOption http://www.snsoft.com.cn/schema/SysOption.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <!-- 系统选项 -->
    <SysOption type="C">
        <Key>HBNZ-CODE.GroupHeadLegalPerComCode</Key>
        <Title>【集团本部】法人公司编码</Title>
        <Flags>1</Flags>
    </SysOption>
</SysOptions>
```

![](images/WEBRESOURCE082e3840573f4b29679ba53ace5a57ed截图.png)

```java
// 获取系统选项
Object sysOption = SysConfig.impl.getSysOption("HBNZ-CODE.GroupHeadLegalPerComCode")
String sysOption = SysConfig.impl.getSysOptionAsStr("HBNZ-CODE.GroupHeadLegalPerComCode");
```

# N10开发

[N10开发说明文档](note://WEBe529d19e3c9c5aed2cdb7ff0da571a38)