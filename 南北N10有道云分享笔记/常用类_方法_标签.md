# 常用资源

- 

- 

- 

- 

# 标签列表

## B

界面根元素，对应整个界面

| 属性 | 说明 | 
| -- | -- |
| xmlns | 默认配置 / 约束文件 | 
| options0 | 根节点选项，可以设置界面是否为弹窗展示 | 
| all_c_columns | 为 c 列标签设置默认的资源文件引用：${RES.C} | 


## GridTable

界面中的表格，一般对应数据库中某个表

| 属性 | 说明 | 效果 | 
| -- | -- | -- |
| layoutOnClient | 客户端布局 |   | 
| clayoutwids |   | 
| uiprops.subCompOpts | 子元素宽度 | 
| uiprops.fitFullCols | 所有列的宽度 |   | 
| uiprops.disableColumnMove | 是否列禁止鼠标拖动 |   | 
| mutiple | 是否多选 |   | 
| uiprops.markSeledRow | 配合 mutiple，当为单选时，是否显示单选按钮 |   | 
| dsprops.pageRows | 分页行数，设置分页 |   | 
| dsprops.lastSort="['predate desc']" | 设置排序字段 |   | 
| uiprops.minVisibleRows | 最小显示行数 |   | 
| uiprops.maxVisibleRows | 最大显示行数，超过显示滚动条 |   | 
| uiprops.closeTabOnDelete | 行删除后关闭标签页 |   | 
| noCollapseQPaneAfterRefresh | 刷新后，不自动隐藏查询参数面板 |   | 
| sqlfilter | 表查询条件 |   | 
| disableAppend / disableInsert | 限制新增 |   | 
| uiprops.zdomid | 全屏时需要包含的标签 |   | 


## c

小写 c，代表一列

| 属性 | 说明 | 效果 | 
| -- | -- | -- |
| codedata | 指定码表 |   | 
| 指定字典 | 
| showname | 展示码表名称 | 
| showcode | 展示码表值 | 
| aidInputerBtn | 辅助输入按钮 | 
| droplist | 辅助下拉列表 |   | 
| uiprops.initOpVal | 数据比较方式 |   | 
| cmparams.SQLFILTER | 码表 SQL 过滤 |   | 
| cmparams.sheetcode | 码表单据过滤 |   | 
| cmprops | 码表属性，对应 CodeData 对象的属性 |   | 
| mutipleLine | 大文本（辅助录入） |   | 
| layoutheight；cellrows | 配合“大文本”，展示 |   | 
| initval | 初始值 | 特殊值（ | 
| align | 文本对齐方式 |   | 
| uiprops.maxCharsCnt | 最大输入字符个数 |   | 
| disableed | 是否禁止编辑 |   | 
| uiprops.bgLabel | 背景提示文字 |   | 
| noblank | 强制录入 |   | 
| title | 列标题 |   | 
| tipIfOverflow | 漂浮显示 |   | 
| mergeSameToLastRow | 合并单元格 |   | 
| toUpper | 转换为大写 |   | 
| notInDataSet | 不包含在数据中 |   | 
| fixedLeft | 冻结列 |   | 
| mindeci | 小数位 |   | 
| asceOrd / descOrd | 升序 / 降序 |   | 
| name 显示复选框 | 复选框 |   | 
| uiprops.renderer | 指定状态列 |  | 
| evalexpr | 计算表达式 |   | 
| xprops.cpmastercol | 拷贝主表字段 |   | 
| aiprops | 辅助输入属性，JSON 格式 |   | 
| aiprops.copyMap | 拷贝辅助录入字段 |   | 


# 常用类

一般情况下，基本常用功能可以搜索 Utils 结尾的类

## JS常用类

### DialogPane

弹窗，在 JS 监听中，可以手动生成弹窗，具体参考[南北开发说明文档](note://WEB7ebfc997c9e0c4501ab2c3542e3dd999) 监听->界面弹出

| 方法 / 属性 | 功能 | 
| -- | -- |
| Component | 根据名字查找子组件（或孙组件等） | 
| void  | 根据名字设置某子组件（或孙组件等）的输入值 | 


### TableEvent

监听类中，作为入参存在。可用来获取触发事件的某一列、行

| 方法 / 属性 | 子属性 | 功能 | 
| -- | -- | -- |
| xjs.ui.Cell | name | 获取当前列的名称 | 
| xjs.ui.Component | selectOptions | 获取当前列的码表 | 


### Table

界面定义表对象，对应界面布局，例如 RecordTable，GridTable，常见的子类：RecordTable，GridTable

监听类中，作为入参存在。可用来获取整个表的数据及配置参数

| 方法 / 属性 | 功能 | 
| -- | -- |
| name | 获取当前表的名称，即触发事件所在的 GridTable 或其他 Table 标签的 name 属性 | 
| DataSet | 返回 DataSet 对象 | 
| TableColumn | 通过列名，返回 TableColumn 对象 | 
| void  | 用于界面刷新等 | 
| void  | 重新获取表数据 | 
| void  | 跳转至指定行 | 
| void  | 跳转至最后一行 | 
| int  | 获取当前行 | 
| Object | 获取选中行指定列的值 | 
| Object | corpbcode：公司编码 | 
| int | 非空检查 | 
| void  | 弹出指定窗口 | 


### TableColumn

对应表列，可通过 Table 获取

| 方法 / 属性 | 功能 | 
| -- | -- |
| setXXX() | 设置列属性，一般通过 | 


### DataSet

界面的数据集对象

可通过

| 方法 / 属性 | 功能 | 
| -- | -- |
| Table | 获取表 | 
| String name | 获取当前表的名称，即触发事件所在的 GridTable 或其他 Table 标签的 name 属性 | 
| DataSetRow[] rows | 获取所有行 | 
| DataSet master | 获取当前子表对应的主表 | 
| String  | 当前表的表名，即触发事件所在的 GridTable 或其他 Table 标签的 sqlexpr 属性 | 
| int  | 获取某一列的索引 | 
| int  | 获取当前行，如果没有选中行，返回 -1 | 
| void  | 存盘 | 
| T  | 获取指定行某一列的值 | 


### DataSetUtils

工具类，JS 监听类中直接使用

| 方法 / 属性 | 功能 | 
| -- | -- |
| void  | 刷新表当前行的列值 | 


### DataSetEvent

在 JS 监听周期函数中作为参数出现，例如列值改变前触发 dataSetFieldPosting、存盘后触发 dataSetSaved

代表触发该事件的对象

| 方法 / 属性 | 功能 | 
| -- | -- |
| String | 列名 | 
| String | 列值 | 


### JSString

| 方法 / 属性 | 功能 | 
| -- | -- |
| boolean | 判断 s 是否在 strList 中 | 


## 业务层常用类

### VOUtils

snsoft.commons.dx

| 方法 / 属性 | 功能 | 
| -- | -- |
| static <T> T  | 获取指定对象指定属性的值 | 


### SpringBeanUtils

snsoft.commons.spring

| 方法 / 属性 | 功能 | 
| -- | -- |
| static <T> T  | 根据 Bean name 获取 Bean | 


### FTCommUtils

snsoft.ft.comm

| 方法 / 属性 | 功能 | 
| -- | -- |
| static  | 获取码表 / 字典某一列的值 | 
| static  | 获取实体类某字段的中文名 | 


### CodeDataUtils

snsoft.dx.codedata

| 方法 / 属性 | 功能 | 
| -- | -- |
| static  | 获取码表的编码对应名称 | 


### StrUtils

snsoft.commons.util

| 方法 / 属性 | 功能 | 
| -- | -- |
| static  | 将 list 的所有值，根据传入的分隔符进行连接 | 


### DateUtils

snsoft.commons.util.DateUtils日期工具类

| 方法 / 属性 | 功能 | 
| -- | -- |
| static  | 根据传入的值，转换成 Date | 


# 常用方法

当前常用方法是因为工具类只用到了一两个方法，当该类中用到的方法增多时，会挪到常用类中

## 监听层

| 方法 | 功能 | 
| -- | -- |
| void  | 批量设置界面中多列的属性 | 


# 常用注解

只记录公司内部注解，java 或 spring 原生注解不在此记录

| 注解 | 描述 | 
| -- | -- |
| @NotNull | 指定字段非空 | 
| @DefaultValue | 指定默认值 | 
| @CodeTable | 码表属性校验 | 
| String value | 码表 ID，或者字典 ID（例如：DT_FT-CODE.TradeType） |   | 
| String params | 码表过滤参数 | 
| @SqlColumn | 指定查询条件 | 
| int sqlop | SQL 表达式运算符，被修饰的参数采用指定逻辑运算符拼接到查询 SQL 中 |   | 


# 底层监听类

底层监听类，即 jar 包中已经写好的监听方法

只需要在界面定义 XML 中通过 #new 引入即可，不需要编译生成 JS

> 如果本地监听也定义了同名方法，则两个方法均会执行


## **tgtUINames 属性**

在监听类的父类：

例如某个界面 XML 中有多个 GridTable，只需要在主表中添加监听，并通过该属性指定要监听的子表 name 属性，即可

若未指定该属性，则默认加在主表中

## 监听表

| 监听类型 | 描述 | 举例 | 
| -- | -- | -- | -- |
| 状态改变监听类 | 类路径 | JS 监听：snsoft.plat.bas.busi.StatusJSListener |   | 
| 功能 | 实现对数据状态的操作，以及更新“修改人”和“修改时间”字段（需要这两个字段的 c 标签配置  | 
| 清空值监听类 | 类路径 | JS 监听：snsoft.plat.bas.comm.CleanValueJSListener |   | 
| 功能 | 设置某列值改变后，清空指定列的值 | 
| Grid 表列按钮监听 | 类路径 | JS 监听：snsoft.sheet.comm.OperateJSListener |   | 
| 功能 | 用于实现操作列功能 | 
| 页面标题条 JS 监听 | 类路径 | JS 监听：snsoft.plat.bas.busi.PageTitleBarJSListener |   | 
| 功能 | 在标题条上显示外码、状态 | 
| 展示立即刷新面板 | 类路径 | JS 监听：snsoft.plat.bas.busi.ShowRefreshPanelListener |   | 
| 功能 | 在查询面板中展示立即刷新面板 |   | 
| 码表浏览器不缓存 | 类路径 | JS 监听：snsoft.ft.set.def.bas.SetAidClearBufferedCliListener |   | 
| 功能 | 可以在不刷新界面的情况下，每次点击辅助输入按钮，都会获取最新的码表数据 |   | 
| 入口/单据内/生成下级拷贝按钮共用 JS 监听 | 类路径 | JS 监听：snsoft.plat.busi.datacopy.BusiSheetDataCopyListener |   | 
| 功能 | 指定界面生成“拷贝新建“按钮，并调用拷贝定义生成单据 |   | 
| 选择部门自动代入公司 JS 监听 | 类路径 | JS 监听：snsoft.ft.code.comm.SheetCorpBcodeJSListener |   | 
| 功能 | 在选择部门人员时，自动代入法人公司字段，一般在详情页会默认加入该监听 |   | 
| 批量修改监听 | 类路径 | JS 监听：snsoft.ft.comm.busi.FTBatchModifyJSListener |   | 
| 功能 | 可以对勾选行的指定列批量修改成指定值 |   | 
| 汇总子表字段到主表 | 类路径 | 服务查询监听：SN-PLAT.QR、snsoft.plat.busi.formula.service.impl.BusiStrcatListener |   | 
| 功能 | 对子表的某一列进行拼接，填充至主表某个字段内 | 
| Excel 导入 | 类路径 | JS 监听：snsoft.bastool.excel.ExcelStrategyImportListener |   | 
| 功能 |   |   | 


# 常用获取值

| 类 | 说明 | 方法 | 
| -- | -- | -- |
| snadk-utils 包 | 业务层获取系统选项 | String | 
| snadk-utils 包 | 获取当前登录的用户名 | getUserSession(true). | 
| 获取当前登录用户的商户 | getUserSession(true). | 
| ft-comm 包 | 获取内部客商 | Set<String> | 
| snsoft.plat.bas.bcode.service. | 获取记账部门 | String | 
| 获取公司对应的本位币种 | String | 
| 获取部门对应的法人公司 | String | 
| snsoft.ft.code.bcode.bcode.service. | 获取部门码表 |   | 
| snsoft.dx.data. | 获取下属单位 |   | 
| snsoft.commons.util.DateUtils | 获取服务器时间 | java.util.Date | 
