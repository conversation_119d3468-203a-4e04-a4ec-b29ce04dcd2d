UPDATE mysql.user SET authentication_string = PASSWORD('root123') WHERE user = 'root';
java.math.BigDecimal.ROUND_HALF_UP

java.math.BigDecimal.ROUND_HALF_UP

requestInfo = (snsoft.ui.impl.HttpRequestInfo) snsoft.context.AppContext.getUserSession();
requestInfo.getLoginHost();

snsoft.dx.DBUtils.getConfigDataSource(true)

DBUtils.getDataSource("BASDATA", true)
(1)付款申请单主表“来源单据类型”不等于“承兑赎单”时，通过“付款申请单号内码”+“单据类型”匹配【外汇交易复核入账工作台】〖业务信息〗子表“来源单据内码”+“来源单据类型”，取表头“交易汇率”字段值，多个逗号隔开；



(2)付款申请单主表“来源单据类型”等于“承兑赎单”时，通过付款申请单主表主表的“来源单据内码”+“来源单据类型”匹配【外汇交易复核入账工作台】〖业务信息〗子表“来源单据内码”+“来源单据类型”，取表头“交易汇率”字段值，多个逗号隔开。



取【应收票据池】同名字段值::首页 应收票据签收工作台  票据到期日


收付款方式:空的话赋值转账



取“实际收付日期”--款项调整记录取 调入日期



网银到账认领工作台： 都是取这里面的数据，如果来源类型是收款认领调整的，需要倒查对应单据的调入日期；如果不是的话，就取事务处理日期
主表ft_rec_bno：网银流水明细表      ft_rec_rclm：收款认领表

收款认领调整工作台



银行对账数据用于核对企业内部财务记录与银行账户的往来记录，主要目的是确保企业的银行记录（如现金账户）与银行提供的对账单信息一致。在交单融资以及赎单融资对应场景中，生成对应的银行对账数据并进行逆向处理。




