```java
//还款手续费
feefcy=rs.getValue(row,"feefcy")
//收款金额
recfcy=rs.getValue(row,"recfcy")
if(feefcy.compareTo(recfcy) <0)


//银行账号
bankaccount=m.recbankacccode
bankAccountService = getBean("FT-CODE.CorpBankAccountClientService")
accountComplementsVO=bankAccountService.queryCorpBankAccountComplements(java.util.Collections.singletonList(bankaccount)).get(0)
//银行
corpbankcode=accountComplementsVO.getCorpbankcode()
 
 
    
db = getDatabaseByTable(("ft_lrp_lrp"))
selectSql = "select fcy from ft_fee_bfee where rptype='S5003.001' and srcgicode='"+g.lrpicode+"'"
//还款手续费
bankfcy = db.query1(selectSql)
//原币金额-还款手续费
v=snsoft.commons.utilx.BigUtils.subtract(g.fcy,bankfcy)
db.close()    
    
    
    
println("=======")
db = getDatabaseByTable(("ft_docu_dlydg_cb"))
selectSql = "update ft_docu_dlydg_cb set  bcode='B00153',corpbcode='CCW00000442'"
queryResult = db.executeUpdate(selectSql)
println(queryResult)       
    
```

获取数据源

```
println(snsoft.dx.DBUtils.getDataSource("FT-FUND",true))
****************************************************************************
```

```
defClientService = getBean("FT-LCFG.RptypeDefClientService")
rptypeDefsBySysRptypes =defClientService.findRptypeDefsBySysRptypes(java.util.Collections.singleton("S5099"), "C000000003")
println(rptypeDefsBySysRptypes)
```

单据自动生效

```
snsoft.ft.bfc.comm.utils.BfcUtils.autoKeepByGenStatus("70","FT-TSO.TStockSalOut","6866018a9a8e40206a0183ee")
```



```
bankRecoClientService = getBean("FT-CODE.CorpBankAccountClientService")
bankRecoClientService.updateRecoInvalid(java.util.Collections.singletonList("67a572447e881a0090084d41"),"FT-LOAN.DocuDeliveryFinaApp")
```





```
消息通知管理里面拿到参数，重新推送消息执行！
msg = new snsoft.api.async.vo.AsyncSendMessage()
msg.setMessagetype("SNA-VM.VMark")
msg.setRequester("SNA-VM")
msg.setResponder("SNA-VM")
msg.setParameter("sysid", "FT-TSM")
msg.setParameter("cuicode", "C000000001")
msg.setParameter("usercode","C00000000100002")
msg.setParameter("wsid","00")
msg.setParameter("sheetcode", "FT-TSM.TStockBWcodeChange")
msg.setParameter("srcicode","67ea5b3595b27d5d62a06deb")
msg.setParameter("vmarkicodes","67ea5d52f3e0c96c5c534a0e")
snsoft.dx.async.util.AsyncUtils.sendMessage(msg)
```



```
单独校验执行代码逻辑
deliveryFinaAppService = getBean("FT-LOAN.DocuDeliveryFinaAppService")
deliveryFinaApp=deliveryFinaAppService.queryCascadeByInnerCode("67e2344c1f3e1a5dfa494eb5")
checkRes=deliveryFinaAppService.checkRiVmark(deliveryFinaApp)
println(checkRes)

deliveryFinaAppService = getBean("FT-LOAN.AccOrdFinaAppTask")
deliveryFinaAppService.process("","FT-LOAN.AccOrdFinaApp","6882f0b41923f664e8a184b4")
```



```



println("===交单初始化数据删除====")
db = getDatabaseByTable(("ft_docu_dlyd"))
executeSql = "delete from  ft_docu_dlydg where dlydicode in (select dlydicode FROM ft_docu_dlyd WHERE isinit='Y')"
executeResult = db.executeUpdate(executeSql)
executeSql = "delete from  ft_docu_dlyds where dlydicode in (select dlydicode FROM ft_docu_dlyd WHERE isinit='Y')"
executeResult = db.executeUpdate(executeSql)
executeSql = "delete from  ft_docu_dlydc where dlydicode in (select dlydicode FROM ft_docu_dlyd WHERE isinit='Y')"
executeResult = db.executeUpdate(executeSql)      
executeSql = "delete from  ft_docu_dlyd where isinit='Y'"
executeResult = db.executeUpdate(executeSql)

executeSql = "delete from  ft_docu_dlyd_st"
executeResult = db.executeUpdate(executeSql)
executeSql = "delete from mig_rela where  tgttbl ='ft_docu_dlyd' or tgttbl='ft_docu_dlydg'"
executeResult = db.executeUpdate(executeSql)


println("===交单融资初始化数据删除====")
db = getDatabaseByTable(("ft_loan_dlydfa"))
executeSql = "delete from ft_loan_dlydfag where dlydfaicode in (select dlydfaicode from ft_loan_dlydfa where isinit='Y')"
executeResult = db.executeUpdate(executeSql)
executeSql = "delete from ft_loan_dlydfa where isinit='Y'"
executeResult = db.executeUpdate(executeSql)
```



内部类声明

```
params = new snsoft.ft.docu.accord.service.MigDocuAccOrdCallBack$MigDocuAccOrdParam()
```



手动TAC 推送消息60task

```
println("开始执行")
dataSource = getDataSourceByTable("ft_docu_dlyd")
db = getDatabase(dataSource)
a=db.query2("SELECT t.dlydicode  FROM FT_FUND.ft_docu_dlyd t WHERE t.status = '60' AND t.ISINIT = 'Y'")
for i in a
    bean = getBean("SN-PLAT.PlatClientService")
	params = new snsoft.plat.sheet.ex.entity.SheetExchangeEntity("FT-DOCU.DocuDelivery",i,"60")
	bean.appendSheetExchange(params)
end for
println("完成")
```

