# 核心逻辑

## 表格 1

| 过程 | 主键 | 红冲核销码[r] | 原单据主键[o] | 红冲标识 | 说明 | 状态 |
| ---- | ---- | ------------- | ------------- | -------- | ---- | ---- |
| 原始 | D01  | D01           | D01           | 0        | 无   | 70   |

## 表格 2

| 过程 | 主键 | 红冲核销码[r] | 原单据主键[o] | 红冲标识 | 说明 | 状态 |
| ---- | ---- | ------------- | ------------- | -------- | ---- | ---- |
| 原始 | D01  | D01           | D01           | 1        | 取消 | 70   |
| 红票 | D02  | D01           | D01           | 2        |      | 70   |

## 表格 3

| 过程 | 主键 | 红冲核销码[r] | 原单据主键[o] | 红冲标识 | 说明 | 状态 |
| ---- | ---- | ------------- | ------------- | -------- | ---- | ---- |
| 原始 | D01  | D01           | D01           | 4        | 取消 | 70   |
| 红票 | D02  | D01           | D01           | 2        |      | 70   |
| 蓝票 | D03  | D01           | D01           | 0        |      | 10   |

## 表格 4

| 过程      | 主键 | 红冲核销码[r] | 原单据主键[o] | 红冲标识 | 说明 | 状态 |
| --------- | ---- | ------------- | ------------- | -------- | ---- | ---- |
| 原始      | D01  | D01           | D01           | 4        | 取消 | 70   |
| 红票      | D02  | D01           | D01           | 2        |      | 70   |
| 蓝票-原始 | D03  | D01           | D01           | 4        | 取消 | 70   |
| 蓝票-红冲 | D04  | D01           | D01           | 2        |      | 70   |
| 蓝票-蓝票 | D05  | D01           | D03           | 0        |      | 10   |

## 撤销检查

- 仅【红冲标识=4】(红蓝原始单据)可以进行撤销红冲；
- 如果存在蓝票且蓝票已经生效，则不允许撤销红冲；
- 如果接口检查不通过，则不允许撤销；

## 撤销逻辑
- 调用接口处理下游数据
- 删除原单据主键[o]=inner and redflag in(0,2)
- 修改当前单据(红冲标识=0)

## 注意

撤单检查，必须(主键=红冲核销码[r] and redflag=0)，否则不允许撤单。





# 红冲实操

> FH-250505009
>
> 红冲的单据都是redflag=2   ！！！
>
> r字段始终都是最早的那个单据的内码

```
select salshipicoder,
       salshipicode,
       salshipicodeo,
       redflag,
       status,
       salshipcode,
       salshipcoder
from ft_ssp_sp
where salshipicoder = '68187c9716dd1a0c4712a96d'
```

## 红冲按钮

- 红冲按钮：  生成的就是 1  2 ，  r  o字段都和原始单据的主键一模一样

| 过程 | 主键 | 红冲核销码[r] | 原单据主键[o] | 红冲标识 | 说明 | 状态 |
| ---- | ---- | ------------- | ------------- | -------- | ---- | ---- |
| 原始 | D01  | D01           | D01           | 1        | 取消 | 70   |
| 红票 | D02  | D01           | D01           | 2        |      | 70   |

## 红冲生成新单据

- **红冲生成新单据**    生成的redflag是4 2 0，  r  o字段都和原始单据的主键一模一样

| 过程 | 主键 | 红冲核销码[r] | 原单据主键[o] | 红冲标识 | 说明 | 状态 |
| ---- | ---- | ------------- | ------------- | -------- | ---- | ---- |
| 原始 | D01  | D01           | D01           | 1        | 取消 | 70   |
| 红票 | D02  | D01           | D01           | 2        |      | 70   |

## 基于蓝单再次生效后红冲生成新单据

- 再次基于生成的蓝单提交生效后再次点击：红冲生成新单据，原来的redflag为0 的变成了4，另外的变成了2 0，主要区别就是这2个新生成的2 0 的o字段记录的是这个新生成的4的内码，其他不变



| 过程      | 主键 | 红冲核销码[r] | 原单据主键[o] | 红冲标识 | 说明 | 状态 |
| --------- | ---- | ------------- | ------------- | -------- | ---- | ---- |
| 原始      | D01  | D01           | D01           | 4        | 取消 | 70   |
| 红票      | D02  | D01           | D01           | 2        |      | 70   |
| 蓝票-原始 | D03  | D01           | D01           | 4        | 取消 | 70   |
| 蓝票-红冲 | D04  | D01           | D01           | 2        |      | 70   |
| 蓝票-蓝票 | D05  | D01           | D03           | 0        |      | 10   |

