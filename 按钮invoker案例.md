## confirmRequest

```
/*
	 * 缺省：0-- OK/Cancel
	 *   1: Yes/NO/cancel
	 *   2 : this.showConfirm -- 自定义对话框
	 *   
	 */
	public int type;
```



## 1、交单明细删除

不能直接在底层基础上直接写自己的invoker，需要覆盖底层的删除，目的是为了在底层按钮删除点击以后，弹出对话框，在对话框点击确定的时候再将参数传递到后端

```
	@Override
	public ConfirmRequest check(InvokerEvent event)
	{
		return new ConfirmRequest(0, getTitle(), ResBundle.getResVal("FT.00000221"), new FuncCall((js.IFunction_V1<InvokerEvent>) this::onOk, this, new Object[] { event }));
	}

	@Override
	public JSObject invoke(InvokerEvent event)
	{
		return $o("dlydgicodes", dlydgicodes);
	}

	//点击《确定》执行
	public void onOk(InvokerEvent event)
	{
		int[] rows = this.table.getSelectedRowNumbers();
		//可能没有勾选行，那就是删除当前光标行
		if (rows == null || rows.length == 0)
		{
			dlydgicodes = new String[] { this.dataSet.get("dlydgicode").toString() };
		}
		//说明勾选了多行，把勾选的多行内码传到后端
		String[] dlydgicodes = new String[rows.length];
		for (int i = 0; i < rows.length; i++)
		{
			int r = rows[i];
			dlydgicodes[i] = this.dataSet.getValue("dlydgicode", r);
		}
	}
```

## 2、获取银行估值表

点击按钮，弹出对应自定义界面，同时在自定义界面中点击确定按钮的时候做自己的逻辑处理

```
snsoft.ft.forex.bank.invoker.GetBankValuationInvoker#check

	@Override
	public ConfirmRequest check(InvokerEvent event)
	{
		return new ConfirmRequest(2, this.bindAsFunctionCall((js.IFunction_V2<FuncCall,InvokerEvent>) (FuncCall fcall, InvokerEvent ie) -> {
			FuncCall funcCall = this.bindAsFunctionCall((js.IFunction_V5<DialogPane,Button,String,FuncCall,InvokerEvent>) this::confirmDeal, new Object[] { fcall, ie });
			DialogPane dialogPane = UIUtil.loadDialog("FT-FOREX.ForexBvalEntryDlg");
			dialogPane.title = event.cfg.title;
			//在对话框的ok按钮上面，监听事件处理。
			dialogPane.addListener("onOk", funcCall);
			dialogPane.showModal();
		}, new Object[] { event }));
	}
```

