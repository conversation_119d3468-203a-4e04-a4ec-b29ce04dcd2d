### 1、for 循环使用 try catch

>
> 其实就是看业务。我需要出现异常就终止循环的，就放外头；
>
> 不需要终止循环，就搞里头。--比如for循环尝试，睡眠一会

> 不要在循环中使用 try…catch…，应该把其放在最外层。除非不得已。如果毫无理由地这么写了，这种就算是垃圾代码范畴。

```java
		for (int i = 0; i < 3; i++) {
			try {
				receiptFile = downloadDepository(goodsReceiptVo);
			} catch (Exception e) {
				if (e.getMessage().contains("出证中")) {
					Thread.sleep(Long.parseLong(dictionary.getDictValue()));
					continue;
				}
			}
			if (receiptFile != null && receiptFile.getFileId() != null && receiptFile.getFileName() != null) {
				break;
			}
		}
```

**主要区别**

1. **异常处理范围**：
   - **循环内部的 `try-catch`**：每次迭代都可以独立处理异常。如果异常可恢复（例如，重试操作），可以继续下一次迭代。
   - **循环外部的 `try-catch`**：一旦抛出异常，整个循环都会中断，不会继续执行剩余的迭代。
2. **代码执行流**：
   - **循环内部的 `try-catch`**：允许在异常处理后继续循环的下一次迭代。
   - **循环外部的 `try-catch`**：异常会中断整个循环，除非异常处理代码显式地控制继续循环。

**选择依据**

- **需要重试机制**：如果需要在异常发生后继续重试（例如，网络请求、文件下载等），应将 `try-catch` 放在循环内部。
- **整体操作的原子性**：如果希望整个循环作为一个单元操作，要么全部成功要么全部失败，可以将 `try-catch` 放在循环外部。

在你提供的上下文中，显然需要在异常发生后继续重试，因此应将 `try-catch` 放在循环内部。这样可以在遇到 "出证中" 异常时休眠并继续重试，而不是中断整个循环。

### 2、idea  紧凑分包不展示.

tree appearance --> compact middle packages

### 3、Consumer 可以用方法引用创建

**consumer 参数允许你传入任何类型的任务处理逻辑**

```
TaskDisposeUtils.dispose(taskList, (task) -> disposeTask(task), executorService);
TaskDisposeUtils.dispose(taskList, TaskDisposeUtils::disposeTask, executorService);


 public static <T> void dispose(List<T> taskList, Consumer<? super T> consumer, Executor executor) {
 	//....
 	consumer.accept(item)
 }
```

### 4、线程设置

ThreadPoolExecutor 里面 corePoolSize、maximumPoolSize 以及队列 workQueue 的设置标准是什么呢，有没有可参考的

```
 public ThreadPoolExecutor(int corePoolSize,
                              int maximumPoolSize,
                              long keepAliveTime,
                              TimeUnit unit,
                              BlockingQueue<Runnable> workQueue,
                              RejectedExecutionHandler handler) 
```

这里设置为 0，是不让任务等待，线程池无法执行时，直接让当前线程执行

队列要看你愿意等多久以及每个任务处理时间





### 5、aop  filter 等

> AOP 也可以拦截包含 controller 的方法！ 拦截器不可以拦截 service 方法
>
> 比如操作日志：记录系统操作日志
>
> ```
> @Around("@annotation(operLog) && execution(* com.itsoku..*Controller.*(..))")
> public Object around(ProceedingJoinPoint joinPoint, OperLog operLog) throws Throwable {
> ```
>
> Filter--> 依赖于 servlet 容器    拦截器和 aop 依赖于 spring，



> - 过滤器，拦截器拦截的是 URL。AOP 拦截的是类的元数据(包、类、方法名、参数等)。 过滤器并没有定义业务用于执行逻辑前、后等，仅仅是请求到达就执行。 
> - 拦截器有三个方法，相对于过滤器更加细致，有被拦截逻辑执行前、后等。
> -  AOP 针对具体的代码，能够实现更加复杂的业务逻辑。 三者功能类似，但各有优势，从过滤器--》拦截器--》切面，拦截规则越来越细致。 
> - 执行顺序依次是过滤器、拦截器、切面。



**场景：**

在编写相对比较公用的代码时，优先考虑过滤器，然后是拦截器，最后是 aop。
比如：
权限校验，一般情况下，所有的请求都需要做登陆校验，此时就应该使用过滤器在最顶层做校验；日志记录，一般日志只会针对部分逻辑做日志记录，而且牵扯到业务逻辑完成前后的日志记录，因此使用过滤器不能细致地划分模块，此时应该考虑拦截器，然而拦截器也是依据 URL 做规则匹配，因此相对来说不够细致，因此我们会考虑到使用 AOP 实现，AOP 可以针对代码的方法级别做拦截，很适合日志功能。



### 6、主从、主备

主从或者主备  单纯只是备份的情况，并不实现读写分离的话，那代码就不需要实现数据源的切换，只有要实现读写分离--主库写从库读这种场景才需要代码支持！

### 7、Lock.lock()为什么在 try 之前执行？

> https://blog.csdn.net/E_N_T_J/article/details/105943325

```java
ReentrantLock lock = new ReentrantLock();
lock.lock(); // 加锁
try{
    // do something
}finally{
    lock.unlock(); // 解锁
}
```

为何加锁操作需要在 try 之前，能否在 try 块中执行呢？

不推荐，原因在于加锁操作可能会抛出异常，如果加锁操作在 try 块之前，那么出现异常时 try-finally 块不会执行，程序直接因为异常而中断执行；如果加锁操作在 try 块中，由于已经执行到了 try 块，那么 finally 在 try 中出现异常时仍然会执行，此时 try 中的加锁操作出现异常，finally 依然会执行解锁操作，而此时并没有获取到锁，执行解锁操作会抛出另外一个异常，虽然都是抛出异常结束，但是此时 finally 解锁抛出的异常信息会将加锁的异常信息覆盖，导致信息丢失。因此应该将加锁操作放在 try 块之前执行。
将加锁操作放在 try 块之前执行，可能有其它问题吗？

**看如下代码，如果在加锁之后执行一系列 predo 的操作，会出现什么问题呢？**

```java
ReentrantLock lock = new ReentrantLock();
lock.lock(); // 加锁
// predo something
try{
    // do something
}finally{
    lock.unlock();
}
```

如果在 predo 的操作中出现异常，程序会因为异常而终止，而由于并未执行 try 块，因此 finally 也不会执行，此时锁并没有释放掉，因此可能会出现死锁。所以，加锁之后直接执行 try 块，不要执行 predo 操作。

### 8、idea  结合 git bash 执行 shell 脚本

这样就可以 run 的时候自动打开 git bash hero 的效果，且自动执行里面的脚本

![image-20240617234040462](E:\Apple\TyporaMD\Img\image-20240617234040462.png)

### 9、idea  重构快捷键

- extracted: 提取为方法、常量、字段、变量、参数。
- change signature: 改变签名
- rename: 重命名

```java
Methods
Ctrl+Alt+M
    
Constants
Ctrl+Alt+C
    
Fields
Ctrl+Alt+F
    
Variables
Ctrl+Alt+V
    
Parameters
Ctrl+Alt+P
```



### 10、枚举和常量类的定义

> 接口常量，写起来方便，看着简洁，但是为了让其他人知道每个常量的含义，必须写注释，如果需要在用到常量对应的信息时候，需要看着注释来写。
>
> 如：在其他地方需要用到 SwingConstants.CENTER 的含义，必须看类里的注释，知道他表示中心。如果常量很多的话，把所有的常量都放在这一个接口里边，这种方式感觉也不是很友好。
>
> （**申哥**，瑞友的架构师，我的老乡、偶像）看了申哥以前的项目，发现一个常量类，也是接口常量类，**在接口里定义静态内部类**，但是比上边这种要好，他可以 **把不同的功能的常量类进一步分类**，看代码：

```java
public interface UtilConstants {
     
    /**
     * JSP路径
     */
    public static class JspFilePath {
        public static final String TESTCONTROLLER = "jsp/basic/"; 
        public static final String TEMPLATE_PAGEPATH = "basic/template/"; // 模板（测试）
    }
    
    /**
     * vo 对象的一些公共的属性名称 
     *
     */
    public static class VoFields {
        public static final String ACTIONTIME = "operateTime";//操作时间
        public static final String ACTIONUSERNAME = "operatorName";//操作人姓名
        public static final String CHECKTIME = "auditTime";//审核时间
  
    }
    
}
```

使用，把不同功能的常量放在了接口的内部类里，通过不同的内部类，调用的时候可以更明确好找吧。

```java
UtilConstants.JspFilePath.TEMPLATE_PAGEPATH
UtilConstants.VoFields.CHECKTIME
```

> 申哥的枚举：
>
> 实际项目中不会每个类型都弄一个枚举类，假设那样，系统中会有很多枚举类，每个类里边只有几个属性，那样类太多，感觉没必要，所以申哥设计了一个常量类 Constants，在常量类里定义了多个静态枚举类，这个和上边说的申哥在接口常量里定义静态内部类有点类似，不过现在换成了枚举。
>
> 接口里定义静态内部类好处：对不同功能的常量进行分类，用起来比较明确，但是由于是 interface，里边不能定义 static 静态代码块，所以没办法像类常量那样即定义常量状态码，又在 map 里放对应的说明信息，只能通过注释给开发人员用的时候看。
>
>  申哥的这种做法算是两全其美，在一个常量类里，定义多个静态的枚举，枚举类定义两个参数的构造器，一个相当于 key，一个是 value，属性也是 final 类型的，每个枚举对应实体对象的一种状态，甚好。

```java
public class Constants {
    public static enum OrderType {
		SALE("DDLY010301", "ERP"), DISTRIBUTION("DDLY0104", "分销平台");

		private OrderType(String value, String name) {
			this.value = value;
			this.name = name;
		}

		private final String value;
		private final String name;
		....
		
	}
    
    @Getter
	@AllArgsConstructor
	public static enum DeleteStatus {
		NORMAL(1, "正常"), DELETE(0, "删除");

		private final Integer value;
		private final String name;

	}
    
}
```

### 11、windows 凭据错误？ git 报错

> git Incorrect credentials: 401 Unauthorized, Please remove invalid credentials manually.
> 不正确的凭据: 401 未授权，请手动删除无效凭据。
>
> https://blog.csdn.net/lzllzllhl/article/details/110521161
> https://blog.csdn.net/m0_59680972/article/details/127081603

控制面板-> 用户账户-> 管理 windows 凭据

### 12、QQ 截图独立版本

> https://www.ghxi.com/qqscreenshot.html

```java
https://bbs.pediy.com/thread-271225.htm

https://www.feijix.com/s/hWFEm2P

已高速(如有密码：3519)http://ct.ghpym.com/d/7369060-48924959-054ec8

已高速https://www.123pan.com/s/HQeA-vt1Sh

https://pan.quark.cn/s/5207add194d7
```

### 13、full join 全连接   union-去重！

`oracle` 的全连接查询可以直接用 `full join`,

```
select * from a full join b on a.id = b.id
```

**mysql 的全连接**

`mysql` 中没有 `full join`, `mysql` 可以使用 `union` 实现全连接；

```

select * from a left join b on a.id = b.id
union
select * from a right join b on a.id = b.id
```

<img src="http://image.chn520.cn/2022-03-01/509ddcc6-69a7-478c-8bc9-b8423dc1c070" alt="在这里插入图片描述" style="zoom:67%;" />

可以发现 union  会默认去重，会筛选 A 表所有数据，关联上 B 表所有数据，对于 AB 两者共同的数据会去重，也就是中间部分就是共有的，不能关联两边

union all 则不去重

### 14、git 设置提交的 message 可以跳转链接

> Version Control-> Issue Navigation
> https://timewentby.com/git/gitlab-integrate-redmine-and-idea-intergate-redmine-issue-tracker.html

![image-20240626090844093](E:\Apple\TyporaMD\Img\image-20240626090844093.png)



### 15、idea 设置 XML Java 注释格式

> Editor-> Code Style-> Java      XML 同理

tab 选到 code  generation   ，把 comment code 里面的选项勾掉  

![image-20240626161551022](E:\Apple\TyporaMD\Img\image-20240626161551022.png)

### 16、idea 设置.editorconfig    代码标准规范

> https://juejin.cn/post/6844904142507343885
> 原来 .editorconfig 是可以设置换行符的。并且会覆盖了 idea 的设置。因为 .editorconfig 本身就是用于帮助开发人员在不同 IDE 下维护一样的样式格式。.editorconfig 控制换行符的方法：
>
>
> 工程放的位置：  可以和.gitignore 一起

```java
[*]
end_of_line = crlf
 
# [] 这个 section 可以指定所有文件，也可以某个后缀([*.json])，当然也可以指定某个文件([pom.xml])
```

另外也可以配置？

<img src="E:\Apple\TyporaMD\Img\image-20240627111904707.png" alt="image-20240627111904707" style="zoom:50%;" />

### 17、Jenkins 部署流程

```java
1-拉取代码	获取用户输	入的分支名	
2-拷贝依赖	node_modu	les依赖包	
3-编译代码	npm run	build xx	
4-备份代码	备份服务器代码	
5-上传代码	编译的代码	
6-更新代码  替换服务器代码
7-清理代码	清理备份的代码	
8-发送构建通知	 发送成功或者失败的邮件
```

### 18、延迟队列基于死信队列实现

> 死信队列
> 以下三种情况会消息会变成死信：
> ① 消息过期时间到了 – 消息过期了
> ② 队列满了
> ③ 消息被拒绝 – 即消费者调用 Basic.Reject/Basic.Nack
>
> 死信队列和普通队列基本是没有什么区别的。正常情况下，消息都是先到正常的队列里面，然后在满足上面三个条件的任意一个的情况后，就会进入到死信队列里面去。

**延迟队列**
基于上面的死信队列，其实还可以利用它的特性去实现一个延迟队列。

① 创建正常队列，队列设置过期时间分别为：5 秒、10 秒、30 秒、1 分钟四个队列。
② 为这四个正常队列绑定四个死信队列。
③ 消费端监听死信队列，就得到了相应的延迟消息。

比如 我希望这条消息延迟 5 秒发送，那么生产者就把消息发送到 5 秒的正常队列，时间到了后，rabbitmq 会将消息重新转发到死信队列，这样消费端就监听到了。

> 延时队列，从名字就可以看出，队列里面的消息会延时一会，也就是等一会才会被消费。这个功能非常常用，比如说最经典的就是电商中下订单后不支付。通常，我们会设定一个时间，比如 20 分钟内如果不支付，订单就自动取消。这个功能就可以通过延时队列来实现，下订单后，马上向延时队列发送一条消息，并且设置延迟时间为 20 分钟。然后等 20 分钟之后，消费者开始消费这条消息，可以简单的判断一下比如订单还是未支付状态，就把订单状态改为关闭的。

**RabbitMQ 延时队列**

这回轮到 RabbitMQ 没有了，是的，RabbitMQ 里面没有延时队列的实现。额，不是说 RabbitMQ 是一个标准队列嘛？注意，延时队列只是一个队列功能，和我们之前学过的持久化、确认、异常处理等功能相比，它确实是一个可有可无的功能。因此，并不是每个队列系统都一定要实现这个功能的。而且，我们可以利用各种逻辑业务的方式来实现，比如说在 RabbitMQ 中，最方便的实现延时队列的方式就是利用上节课我们学习过的死信队列。

还记得 **死信队列有一个条件就是超过消息的有效时间吧。利用这个有效时间，我们可以完全不写普通消费者，让消息全部等到有效时间后过期，然后让死信消费者成为延时队列消费者。**

我们之前演示的是在队列定义时设置队列的消息过期时间，如果只使用这种形式，那么整个队列中所有的消息过期时间都是一样的，这个明显不符合我们的需求。所幸，消息对象，也就是 AMQPMessage 对象的 `expiration` 属性，也可以设置一个过期时间。它和队列定义中的 `x-message-ttl` 一起存在的话，谁小就按谁先过期。



**延迟队列作为优先级队列的特例**

**排序依据**

- **优先级队列**：元素根据其优先级进行排序。优先级高的元素（通常是数值较小的优先级）会被优先处理。
- **延迟队列**：元素根据其延迟时间进行排序。延迟时间最短的元素（即最早到期的元素）会被优先处理。

**目的**

- **优先级队列**：主要目的是确保高优先级任务优先执行。
- **延迟队列**：主要目的是确保任务在指定的延迟时间之后才被执行

### 19、将一个集合转换为另外一个集合

```java
    public static <T, U> List<U> convertList(Collection<T> from, Function<T, U> func) {
        if (isEmpty(from)) {
            return new ArrayList<>();
        }
        return from.stream().map(func).collect(Collectors.toList());
    }
```

使用

```java
List<Msg<?>> msgList
//将消息转换为json格式
List<String> msgBodyJsonList = CollUtils.convertList(msgList, JSONUtil::toJsonStr);
```

```java
Object msgBody;
List<?> msgBodyList=Arrays.asList(msgBody)

List<Msg<?>> msgList=CollUtils.convertList(msgBodyList, body -> build(body))
@Override
public <T> Msg<T> build(T body) {
    return Msg.msg(this.mqProperties.getProducer(), IdUtil.fastSimpleUUID(), body);
}

```

### 20、MQ

#### send 方法流程

- 将消息转换为 json 格式
- 判断是否存在事务
  - 如果存在事务
    - 先将消息批量入库，返回 MsgPO 列表
    - 注册事务同步器，用于在事务完成后执行异步任务
  - 如果不存在事务
    - 直接将消息投递到 MQ
- 异步任务中调用 transactionAfter 方法，处理消息投递（即使失败了，会有补偿 JOB 进行重试）
  **代码走到这里时，事务已经完成了（可能是回滚了、或者是提交了），看下本地消息记录是否存在？**
    - 存在：说明事务是成功的，业务是执行成功的，则投递消息 & 并将消息状态置为成功
    - 不存在：说明事务执行失败了，消息未投递，由于插入消息和业务在一个事务中，事务执行失败，此时 db 中也是没有消息记录

### 21、OSS 防盗链

> 简单说就是设置了以后，只有白名单的可以访问，并且 Referer 为空的话  直接浏览器可以打开（因为此时没 Referer），并且 typora 也可以直接引用，但是从 localhost: 8080 引用这个图片是不行的，只有指定的可以！

为防止存储于阿里云对象存储 OSS 的数据被其他人盗链而产生额外的费用，需要在 OSS 控制台的防盗链功能设置 Referer 白名单，限制仅白名单中的域名可以访问 Bucket 内的资源。

### 22、  函数式接口抽取应用

**采用这样即可，有点类似于 aop？**
`tx(accountChange.getTxNo(), () -> accountInfoDao.updateAccountBalance(accountChange.getAccountNo(),accountChange.getAmount()));`

```java
trylock   finally  unlock
    
 begin transaction     commit transaction
    
   
```

methodInvocation.proceed();   //目标方法  就是类似  runabble.run

```java
@Override
public Object invoke(MethodInvocation methodInvocation) throws Throwable {
    //环绕通知
    //目标方法之前执行
    methodInvocation.proceed();   //目标方法

    //目标方法之后执行
    return resultVal;
}

```



```java
  //更新账户金额
    @Override
    @Transactional
    public void updateAccountBalance(AccountChangeEvent accountChange) {
        //幂等校验
        if(accountInfoDao.isExistTx(accountChange.getTxNo())>0){
            return ;
        }
        int i = accountInfoDao.updateAccountBalance(accountChange.getAccountNo(), accountChange.getAmount());
        //插入事务记录，用于幂等控制
        accountInfoDao.addTx(accountChange.getTxNo());
		//采用这样即可，有点类似于aop？
		//tx(accountChange.getTxNo(), () -> accountInfoDao.updateAccountBalance(accountChange.getAccountNo(),
				accountChange.getAmount()));
    }


	public void tx(String txNo, Runnable r){
		//幂等校验
		if(accountInfoDao.isExistTx(txNo)>0){
			return ;
		}
		r.run();
		accountInfoDao.addTx(txNo);
	}
```

### 23、RabbitMQ 和 RocketMQ 的自动 ack 机制不一样

**RabbitMQ：**

- **自动确认（Auto ACK）**：在启用自动确认模式（`autoAck=true`）时，RabbitMQ 会在消息传递给消费者的瞬间立即将其标记为已确认，这意味着即使消费者在处理消息时出现故障，消息也不会重新传递给其他消费者。
- **手动确认（Manual ACK）**：在手动确认模式（`autoAck=false`）下，消费者需要在成功处理完消息之后显式地确认消息（调用 `basicAck` 方法）。如果处理消息时出现故障，可以调用 `basicNack` 方法重新入队，从而保证消息不会丢失。

RocketMQ:

> 在 Apache RocketMQ 的自动确认（Auto Acknowledgement, Auto ACK）机制下，默认的行为并不是简单地在接收到消息后立即确认，而是在消息被成功消费后才确认。具体来说，RocketMQ 的 `DefaultMQPushConsumer` 在消费消息时会根据消费结果来决定是否确认消息。

- 自动确认（Auto ACK）: 自动确认是指消费者在消费消息后，自动向 RocketMQ 确认消息已经被成功处理。通常情况下，RocketMQ 的消费客户端（Consumer）默认使用自动确认机制。

- 手动确认（Manual ACK）: 手动确认是指消费者在成功处理消息后，显式地向 RocketMQ 确认消息已经被成功处理。在 RocketMQ 中，手动确认主要通过 `DefaultMQPullConsumer` 或者其他类似的机制来实现。



> - **RabbitMQ**：
>   - **默认使用自动确认**：消息一旦被消费者接收即被确认，不管是否处理成功（`autoAck=true`）。
>   - **显式手动确认**：需要设置 `autoAck` 为 `false` 并调用 `basicAck` 进行手动确认。
> - **RocketMQ**：
>   - **默认使用自动确认**：消息处理成功后自动确认，处理失败可以返回 `RECONSUME_LATER` 重新投递。
>   - **没有类似于 RabbitMQ 的 `autoAck` 参数**：但通过消费状态控制确认行为。

### 24、分库分表查询

**第四段：第一种解决方案 - 数据冗余**

- 推荐使用数据冗余的方式来提升性能。
- 需要注意数据修改后的一致性问题。
- 建议使用消息队列（MQ）来处理数据修改时的冗余数据同步。

**第五段：第二种解决方案 - 应用层查询**

- 通过应用层分别查询不同表的数据，然后进行汇总。
- 指出这种方法会增加应用层的复杂性，并且性能不佳。

**第六段：第三种解决方案 - 中间件支持**

- 利用中间件如 Sharding Sphere 来支持关联表查询。
- 通过配置关联规则，Sharding Sphere 可以自动处理多表关联查询。

**第七段：业务拆分和冗余数据建议**

- 建议按照业务进行数据库拆分，合理设计以减少跨库关联查询的需求。
- 即使出现跨库关联查询，推荐使用冗余数据方式。
- 如果性能要求不高，且使用 Sharding Sphere，可以直接使用其关联表功能。

**第八段：拆分的利弊和架构设计重要性**

- 拆分是一项对系统架构设计要求极高的工作。
- 拆分得当有利于系统分工和性能优化。
- 拆分不当可能增加系统复杂性和开发成本。

### 25、toMap 进行累加

> 默认就是 HashMap:: new ，可以不用写

```
Map<String, BigDecimal> offgicodeZadvFcyMap = advpayoffgdetails.stream()
       .collect(Collectors.toMap(
             advPayOffGDetail -> advPayOffGDetail.getOffgicode() == null ? "" : advPayOffGDetail.getOffgicode(), // 处理键为 null 的情况
             advPayOffGDetail -> advPayOffGDetail.getPayfcy() == null ? BigDecimal.ZERO : advPayOffGDetail.getPayfcy(), // 处理值为 null 的情况
             BigDecimal::add, // 合并函数，用于累加BigDecimal
             HashMap::new // 提供一个Map的Supplier
       ));
```



```java
Collectors.toMap(f -> f.name, Function.identity())
```

```java
	Map<String,AdvPayOffGDetail> lrpicodexAdvPayOffGMap = advpayoffgdetails.stream().collect(Collectors.toMap(item -> item.getLrpicodex() == null ? "" : item.getLrpicodex(), // 处理键为 null 的情况
				item -> item, // 处理值为 null 的情况
				(existingValue, newValue) -> existingValue));
```



### 26、java 8 分组获取最大值

> PayAppDetail 属性有 String paicode, Integer idx；
> 然后我有一个 List <PayAppDetail> allPayAppDetails; 我需要根据 paicode 分组，分组后对应每个 paicode 是最大的 idx，类似 Map <String,Integer> 这样的返回，key 就是 paicode，value 是 idx

```java
 // 分组并找到每组中最大的idx
        Map<String, Integer> result = allPayAppDetails.stream()
            .collect(Collectors.groupingBy(
                PayAppDetail::getPaicode,
                Collectors.collectingAndThen(
                    Collectors.maxBy(Comparator.comparingInt(PayAppDetail::getIdx)),
                    optional -> optional.map(PayAppDetail::getIdx).orElse(null)
                )
            ));

```

### 27、@Bean 方法自动注入对应的实例

> 通过@ Bean 注解，spring 会自动实例化注 RabbitTemplate 入到我这个方法吗？

```java
 @Bean
    @ConditionalOnMissingBean
    public IMsgSender msgSender(IMsgService msgService, MqSendRetry mqSendRetry, DistributeLock distributeLock, RabbitTemplate rabbitTemplate) {
        return new DefaultMsgSender(this.mqProperties,
                msgService,
                mqSendRetry,
                this.mqExecutor(),
                this.delayMsgProcessor(),
                this.delaySendRetryProcessor(),
                distributeLock,
                rabbitTemplate);
    }
```

如果没有显式声明 `RabbitTemplate` bean，而是依赖 Spring Boot 自动配置来创建它，那么 `RabbitTemplate` 仍然可以正常注入到你的 `@Bean` 方法中。

Spring Boot 提供了许多自动配置类，这些类会根据类路径中的库和应用程序的配置属性自动创建和配置 bean。例如，`RabbitTemplate` 是由 `RabbitAutoConfiguration` 自动配置类创建的，只要你在类路径中有相应的依赖（例如，spring-boot-starter-amqp），Spring Boot 会自动配置 `RabbitTemplate`。



> **假如没有   |**
>
> ```java
>   @Bean
>     public RabbitTemplate rabbitTemplate() {
>         // 配置 RabbitTemplate
>         return new RabbitTemplate();
>     }
> ```
>
> **可以正常注入吗**
>
> 总结来说，只要你使用了 Spring Boot 并且包含了 `spring-boot-starter-amqp` 依赖，`RabbitTemplate` 会被自动配置和创建，因此可以正常注入到你的 `@Bean` 方法中。

### 28、Redisson  /redisTemplate.opsForValue().setIfAbsent：setnx 原理

> redisson 分布式锁框架 底层采用的是不是 SETNX 命令
> 

```java
// 获取锁
RLock lock = redissonClient.getLock("myLock");

try {
    // 尝试加锁，等待最多 100 秒，上锁后保持 10 秒
    boolean isLocked = lock.tryLock(100, 10, TimeUnit.SECONDS);
    if (isLocked) {
```

是的，Redisson 分布式锁框架的底层确实使用了 Redis 的 `SETNX` 命令来实现分布式锁的功能，但不仅仅是简单的 `SETNX`，还结合了其他命令和机制来确保锁的正确性和可靠性。

具体来说，Redisson 分布式锁的实现过程如下：

1. **`SETNX` 命令**：首先使用 `SETNX` 命令（等价于 `setIfAbsent` 方法）来尝试设置一个键（代表锁），如果键不存在则设置成功，返回 `true`，否则返回 `false`。

2. **过期时间**：为了避免因程序崩溃或其他原因导致锁永远不被释放，Redisson 在设置锁的同时会设置一个过期时间。这样即使锁持有者没有正常释放锁，锁也会在超时后自动释放。设置过期时间通常使用 Redis 的 `SET` 命令和 `NX` 选项以及 `PX` 选项（以毫秒为单位设置过期时间），例如：

   ```
   shell
   复制代码
   SET myLock myValue NX PX 30000
   ```

3. **UUID 和线程 ID**：每个锁都会分配一个唯一的标识符（UUID）以及线程 ID，用来标识持有锁的客户端和线程。这样可以确保只有持有锁的客户端才能正确地释放锁。

4. **Lua 脚本**：为了确保操作的原子性，Redisson 使用 Lua 脚本来进行一系列的锁操作（如加锁和解锁）。Lua 脚本在 Redis 中是原子的，可以确保在执行脚本期间不会被其他命令打断。例如，释放锁时使用 Lua 脚本检查键的值是否等于锁持有者的 UUID 和线程 ID，如果是则删除键以释放锁。

**综上所述**

虽然 Redisson 的分布式锁框架底层确实依赖 `SETNX` 命令，但它结合了其他机制（如设置过期时间、使用唯一标识符和 Lua 脚本）来确保分布式锁的可靠性和正确性，从而在分布式环境中提供安全有效的锁管理功能。

> 我的 Rabbit 消费者代码如下，其中我用了 redisTemplate.opsForHash().entries("mail_log").containsKey(msgId)判断幂等性，如果生产部署集群的情况下，是否存在并发消费导致幂等性判断失效？ 

```java
String msgId = (String) headers.get("spring_returned_message_correlation");
if (redisTemplate.opsForHash().entries("mail_log").containsKey(msgId)) {
```

1. **Redis 操作的非原子性**：
   您的代码中，首先检查 `redisTemplate.opsForHash().entries("mail_log").containsKey(msgId)` 来判断 `msgId` 是否存在于 Redis 的 hash 中。如果此时多个实例同时接收到相同的消息（例如，由于网络延迟或消息重试），它们可能会同时进入这个检查。

   如果 Redis 的 `containsKey` 方法返回 `true`（意味着有另一个实例正在处理这个消息），但在检查与实际处理（即发送邮件和更新 Redis）之间，Redis 的 hash 中的该条目被删除了（由另一个实例完成处理并删除），那么当前实例可能会错误地认为这条消息还没有被处理过，并尝试再次处理它。

2. **Redis 命令的原子性**：
   更好的做法是使用一个 Redis 命令来同时完成检查（是否存在）和设置（例如，使用 `HSETNX` 或 `HSET` 配合 Lua 脚本来实现原子性操作）。这样可以确保如果 key 已经存在，则不会执行任何操作（或至少不会重复处理消息）。

> Boolean isFirstProcessed = redisTemplate.opsForValue().setIfAbsent("mail_log:" + msgId, "processed", 1, TimeUnit.DAYS);
>  是原子性的吗，多个实例同时执行会不会有并发问题

```java
// 使用 Redis 原子操作判断和设置幂等性
Boolean isFirstProcessed = redisTemplate.opsForValue().setIfAbsent("mail_log:" + msgId, "processed", 1, TimeUnit.DAYS);
if (isFirstProcessed == null || !isFirstProcessed) {
```

`redisTemplate.opsForValue().setIfAbsent("mail_log:" + msgId, "processed", 1, TimeUnit.DAYS);` 是 Redis 中一个原子操作，因此它本身是线程安全的，不会有并发问题。

具体来说，**`setIfAbsent` 方法（在 Redis 中是 `SETNX` 命令）会尝试设置键的值**，如果键不存在则设置成功并返回 `true`，否则返回 `false`。这个操作在 Redis 中是原子的，意味着 Redis 在执行这个操作时会保证没有其他操作会在这个过程中打断它。

因此，多个实例同时执行这段代码时，如果键 `"mail_log:" + msgId` 不存在，只有一个实例能够成功设置该键，其余实例会得到 `false` 作为返回值。这保证了在分布式环境下不会出现并发问题。





### 29、springboot 启动卡住没有任何日志

> - try catch 也没有任何异常
> - debug 也米有打在某个断点---通过 run 启动排除

日志配置问题导致

```java
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%thread] %d{HH:mm:ss} ：%msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.itsoku.lesson034.consume.TestConsumer" level="debug" />

    <root level="error">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>
```

### 30、消息排序逻辑

1. 消息标识

确保每条消息包含一个可以用来排序的标识，例如时间戳或序列号。

2. 使用有序数据结构

在消费者中使用一个有序数据结构（如 `PriorityQueue` 或 `TreeMap`）来存储消息。

3. 检查顺序并处理

每次从队列中消费消息时，检查是否是预期的顺序，如果是，则处理，否则等待或者重新排序。

```java

@Service
public class OrderedMessageConsumer {

    private final PriorityBlockingQueue<Message> messageQueue = new PriorityBlockingQueue<>();

    private int expectedSequence = 1;

    @RabbitListener(queues = "q_seq1")
    public void handleMessage(Message message) {
        messageQueue.offer(message);
        processMessages();
    }

    private void processMessages() {
        while (!messageQueue.isEmpty()) {
            Message message = messageQueue.peek();
            if (message.getSequence() == expectedSequence) {
                // Consume the message
                System.out.println("Processing message: " + message.getBody());
                messageQueue.poll();
                expectedSequence++;
            } else {
                break; // Wait for the correct sequence
            }
        }
    }

    // Message class with a sequence number
    public static class Message implements Comparable<Message> {
        private final int sequence;
        private final String body;

        public Message(int sequence, String body) {
            this.sequence = sequence;
            this.body = body;
        }

        public int getSequence() {
            return sequence;
        }

        public String getBody() {
            return body;
        }

        @Override
        public int compareTo(Message other) {
            return Integer.compare(this.sequence, other.sequence);
        }
    }
}
```

> expectedSequence  对应的就是消费位置
>
> 路人甲：
> 里面用 DB 当做队列存储消息，每次排序取出最小序列，
> 然后判断当前消费的位置是不是这个最小序列，而当前消费的位置也是每次存储在 DB 的，默认一开始是 0，消费以后就应该是 1 2 3 这样累加下去

### 31、打印对象的真实内存地址，不用 hashcode 和 tostring

```java
System.out.println("Object reference value: " + Integer.toHexString(System.identityHashCode(local.get())));
```

### 32、数据库日志和特性理解

```java
MySQL 中主要是通过 redo log 和 undo log 来控制事务，「redo log」 是在事务提交前回滚，保证事务的「持久性」， 「undo log」 是在事务提交后回滚，保证事务的「原子性


事务的四大特性（ACID）
「原子性（Atomicity）」：
事务中的所有操作要么全部成功，要么全部失败。即使在系统故障的情况下，事务也能保证不会只执行一部分操作。
「例子」：银行转账操作中，从一个账户扣钱并在另一个账户加钱，这两步操作要么都成功，要么都失败。
「一致性（Consistency）」：
事务执行前后，数据库都必须处于一致的状态。所有事务必须使数据库从一个一致状态变换到另一个一致状态。
「例子」：转账后，两个账户的总金额应该保持不变。
「隔离性（Isolation）」：
并发事务之间互不影响，一个事务的中间状态对其他事务不可见。不同事务之间的操作是相互独立的。
「例子」：同时进行的两个转账操作不会互相干扰，每个操作都看不到对方的中间状态。
「持久性（Durability）」：
一旦事务提交，其结果是永久性的，即使系统崩溃，事务的结果也不会丢失。
「例子」：转账成功后，系统崩溃重启，账户金额的变动依然存在
```

### 33、如何重写 equals 和 hashcode

> https://blog.csdn.net/neosmith/article/details/17068365

我们先定义一个程序员类(Coder):

```java
class Coder {
	private String name;
	private int age;
	
	// getters and setters

}
```

我们想要的是，如果 ２ 个程序员对象的 name 和 age 都是相同的，那么我们就认为这两个程序员是一个人 ． 这时候我们就要重写其 equals()方法 ． 因为 默认的 equals()实际是判断两个引用是否指向内在中的同一个对象，相当于 == ．　重写时要遵循以下三步：
1. 判断是否等于自身.

	```java
	if(other == this)
			return true;
	```
	
2. 使用 instanceof 运算符判断 other 是否为 Coder 类型的对象.

	```java
	if(!(other instanceof Coder))
			return false;
	```
	
3. 比较 Coder 类中你自定义的数据域，name 和 age，一个都不能少.

	```java
	Coder o = (Coder)other;
		return o.name.equals(name) && o.age == age;
	```
	
	

看到这有人可能会问，第 3 步中有一个强制转换，如果有人将一个 Integer 类的对象传到了这个 equals 中，那么会不会扔 ClassCastException 呢？这个担心其实是多余的 ． 因为我们在第二步中已经进行了 instanceof 的判断，如果 other 是非 Coder 对象，甚至 other 是个 null, 那么在这一步中都会直接返回 false, 从而后面的代码得不到执行的机会 ．
上面的三步也是 ＜Effective Java＞ 中推荐的步骤，基本可保证万无一失 ．

> **idea 里面的**

```java
@Override
public boolean equals(Object o) {
    if (this == o)
        return true;
    if (o == null || getClass() != o.getClass())
        return false;
    User user = (User) o;
    return userId == user.userId && userPoint == user.userPoint && Objects.equals(userName, user.userName);
}

@Override
public int hashCode() {
    return Objects.hash(userId, userName, userPoint);
}
```

### 34、泛型 PECS 原则

**PECS 原则总结**

从上述两方面的分析，总结 PECS 原则如下：

如果要从集合中读取类型 T 的数据，并且 **不能写入**，可以使用 ? extends 通配符；(Producer Extends)

如果要从集合中写入类型 T 的数据，并且 **不需要读取**，可以使用 ? super 通配符；(Consumer Super)

如果既要存又要取，那么就不要使用任何通配符。

### 35、aiXUEXI

主要看场景   现在可以找更多场景够用起来  比如考软考   就高了好几个智能体（概念通俗讲解、试题讲解、论文打分等）       写文章也搞了好几个 (起标题、文章润色、文章思维导图、文章概况、文章点评、文章配图等)

现在大多数人的问题
1 没有找到更多场景，没有真正”AI First“ 很多场景可以用但想不到用
2 提示词工程学的不咋地，又合适的场景不愿意写 不会写提示词

### 36、500w 分库

> https://juejin.cn/post/7196970992576872503
> https://tech.meituan.com/2016/11/18/dianping-order-db-sharding.html

1.1 天 500 万，那么 1 年就是 18 亿，我们按照 2 年的增量来算就是大概 40 亿

2.在预留一些空间，按照 50 亿来算

3.那么需要使用 32 个库，每个库 32 张表，那么一共就是 1024 张表，每个表存 500 万数据

4.使用 orderId(订单 id)当做分片键，使用一致性 hash 算法来分配，要插入到那个数据库的那个表中

5.当订单的数据超过了 2 年，那么使用冷热分离的方案或者使用 tidb 在线扩容数据库来解决

6.热库数据保留一年，1 年以后迁移到冷库，冷库保存 2 年，2 年后, 进行数据归档



**为什么要 2 的 n 次方**

> https://blog.csdn.net/lbh199466/article/details/115528420

1、定位速度更快

2、减少表扩容时影响的数据量



### 37、主从复制

> 刚写入主，从读不到怎么办呢
> 能有办法，让写入就立马读码
> 如何同步？实时性
>
> 同步复制？？
> 半同步复制 提供了比异步复制更高的实时性，但可能会影响写入性能。
> 读写分离 和 应用层处理 可以帮助优化读操作的实时性。
> 优化网络 和 调整复制延迟 可以减少主从之间的延迟。
> GTID 复制 可以提高复制的一致性和管理便利性。

读不到，问问业务怎么办，业务可以接受，就没问题
1、业务不接受，你可以说服业务改方案，比如订单支付成功后，立即读取不到，可以先跳到一个成功页面，用户点击去看订单的时候，期间已经过了几秒了，数据已经同步过去了
2、强制读主库

只需要在代码上如果从库上没有，直接强制路由到主库，主库没有才真的没有





### 38、多线程事务

> 明白了，每个线程都用 sqlSession 手动控制事务，所有线程都执行成功了，再 commit，否则大家都 rollback

<img src="E:\Apple\TyporaMD\Img\794ce9365607d03482e3db4ccd9c89c.png" alt="794ce9365607d03482e3db4ccd9c89c" style="zoom: 67%;" />

### 39、idea view会显示省略

注意idea的view有时候看着会省略内容



### 40、java8 option  判空操作

```java
String lcregicode = docuDeliverygs.stream().filter(item -> StringUtils.isNotEmpty(item.getLcregicode())).findFirst().get().getLcregicode();


String lcregicode = docuDeliverygs.stream()
				.filter(item -> StringUtils.isNotEmpty(item.getLcregicode()))
				.findFirst()
				.map(DocuDeliveryg::getLcregicode)
				.orElse(null);
if (StringUtils.isEmpty(lcregicode))
{
    return result;
}

```

### 41、查看异常堆栈

> https://www.cnblogs.com/ismallboy/p/12779200.html

```java
public class TestEx {
    private void fun1() throws IOException {
        throw new IOException("level 1 exception");
    }
​
    private void fun2() throws IOException {
        try {
            fun1();
            System.out.println("2");
        } catch (IOException e) {
            throw new IOException("level 2 exception", e);
        }
    }
​
    private void fun3() {
        try {
            fun2();
            System.out.println("3");
        } catch (IOException e) {
            throw new RuntimeException("level 3 exception", e);
        }
    }
​
    public static void main(String[] args) {
        try {
            new TestEx().fun3();
            System.out.println("0");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```



```java
Connected to the target VM, address: '127.0.0.1:57353', transport: 'socket'
java.lang.RuntimeException: level 3 exception
  at person.ismallboy.console.TestEx.fun3(TestEx.java:24)
  at person.ismallboy.console.TestEx.main(TestEx.java:30)
Caused by: java.io.IOException: level 2 exception
  at person.ismallboy.console.TestEx.fun2(TestEx.java:15)
  at person.ismallboy.console.TestEx.fun3(TestEx.java:21)
  ... 1 more
Caused by: java.io.IOException: level 1 exception
  at person.ismallboy.console.TestEx.fun1(TestEx.java:7)
  at person.ismallboy.console.TestEx.fun2(TestEx.java:12)
  ... 2 more
Disconnected from the target VM, address: '127.0.0.1:57353', transport: 'socket'
```

### 42、idea 大小写警告inspection提示

inspections--->proofreading-->typo

<img src="E:\Apple\TyporaMD\Img\image-20241021*********.png" alt="image-20241021*********" style="zoom: 67%;" />

### 43、一个事务多个操作可见性问题

> DBUtils.trans里面都是事务操作，我在最后面的面的createVMark里面需要查询交单融资信息新插入的数据，能查询到这个插入的数据吗
> //生成凭证标记表
> this.createVMark(insertLenInfos);

```java
DBUtils.trans("ft_fee_bfee", db -> {
					//生成往来对象
					this.createLrpclient(genLrpclients);
					//生成银行费用
					DAO.newInstance(BankFee.class).save(bankFees);
					//更新交单融资信息
					deliveryFinaAppService.save(docuDeliveryFinaApp);
					//生成凭证标记表
					this.createVMark(insertLenInfos);
});

```

在这段代码中，`DBUtils.trans` 方法被用来执行一个事务。事务的特点是要么全部成功，要么全部失败，这通常被称为原子性（Atomicity）。这意味着在事务中的所有操作要么都被提交（Commit），要么在遇到错误时全部回滚（Rollback）。

在你的代码中，`DBUtils.trans` 方法包含了一系列数据库操作：

1. `createLrpclient(genLrpclients)`：生成往来对象。
2. `DAO.newInstance(BankFee.class).save(bankFees)`：生成银行费用。
3. `deliveryFinaAppService.save(docuDeliveryFinaApp)`：更新交单融资信息。
4. `this.createVMark(insertLenInfos)`：生成凭证标记表。

由于这些操作都在同一个事务中，当你执行到 `createVMark` 方法时，之前的数据库操作（如更新交单融资信息）已经被提交到数据库中（在事务的上下文中）。因此，你可以在 `createVMark` 方法中查询到之前插入的数据，因为这些数据在事务的上下文中是可见的。

但是，需要注意的是，这些数据对外部是不可见的，直到整个事务被提交。如果事务在 `createVMark` 方法执行后遇到错误并回滚，那么所有在事务中进行的更改（包括你在 `createVMark` 方法中可以查询到的数据）都将被撤销。

总结来说，是的，你可以在 `createVMark` 方法中查询到之前插入的数据，因为它们在事务的上下文中是可见的。

### 44、BeanUtils.copyProperties放在开头

```java
//需要放在开头，不然有些同名字段赋值后会被重新拷贝
BeanUtils.copyProperties(deliveryFinaLrpclient, currentBankFee);
```

### 45、代码优化for循环--Java8函数式+泛型

```java
下面这4个for循环能否抽象优化方法
	
	for (Lrpclient lrpclient : redLrpclients)
		{
			VMarkService.VMarkRedCommParam param = new VMarkService.VMarkRedCommParam();
			param.setSrcicodeo(lendInfo.getDlydfaliicode());
			param.setSrcicode(dlydfaliicode);
			param.setFlags(0);
			param.setSrcdatas(MapUtils.toMap(lrpclient.getLrpicoder(), lrpclient.getLrpicode()));
			params.add(param);
		}
		for (BankFee bankFee : bankFees)
		{
			VMarkService.VMarkRedCommParam param = new VMarkService.VMarkRedCommParam();
			param.setSrcicodeo(lendInfo.getDlydfaliicode());
			param.setSrcicode(dlydfaliicode);
			param.setFlags(0);
			param.setSrcdatas(MapUtils.toMap(bankFee.getBfeeicoder(), bankFee.getBfeeicode()));
			params.add(param);
		}
		
			for (Lrpclient lrpclient : lrpclients)
		{
			VMarkService.VMarkRedCommParam param = new VMarkService.VMarkRedCommParam();
			param.setSrcicodeo(repInfo.getDlydfariicode());
			param.setSrcicode(dlydfariicode);
			param.setFlags(0);
			param.setSrcdatas(MapUtils.toMap(lrpclient.getLrpicoder(), lrpclient.getLrpicode()));
			params.add(param);
		}
		for (BankFee bankFee : bankFees)
		{
			VMarkService.VMarkRedCommParam param = new VMarkService.VMarkRedCommParam();
			param.setSrcicodeo(repInfo.getDlydfariicode());
			param.setSrcicode(dlydfariicode);
			param.setFlags(0);
			param.setSrcdatas(MapUtils.toMap(bankFee.getBfeeicoder(), bankFee.getBfeeicode()));
			params.add(param);
		}
```

优化1--POE

```java
  // 定义一个通用方法，用于创建VMarkRedCommParam并添加到params中
    public static <T> void addParams(List<VMarkService.VMarkRedCommParam> params, List<T> items, String srcicodeo, String srcicode, Function<T, Map<String, String>> srcDataMapper) {
        for (T item : items) {
            VMarkService.VMarkRedCommParam param = new VMarkService.VMarkRedCommParam();
            param.setSrcicodeo(srcicodeo);
            param.setSrcicode(srcicode);
            param.setFlags(0);
            param.setSrcdatas(srcDataMapper.apply(item));
            params.add(param);
        }
        
    // 使用抽象方法进行处理
        addParams(params, redLrpclients, lendInfo.getDlydfaliicode(), "dlydfaliicode",
                lrpclient -> MapUtils.toMap(lrpclient.getLrpicoder(), lrpclient.getLrpicode()));

        addParams(params, bankFees, lendInfo.getDlydfaliicode(), "dlydfaliicode",
                bankFee -> MapUtils.toMap(bankFee.getBfeeicoder(), bankFee.getBfeeicode()));

        addParams(params, lrpclients, repInfo.getDlydfariicode(), "dlydfariicode",
                lrpclient -> MapUtils.toMap(lrpclient.getLrpicoder(), lrpclient.getLrpicode()));

        addParams(params, bankFees, repInfo.getDlydfariicode(), "dlydfariicode",
                bankFee -> MapUtils.toMap(bankFee.getBfeeicoder(), bankFee.getBfeeicode()));       
```

优化2---轻雪  deep代码

```java
  private void addVMarkRedCommParams(List<?> items, String srcicodeo, String srcicode, List<VMarkRedCommParam> params, Function<Object, Map<String, String>> mapFunction) {
        items.forEach(item -> {
            VMarkRedCommParam param = new VMarkRedCommParam();
            param.setSrcicodeo(srcicodeo);
            param.setSrcicode(srcicode);
            param.setFlags(0);
            param.setSrcdatas(mapFunction.apply(item));
            params.add(param);
        });
    }

    public void processParams(List<Lrpclient> redLrpclients, List<BankFee> bankFees, List<Lrpclient> lrpclients, LendInfo lendInfo, RepInfo repInfo, String dlydfaliicode, String dlydfariicode, List<VMarkRedCommParam> params) {
        addVMarkRedCommParams(redLrpclients, lendInfo.getDlydfaliicode(), dlydfaliicode, params, item -> MapUtils.toMap(((Lrpclient) item).getLrpicoder(), ((Lrpclient) item).getLrpicode()));
        addVMarkRedCommParams(bankFees, lendInfo.getDlydfaliicode(), dlydfaliicode, params, item -> MapUtils.toMap(((BankFee) item).getBfeeicoder(), ((BankFee) item).getBfeeicode()));
        addVMarkRedCommParams(lrpclients, repInfo.getDlydfariicode(), dlydfariicode, params, item -> MapUtils.toMap(((Lrpclient) item).getLrpicoder(), ((Lrpclient) item).getLrpicode()));
        addVMarkRedCommParams(bankFees, repInfo.getDlydfariicode(), dlydfariicode, params, item -> MapUtils.toMap(((BankFee) item).getBfeeicoder(), ((BankFee) item).getBfeeicode()));
    }
```

### 46、系统设计

> https://mp.weixin.qq.com/s/dijoCnUR_whxFda0Ac_V4A

```java
从前端代码设计、前后端交互设计、传输协议优化、后端网关层优化，api服务层优化，业务逻辑代码层优化，数据库库优化，中间件优化，运维层面的优化，整体设计优化。 要从这些维度谈
```

### 47、Java8  函数式接口传参策略

这里只根据srcicode查出来所有的，但是我是要根据往来信息过滤（议付交单内码不为空）的才要红冲

```java
lrpClientService.redLrp(cancelInfo.getDlydicode(), docuDelivery.getSheetcode(), cancelInfo.getDlydicode(), docuDelivery.getSheetcode(), lrpclients -> {
			// 过滤出议付交单内码不为空的往来进行红冲
			return lrpclients.stream().filter(lrpclient -> StringUtils.isNotBlank(lrpclient.getDlydicode())).toList();
		});
```





```java
	public List<Lrpclient> redLrp(String upicode, String upsheetcode, String redUpicode, String redUpcode, Function<List<Lrpclient>,List<Lrpclient>> function)
	{
		List<Lrpclient> lrpClientList = getRedLrp(upicode, upsheetcode, redUpicode, redUpcode, function);
		if (lrpClientList != null && lrpClientList.size() > 0)
		{
			defaultValueService.setDefaultValues(lrpClientList, false);
			insertLrpclients(lrpClientList);
		}
		return lrpClientList;
	}

public List<Lrpclient> getRedLrp(String upicode, String upsheetcode, String redUpicode, String redUpcode, Function<List<Lrpclient>,List<Lrpclient>> function)
	{
		List<Lrpclient> lrpClientList = findLrpByUpicode(upicode, upsheetcode);
		if (function != null)
		{
			lrpClientList = function.apply(lrpClientList);
		}
		if (lrpClientList != null && lrpClientList.size() > 0)
		{
			lrpClientList.forEach(lrpclient -> {
			.......
		}
		return lrpClientList;
	}
```

### 48、@Builder默认就是创建对象

A.builder().build()就是默认创建对象了。但是没有赋值任何字段

```java
@Test
public void test(){
    A a = A.builder().build();
    //此时a对象已经new出来了
    System.out.println(a);
    a.setName("bbb");
}

@Builder
@Data
static  class  A{
    private String name;
}
```

### 49、幂等+并发---超卖

幂等是指多次执行某个操作，结果都是一样的，一般是通过判断是否执行过此操作，同时会搭配

```java
   public IdempotentCallResponse<O> call(IdempotentCallRequest<I> request) {
        logger.info("request:{}", JSONUtils.toJsonStr(request));

        //1.插入幂等调用调用记日志
        IdempotentCallPO idempotentCallPO = this.getIdempotentCallPO(request);

        //2.判断记录状态（如果是 成功 || 失败，直接从json反序列化结果返回）
        if (IdempotentCallStatusEnum.success.getValue().equals(idempotentCallPO.getStatus()) ||
                IdempotentCallStatusEnum.fail.getValue().equals(idempotentCallPO.getStatus())) {
            String responseJson = idempotentCallPO.getResponseJson();
            return JSONUtils.toBean(responseJson, this.responseType(), true);
        }

        //3、执行业务
        IdempotentCallResponse<O> response = this.disposeBus(idempotentCallPO, request);
        return response;
    }


 private IdempotentCallPO getIdempotentCallPO(IdempotentCallRequest<I> request) {
        LambdaQueryWrapper<IdempotentCallPO> queryWrapper = Wrappers.lambdaQuery(IdempotentCallPO.class)
                .eq(IdempotentCallPO::getRequestId, request.getRequestId());
        IdempotentCallPO idempotentCallPO = this.idempotentCallMapper.findOne(queryWrapper);
        if (idempotentCallPO == null) {
            idempotentCallPO = this.insertIdempotentCallPO(request);
        }
        return idempotentCallPO;
    }

    /**
     * 处理本地业务，由子类实现
     *
     * @param request
     * @return
     */
    protected abstract IdempotentCallResponse<O> disposeLocalBus(IdempotentCallRequest<I> request);

  protected IdempotentCallResponse<O> disposeBus(IdempotentCallPO idempotentCallPO, IdempotentCallRequest<I> request) {
        boolean transaction = this.isTransaction();
        if (!transaction) {
            //若没有事务，则会根据disposeLocalBus的返回值来确定业务是否执行成功，且 disposeLocalBus 内部需要自己确保幂等性
            IdempotentCallResponse<O> response = this.disposeLocalBus(request);
            //更新调用记录状态
            this.updateIdempotentCallPOStatus(idempotentCallPO, response);
            return response;
        } else {
            //有事务的情况下，下面会将业务方法 disposeLocalBus 放在spring事务中执行，disposeLocalBus 方法没有异常，则表示业务成功
            try {
                return this.transactionTemplate.execute(action -> {
                    IdempotentCallResponse<O> response = this.disposeLocalBus(request);
                    //没有异常，则将调用记录状态置为成功，并将结果序列化为json存储到表中
                    response.setStatus(IdempotentCallStatusEnum.success.getValue());
                    //将状态置为成功（这里面会有乐观锁[IdempotentCallPO有个version字段]作为条件更新，并发的时候也只有一个会成功）
                    this.updateIdempotentCallPOStatus(idempotentCallPO, response);
                    return response;
                });
            } catch (BusinessException e) {
                logger.error(e.getMessage(), e);
                //有异常，则将调用记录状态置为失败，并将结果序列化为json存储到表中
                IdempotentCallResponse<O> response = IdempotentCallResponse.fail(e.getCode(), e.getMessage());
                this.updateIdempotentCallPOStatus(idempotentCallPO, response);
                return response;
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                //有异常，则将调用记录状态置为失败，并将结果序列化为json存储到表中
                IdempotentCallResponse<O> response = IdempotentCallResponse.fail(e.getMessage());
                this.updateIdempotentCallPOStatus(idempotentCallPO, response);
                return response;
            }
        }
    }
```



这2种是通过先判断，然后插入数据库，插入完毕根据插入或者查询出来的数据，执行业务，执行业务完更新幂等表--乐观锁

```java
  default <T> T exec(Class<?> po, String id, Supplier<T> callback) {
        return exec(String.format("%s:%s", po.getName(), id), callback, null, null);
    }

    public void placeOrder4() throws InterruptedException {
        //扣减库存的方法，返回值为1表示扣减库存成功，0表示失败
        Function<String, Integer> reduceStock = (String goodsId) -> {
            try {
                //使用 dbConcurrencySafe.exec 包住需要并发操作的数据，可以确保数据修改的安全性
                return this.dbConcurrencySafe.exec(GoodsPO.class, goodsId, () -> {
                    //1、根据商品id获商品
                    GoodsPO goodsPO = this.getById(goodsId);

                    //2、判断库存是否够
                    if (goodsPO.getNum() == 0) {
                        return 0;
                    }
                    //3、执行更新扣减库存
                    this.goodsMapper.placeOrder3(goodsId, 1);
                    return 1;
                });
            } catch (ConcurrencyFailException e) {
                return 0;
            } catch (Exception e) {
                return 0;
            }
        };
        //模拟100人秒杀
        this.concurrentPlaceOrderMock("方案4", reduceStock);
    }



   public <T> T exec(String key, Supplier<T> callback, Consumer<T> successCallBack, Consumer<ConcurrencyFailException> failCallBack) {
        return transactionTemplate.execute(status -> {
            //1、获取 ConcurrencySafePO
            ConcurrencySafePO po = this.getAndCreate(key);

            //2、执行业务操作
            T result = callback.get();

            //3、乐观锁更新 ConcurrencySafePO
            int updateCount = this.concurrencySafeMapper.optimisticUpdate(po);

            //成功执行回调
            if (updateCount == 1 && successCallBack != null) {
                successCallBack.accept(result);
            }
            //updateCount==0，说明这个期间，数据被人修改了
            if (updateCount == 0) {
                //失败，创建一个异常
                ConcurrencyFailException concurrencyFailException = new ConcurrencyFailException(key, "并发修改失败!");

                //若调用方传入了失败回调的函数failCallBack，那么将执行回调
                if (failCallBack != null) {
                    failCallBack.accept(concurrencyFailException);
                } else {
                    //兜底，抛出异常，让事务回滚
                    throw concurrencyFailException;
                }
            }
            return result;
        });
    }
```

另外一种是先查询，查询不到就去执行业务，执行业务完毕插入幂等表（根据唯一索引）

```java
 public int idempotent(String idempotentKey, Runnable r) {
        //1.根据 idempotentKey 查找记录，如果能找到，说明业务已成功处理过
        IdempotentPO idempotentPO = this.getByIdempotentKey(idempotentKey);
        if (idempotentPO != null) {
            //已处理过返回-1
            return -1;
        }
        //这块一定要通过事务包裹起来
        this.transactionTemplate.executeWithoutResult(action -> {
            //2.执行业务
            r.run();

            /**
             * 3.向幂等表插入数据
             * 如果这个地方有并发，则由于（t_idempotent.idempotent_key）的唯一性，会导致有一个会执行失败，抛出异常，导致事务回滚
             */
            IdempotentPO po = new IdempotentPO();
            po.setId(IdUtil.fastSimpleUUID());
            po.setIdempotentKey(idempotentKey);
            this.idempotentMapper.insert(po);
        });
        //成功处理返回1
        return 1;
    }
```

不依赖数据库：一锁  二判 三更新

```java
redis加锁：
判断是否执行过，未执行过或者满足条件，
执行更新操作    
```



### 50、分库分表

> 1. 分库：适合写并发量比较大的场景；如果只是读的并发量比较大，可以采用一主多从策略（读写分离）
> 2. 分表：适合单表数量太大导致查询慢的场景
> 3. 分库分表：适合并发量大 + 单表数据量大的场景

**如何确定分库分表的数量？**

分表数量 = （年增长量 * 支撑年限）/ 2000万，结果向上取2的幂

分库数量 = 分表数量 / 8

比如每年增加1亿数据，要支撑10年

分表数量 = 1亿 * 10 / 2000万 = 50，向上取2的幂，也就是 64 张表

> 这里为什么要取2的幂呢，是为了方便扩容时数据迁移，这个后面的文章会进行介绍。

库数量 = 64/8 = 8

最终，8个库，每个库8张表。

### 51、select  for  update  加锁+事务

> 也就是说 采用for  update+同时开启事务的话，在事务提交之前，别的线程无法操作这条数据，会阻塞等待

```java
开启事务;
//从旧表读取订单记录，需要对旧的订单记录进行上锁，避免在同步当前记录的时候，旧数据被修改，导致同步到新表的数据和旧表不一致，这里使用for update对旧记录上锁，此时其他线程是无法修改当前旧的订单记录
Order oldOrder = select * from 旧订单表 where order_id = #{order.orderId} for update;

//订单记录可能在新表已经存在了，不管是否存在，都可以通过 delete + insert 解决
//先将订单从新表删除
delete from 新订单表 where order_id = #{log.orderId};
//将oldOrder写入新表
insert 新的订单表 values (订单信息oldOrder);

提交事务;
```

这里面有个关键点，大伙需要注意，将某一条旧的订单记录同步到新的表，有2个步骤

1. 第1步查询旧的记录
2. 第2步将旧的记录插入新表

这个过程中容易出问题：假如刚好第1步执行后，第2步还未执行时，旧表的数据被某个请求修改了，那么此时同步到新表的数据和旧表数据会出现不一致，为了避免这个问题，上面从旧表查询数据的时候，使用了`for update`对旧的记录进行加锁，可确保事务提交前被加锁(for update)的记录是无法被其他线程修改。



### 52、QPS真正理解--好好看

#### 100WQPS代表的是某个时刻，还是一段时间

100WQPS（QPS: Queries Per Second，每秒处理的请求数）通常代表系统在**某一时刻**的处理能力，而不是一段时间。具体来说，是指在**单位时间（1秒）内，系统能够处理的最大请求数**。

**概念解析：**

1. **单位时间的吞吐量（瞬时值）：**  
   QPS 表示系统在**指定时间内的请求处理能力**，通常是一个统计窗口中的瞬时值，反映系统的并发处理能力。因此**100WQPS描述的是每秒能够处理的请求量**，即当前系统在理想情况下每秒可以处理1,000,000次请求。

2. **负载统计或峰值压力：**  
   测试时，QPS 数据一般通过实时采集得出，用于衡量单独某时刻（或统计时间窗口内）的性能：
   - **瞬时峰值**代表系统瞬间的最大负载。
   - 多个时间点的QPS数据则用于描绘系统的状态变化，如吞吐量趋势图。

3. **区分不同时间段的小周期聚合值：**  
   如果给定的是时间窗口（例如每分钟、每小时的总请求数），该统计值通常计算平均QPS，而非直接代表真实的瞬时吞吐能力。例如，假设一分钟内有60,000,000次请求，通过汇总得到的 QPS 可以计算为：
   \[
   QPS = \frac{{60,000,000请求}}{{60秒}} = 1,000,000请求/秒（100WQPS）
   \]
   但在具体时间点（某一刻具体负载可能远超/低于此值），并非意味着每一瞬间均保持此负载。

---

**一些需要明确的问题：**

如果你在的场景下有某些不确定性，比如不清楚100WQPS描述是否涵盖整个时间区间，可以通过补充背景来进一步理解：
1. 这个100WQPS是静态标称值（理论系统上限）还是实际运行时测试值？  
   例如，系统通过性能测试得出某刻的系统极限吞吐能力是100WQPS。
   
2. 想讨论的是**瞬时吞吐能力**（某一秒性能表现）还是更关注某固定时长内的**平均表现**？  
   在性能评估中，通常关注峰值和平均QPS之间的差异。

---

如果你的需求是与具体的服务容量规划相关，记得结合你的场景，比如TPS（Transaction Per Second，事务处理能力）或延迟来进行更合理地考虑。如果需要进一步解释性能测试QPS的统计策略，我们可以深入探讨相关方法和工具。 😊

#### 10分钟内有600000请求，那么能说他的QPS=600000/600=1000QPS吗

是的，从**平均值**角度来计算，10分钟内有600,000个请求，总共持续时间为600秒（10分钟），你可以计算出它的**平均QPS = 600,000 / 600 = 1,000 QPS**。但需要注意的是，这个1,000 QPS是**全时间段内的平均值**，并不能直接代表某一刻所有时间的瞬时QPS，也不足以评估系统对于更高峰值负载的承受能力。

---

**平均QPS的计算意义**

这个结果表示在这10分钟时间内，不管负载是否平稳，系统平均每秒处理了1,000个请求。从性能评估的角度来看，这个数字说明了系统的整体吞吐量情况，但对于较大规模的服务，这种**平均指标可能会掩盖潜在的异常情况或者峰值压力对系统的影响**。

---

**实际场景需要重点关注的问题**

在性能测试或系统的容量规划中，仅仅关注平均QPS有时是不够的。对于更全面的性能分析，还需要关注以下指标：

1. **瞬时QPS（峰值QPS）：**

   - 瞬时QPS指某一个具体时刻系统经历的**最大请求处理能力**。在很多系统运行中，压力通常并不是分布均匀的，而是集中在某些瞬间。例如，有可能在10分钟的某一瞬间QPS达到了2000，而在其他时段可能低于1000，这时**平均QPS**会变得没那么真实。
   - 举例：
     - 一部分请求集中于前5分钟完成，峰值可能是2000 QPS。
     - 后5分钟请求数会变少，只有500 QPS。
     *平均值=1000 QPS，但离实际高峰相差较远*。

   如果这种情况在业务中经常出现，就需要评估系统是否能支持**某一时刻的高峰需求**。

2. **请求分布不均：**

   如果请求在10分钟内并不是线性分布，而是一部分时间突然蹿升、另一部分时间下降明显，系统可能会无法应对峰值负载。例如：
   - 10分钟有600,000请求，其中前1分钟消耗了300,000请求，则：
     \[
     瞬时QPS = \frac{{300,000}}{{60}} = 5000 QPS
     \]
     在这种情况下，如果系统只能支持1000 QPS，就会在第一分钟发生瓶颈或请求失败。

3. **系统能力设计的容错范围：**

   假如系统支持的理论最大QPS是1,000，而实际的**峰值QPS**达到2,000，就会造成**延迟增加、资源不足甚至无法正常响应**。因此，一个有容量规划的系统除了留有丰富的扩展性（比如过载保护、自动扩容），还得通过实时监控对瓶颈进行预测和处理。

4. **延迟（Latency）和吞吐量问题：**

   即使平均QPS在合理范围，如果单个请求的**延迟时间**波动较大，也会对用户体验造成严重影响。此外，也应该评估服务后台队列和峰值时的处理耗时，避免出现排队超时的问题。

---

**建议：**

1. 如果系统目标是均匀处理请求，平均QPS考量是有意义的。
2. 建议结合**峰值QPS**来看问题，通过性能监控工具获取系统实际瞬时QPS。（例如可以用 Prometheus 或 Apache JMeter 进行数据采样）。
3. 如果瓶颈在单个时间段（某些时间过高）的情况，需要对系统设计进行容量规划测试，添加限流措施或负载均衡。

**总结：**

简单来说，**平均QPS = 600,000 / 600 = 1000 QPS**这个算法是对的，但为了系统的准确评估，还需结合瞬时峰值与请求分布设计更加贴合业务实际的解决方案！

### 53、Redis的zset

zset(有序集合)是Redis中最常问的数据结构。它类似于Java语言中的**SortedSet和HashMap的结合体，它一方面通过set来保证内部value值的唯一性，另一方面通过value的score（权重）来进行排序。**这个排序的功能是通过Skip List（跳跃列表）来实现的。
 zset(有序集合)的最后一个元素value被移除后，数据结构被自动删除，内存被回收。\

<img src="https://p3-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/0a7823219b074b1f8f5bb549837b84a1~tplv-k3u1fbpfcp-zoom-in-crop-mark:1512:0:0:0.awebp" alt="zset的内部数据结构.png" style="zoom: 33%;" />

利用zset的去重和有序的效果可以由很多使用场景，举两个例子：

- 存储粉丝列表，value是粉丝的ID，score是关注时间戳，这样可以对粉丝关注进行排序
- 存储学生成绩，value使学生的ID，score是学生的成绩，这样可以对学生的成绩排名

### 54、一锁二判三更新---一锁不是必要的

第一步加锁为了解决高并发场景下的幂等问题，如果没有高并发， 不需要第一步了，就直接查询，更新，在更新的时候做乐观锁控制就行了

二判这个过程，如果有操作流水，建议基于操作流水做幂等，并将幂等号作为唯一性约束，确保唯一性。如果没有流水，那么基于状态机也是可以的。

但是不管怎么样，数据库的唯一性约束都要加好，这是系统的最后一道防线。万一前面的锁失效了，这里也能控制得住不会产生脏数据。

### 55、重复消费/下单、防止用户重复点击、幂等、库存扣减、超卖

> 理解一下这几个问题  是否有的表达不一样但解决方案是一个意思？？

重复消费、重复下单的问题，主要的解决办法就是做好幂等，因为在分布式系统中，我们是没办法保证消息不会重新投递的，也没办法保证用户一定不会快速的点击两次进行下单。

对于消息的重复消费问题，比较常见的解决方式，就是通过消息中定义的一个幂等号，来做防重判断。这个幂等号一般是约定好的一个业务字段，如果没有这样一个字段的话，也可以用消息中间件的msg_id来做幂等控制，但是可能存在一个情况，那就是发送者重复发送了多次消息，这就会导致几次消息的msg_id不一样，但是消息内容一致。所以，一般都需要在消息中约定一个唯一的幂等字段或者业务字段。

而对于重复下单的场景，这个幂等号应该怎么产生呢？有一个好的办法就是生成token，当用户每一次访问页面的时候，都向后端接口请求获取一个token，然后在之后本页面的操作中，都需要把这个token带过来。如果页面没有刷新，这个token应该是不变的。



### 56、库存扣减3个方案

> 库存扣减和重复下单也好像不太一样，
>
> 重复下单是说用户重复点击了按钮，这种通过token？   但是这个好像也能用库存扣减超卖的逻辑处理？？好像不能，比如提货委托重复审核，应该是通过判断是否已经审核过了来处理，而库存扣减超卖不是这个幂等判断！！！所以好像重复处理其实就是要幂等处理，库存扣减是是并发判断不能超过超卖
>
> 重复下单（面试鸭）：前端控制+token（无法避免，比如返回重新下单，api直接请求）+分布式锁（key:userid+标识，比如order？）
>
> 
>
> 库存扣减是保证不同用户下单的时候，库存不能超卖？

- 数据库扣减，count>0
- redis分布式锁，并发度下降
- redis扣减，redis可以保证按命令的顺序，lua保证原子性，

### 57、数据量大查询慢的几个方法

数据量一大，就会带来查询效率低的问题。

这时候就可以考虑要么就加缓存、要么就用ES、要么就做分库分表。还有就是做数据归档，把历史数据归档掉，无非就是这么几个方案了。

58、银行对账写入规则

```java
根据对账场景（可以再加上单据号）存储对应的实体VO，然后根据新增还是取消，决定setJavaFieldValue是null，还是传入的值
Map<String, Class> map = new HashMap<>();
map.put("DZ001",DocuDeliveryFinaAppLendInfo.class);
DAO dz001 = DAO.newInstance(map.get("DZ001"));
VO o = (VO) dz001.queryByID("1");
VOUtils.setJavaFieldValue(o,"","");
o.addStoredColumn("dz");
dz001.save(o);
```

### 59、git 拷贝提示  cannot update  ：

```
Cannot check the working tree for unmerged files because of an error. detected dubious ownership in repository at 'E:/IdeaPro/apple_test' 'E:/IdeaPro/apple_test' is owned by: 'S-1-5-21-4063752734-3873293201-174820708-1001' but the current user is: 'S-1-5-21-3430620260-1694606806-3851384136-500' To add an exception for this directory, call: git config --global --add safe.directory E:/IdeaPro/apple_test
```

使用通配符添加所有目录为安全目录

```
git config --global --add safe.directory "*"
```

### 60、SQL过滤的两种方式

```
select
```

### 61、熔断降级   功能降级

熔断是一种**快速失败**的机制，当某个服务的错误率或响应时间超过阈值时，熔断器会打开，后续的请求会直接失败，而不会继续调用该服务。熔断器的状态通常有三种：

1. **关闭（Closed）**：正常状态，请求可以调用服务。
2. **打开（Open）**：熔断状态，请求直接失败，不会调用服务。
3. **半开（Half-Open）**：尝试恢复状态，允许部分请求调用服务，如果成功则关闭熔断器，否则继续保持打开状态。

#### **适用场景**

- 当某个服务出现大量错误或响应时间过长时，防止雪崩效应。
- 保护系统不被故障服务拖垮。

```
// 使用 Hystrix 实现熔断
public class MyCommand extends HystrixCommand<String> {
    protected MyCommand() {
        super(HystrixCommandGroupKey.Factory.asKey("MyGroup"));
    }
    @Override
    protected String run() throws Exception {
        // 调用服务
        return callService();
    }
    @Override
    protected String getFallback() {
        // 熔断时的降级逻辑
        return "Service is unavailable";
    }
}

```

降级是一种**备用方案**机制，当某个服务不可用或响应时间过长时，系统会自动切换到备用逻辑，而不是直接失败。降级的目的是保证核心功能的可用性，即使某些非核心功能不可用。

#### **适用场景**

- 当某个服务不可用时，提供默认值或简化逻辑。
- 保证核心功能的可用性，牺牲部分非核心功能（比如双11关闭评论功能）。

```
// 使用 Sentinel 实现降级
@SentinelResource(value = "myResource", fallback = "fallbackMethod")
public String myMethod() {
    // 调用服务
    return callService();
}
public String fallbackMethod() {
    // 降级逻辑
    return "Service is degraded";
}

```

在实际应用中，熔断和降级通常结合使用：

1. 当某个服务出现故障时，熔断器会打开，请求直接失败。
2. 在熔断器打开期间，系统会调用降级逻辑，提供备用方案。
3. 当服务恢复时，熔断器会切换到半开状态，尝试恢复调用。



### 62、SQL  select子查询  利用

> 对于ft_loan_dlydfali表和主表ft_loan_dlydfa 关联，肯定只有一条数据，但是对于关联的ft_loan_dlydfag 可能会有多条，但是我只想取其中一条的这个值字段dlydfagicode，其他值一样，通过第一个sql，这样会关联多个值，解决方案：
>
> 1、group by  ft_loan_dlydfag  的dlydfaicode，其他字段需要max
>
> 2、通过子查询  (select max(c.dlydfagicode) from ft_loan_dlydfag c where c.dlydfaicode = a.dlydfaicode)

```
select *
from ft_loan_dlydfa a
         left join ft_loan_dlydfali b on a.dlydfaicode = b.dlydfaicode
         left join ft_loan_dlydfag c on c.dlydfaicode = a.dlydfaicode


select a.*, (select max(c.dlydfagicode) from ft_loan_dlydfag c where c.dlydfaicode = a.dlydfaicode)
from ft_loan_dlydfa a
         left join ft_loan_dlydfali b on a.dlydfaicode = b.dlydfaicode
```

### 63、SQL子查询关联  left join (select * from...)

```
SELECT
  q.*,
  w.upcode    AS upcode,
  w.paymode   AS paymode
FROM (
  -- 子查询 q，来源于主表 snds(ft_lrp_lrp) 的筛选与聚合
) q
LEFT JOIN (
  -- 子查询 w，通过三种不同表的数据 UNION ALL 得到付款方式与 upcode
) w
  ON w.rclmicode = q.rclmicode
WHERE
  snif('filter!=null','filter') = 0
  AND snif('paymodefilter!=null','paymode is not null') = 0;
```

w子查询可以是这样的

```
left join (SELECT *
                    FROM (SELECT '0005' paymode, m.rclmicode rclmicode, t.bankno upcode
                          FROM ft_rec_bno t,
                               ft_rec_rclm m
                          WHERE m.srcicode = t.bnoicode) g) w ON w.rclmicode = q.rclmicode
```

### 64、idea去掉自动优化包导入，避免git提交莫名引起包import变化

> https://blog.csdn.net/weixin_37539378/article/details/********

Settings→Editor→General→Auto Import 
然后去掉Optimize imports on the fly，保留Add unambiguous imports on the fly

```
Add unambiguous imports on the fly：快速添加明确的导入。
Optimize imports on the fly：快速优化导入，优化的意思即自动帮助删除无用的导入。
```

### 65、Jackson  打印json字符串忽略空值

```
ObjectMapper mapper = new ObjectMapper();
// 或者通过配置方式
mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

String jsonString = mapper.writeValueAsString(operations);
```

### 66、stream  的flatMap用法案例

```
results.getData().stream().map(item -> { String[] bcodes = item.getBcode().split(","); if (bcodes.length > 1) { return bcodes; } else { return new String[] { item.getBcode() }; }我要将这里面的bcode合并在一起到一个数组，现在是一个数组，每个数组元素里面又是一个数组

String []array=new String[]{new String[]{},new String[]{},new String[]{}}
实际需要的是String []array=new String[]{"","",""}
```

要合并这些嵌套数组为一个扁平数组，可以使用 flatMap 替代 map，或者在最后使用 flatMap 处理结果。

```
String[] bcodeArray = results.getData().stream()
				.flatMap(item -> {
					String[] bcodes = item.getBcode().split(",");
					return Arrays.stream(bcodes.length > 1 ? bcodes : new String[]{item.getBcode()});
				})
				.toArray(String[]::new);
```

### 67、如何将commit的但是还没push的git代码回滚

要回滚已经 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51) 但尚未 [push](file://D:\snsoftn10\CODE\snadk-prod\prod-src\cost\cost-client\src\main\java\snsoft\cost\service\CostCommClientService.java#L49-L49) 的代码，可以通过以下步骤实现：

方法一：使用 `git reset`

1. **软回滚（保留更改）**  
   如果想撤销 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51)，但保留更改的代码以便后续修改：
   ```bash
   git reset --soft HEAD~1
   ```

   - `HEAD~1` 表示回退到上一个 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51)。
   - 被撤销的 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51) 中的更改会保留在工作区，标记为已暂存状态。

2. **硬回滚（丢弃更改）**  
   如果想彻底撤销 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51) 并丢弃更改：
   
   ```bash
   git reset --hard HEAD~1
   ```
   
   - 此操作会删除指定的 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51) 和其对应的更改，请谨慎使用。

---

方法二：使用 `git revert`

如果希望保留 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51) 记录，但通过创建一个新的 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51) 来撤销更改：
```bash
git revert HEAD
```

- 这会在当前分支上生成一个新的 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51)，用于撤销最近一次 [commit](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\tx\TransactionManager.java#L51-L51) 的更改。
- 适用于需要保留完整提交历史的场景。

### 68、如何指定某个commit提交回滚

要回滚到某个 **指定的 commit**，可以使用 `git reset` 或 `git revert` 命令。以下是具体操作步骤：

---

方法一：使用 `git reset` 回滚到指定 commit

### 1. 获取 commit hash
首先，使用以下命令查看提交历史，找到你想回滚到的 commit 的 hash 值：
```bash
git log
```

输出示例：
```
commit abc1234567890def...
Author: ...
Date:   ...

    提交信息
```


### 2. 执行 reset 回滚
使用以下命令回滚到指定的 commit：
```bash
git reset --hard abc1234567890def...
```

> `abc1234...` 是你要回滚到的 commit hash。

- `--hard`：会丢弃当前 commit 之后的所有更改，工作区和暂存区都会被重置。
- 如果你只想保留更改，可以使用 `--soft` 或 `--mixed`（默认）：
  - `--soft`：保留更改在暂存区。
  - `--mixed`：保留更改在工作区。

---

方法二：使用 `git revert` 创建撤销提交

如果你不想修改提交历史，而是想创建一个新的提交来撤销某个 commit 的更改，可以使用：
```bash
git revert abc1234567890def...
```


- 会生成一个新的 commit，撤销指定 commit 的更改。
- 更适合用于已经 push 到远程仓库的 commit，避免强制重写历史带来的冲突。

---

方法三：丢弃某个 commit 的更改（但保留后续提交）

如果你想完全移除某个 commit 的更改，但保留其后的提交，可以使用 `git rebase -i`：

1. 执行交互式 rebase：
   ```bash
   git rebase -i abc1234567890def~1
   ```

   > `abc1234...~1` 表示该 commit 的前一个提交。

2. 在打开的编辑器中，找到你想移除的 commit，将 `pick` 改为 `drop` 或简写 [d](file://D:\snsoftn10\CODE\snadk-xjs\snadk-xjs\src\main\java\xjs\ui\util\SelectCodeDialog.java#L2309-L2309)：
   ```
   d abc1234 提交信息
   pick def4567 后续提交
   ```


3. 保存并退出，Git 会重新应用提交，跳过你标记为 `drop` 的 commit。

---

注意事项

- **未 push 的 commit**：可以安全使用 [reset](file://D:\snsoftn10\CODE\snadk-src\snadk-core\snadk-dx\src\main\java\snsoft\dx\mc\service\MakeCodeService.java#L47-L47) 或 `rebase`。
- **已 push 的 commit**：建议使用 `revert`，避免强制 push 导致协作冲突。
- **备份更改**：在执行 `reset --hard` 或 `rebase` 前，建议先保存或备份重要修改。

---

根据你的实际需求选择合适的方法即可。

### 69、如何让某个git管理的文件去掉管理不提交

要让 Git 停止跟踪某个已经被管理的文件，有几种方法。我来为您介绍最常用的方法：

**方法一：停止跟踪文件但保留在磁盘上**

使用以下命令可以让 Git 停止跟踪某个文件，但文件仍会保留在您的工作目录中：

```bash
git rm --cached <file>
```



例如：

```bash
git rm --cached config.properties
```

如果您想要停止跟踪整个目录：

```bash
git rm --cached -r <directory>
```

 **其他情况处理**

 **如果只想在本次提交中忽略文件**

如果您只想在当前提交中忽略文件的更改，可以使用：

```bash
git update-index --assume-unchanged <文件路径>
```

如果要恢复跟踪：

```bash
git update-index --no-assume-unchanged <文件路径>
```



**查看当前被忽略跟踪的文件**

查看当前设置了 assume-unchanged 的文件：

```bash
git ls-files -v | grep '^h'
```

 **总结**

最推荐的做法是使用 `git rm --cached` 命令配合 [.gitignore](file:///D:/snsoftn10/CODE/snadk-help/help-xjs/help-demo/node_modules/eslint-plugin-jsdoc/node_modules/semver/.gitignore) 文件，这样可以确保文件从版本控制中完全移除，并且不会被意外地再次添加进去。
