1、交单融资办理结果回传接口：
放款信息接口没那么多字段？  页面上字段还有很多部门  等等

其他栏位均取自往来对象表同名字段；   往来对象表的srcicode是融资主表内码还是放款信息表内码


单据测试：
穿透问题
字段、功能、原型  文档



---------------------
交单融资---提测------
融资银行：必录项，显示名，编辑禁止，选择有效的且“银行类型”为‘总行’的【外部银行】码表，缺省赋值为“交单银行/账号”对应的总行；

事务：按钮事务加上


外部银行对接
数据显示切换-议付交单，融资，融资处理工作台


议付交单测试   出仓单、量价单场景生成核销



Michel's Solarized Themes

Rider UI Theme Pack (0.15.0)
Selenized Theme (1.0.0)
Solarized Theme (3.0.0)
visual assist theme

----------------------------------------------
交单信用证往来：
dc=1
vdc=1
batype=A
rdate=预计收款日期
lrpdate=业务日期
sfcode=snsoft.ft.orgz.bcode.service.FTBcodeClientService#queryCorpById("").getSfcode()
tcaptime=业务日期
srcsheetcode=主表sheetcode
srcicode=主表内码
srccode=主表单号
fcy=【交单明细】中“交单金额”
fserate=取业务日期对应的汇率snsoft.ft.fund.util.FundUtils#getFrate  交单币种、本位币种
scerate=取业务日期对应的汇率snsoft.ft.fund.util.FundUtils#getFrate   本位币种、CNY
suerate=取业务日期对应的汇率snsoft.ft.fund.util.FundUtils#getFrate  本位币种、usd
scy=fcy*fserate
zcny=scy*scerate
zusd=scy*suerate
lcregicode/lcregcode/lccode=主表同名字段
dlydicode/dlydcode=取主表同名字段


List<Lrpclient> list; Lrpclient父类取主表ccode,子表同名拷贝


params=;
lrpclientList=list;
isCombine=true;
sysrptype="S0009"
snsoft.ft.lrp.service.impl.LrpClientServiceImpl#createLrp(params);



非信用证交单确认:
1、交单应收往来：取值同【交单信用证往来】
不同之处：
fserate=取出仓单汇率（TODO..）
scerate=取出仓单汇率（TODO..）
suerate=取出仓单汇率（TODO..）


2、出仓应收往来冲销：
dc=1
vdc=-1
batype=A
rdate=预计收款日期
lrpdate=业务日期
sfcode=snsoft.ft.orgz.bcode.service.FTBcodeClientService#queryCorpById("").getSfcode()
tcaptime=业务日期
srcsheetcode=主表sheetcode
srcicode=主表内码
srccode=主表单号
fcy=【交单明细】中“交单金额”
fserate=取出仓单汇率（TODO..）
scerate=取出仓单汇率（TODO..）
suerate=取出仓单汇率（TODO..）
scy=fcy*fserate
zcny=scy*scerate
zusd=scy*suerate
dlydicode/dlydcode=null



List<Lrpclient> list;

params=;
lrpclientList=list;
isCombine=true;
sysrptype="S0009"
snsoft.ft.lrp.service.impl.LrpClientServiceImpl#createLrp(params);

2600交单金额    1800  800


无追放款：   银行拿来的钱是实收
拿放款金额去遍历议付交单应收往来金额大于0数据
取小的值对比核销，直到放款金额全部核销完毕    此时是实收800（银行），赋值采用同名拷贝
如果是本位币金额，会有汇率问题?所以需要采用最后一行用减法

客户无追还款：银行放款时已经实收了部分金额，此时实收应该是剩下的钱！1800
根据议付交单内码查找议付交单应收往来余额>0数据
往来实收金额=客户还款金额-还贷金额
检查：往来实收金额要和查出来的应收往来余额金额一致。


此时查找出来应该有多条应收往来金额，比如总共是2600，实收掉了800，那应该还剩1800实收
但是这个1800实收可能对应的时多条应收往来去核销，所以此时生成的是多条对应的实收往来，总共=1800



有追放款：银行拿来的钱是应付往来
根据放款金额直接生成一条应付往来？不是，是遍历交单明细中的交单金额去对比核销，生成多条应付往来（银行），此时原来的应收往来金额还是原始值
赋值采用手动赋值


客户有追还款：还银行--实付往来，交单应收往来--赋值为实收
1、多条应收往来查出来，赋值为实收！
2、多条应付往来（银行），赋值为实付！


1、议付交单  信用证
2、信用证数据造单---认领单据内码
3、交单银行修改--杜哥实现
4、 srcgicode往来，以及交单明细红冲 ？   r字段处理

还是会报错 not find LrpCorBalance, lrpicodex=673dd4e76709696f68a2d2be


--合同造单
CQO1120001



---问题记录：
交单确认、再交单取消
然后再去交单确认、再去交单取消，此时红冲根据议付交单内码找得到2条或多条一样正数的内码


// 境内出仓单  需要注释掉代码--暂时测试使用
//  数据显示切换----金额累加有问题？？？  平行部门维度吧


-----------------------------------------
1、接口actualfcy金额调整
2、凭证标记表  sheetcode   补数