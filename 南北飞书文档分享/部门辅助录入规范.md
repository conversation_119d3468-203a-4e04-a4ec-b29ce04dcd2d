# 1、FT-ORGZ.BWcode 部门人员(产品标准)

（1）用于选择部门+人员

（2）适用场景：单据抬头、单据入口查询参数、有特殊要求的界面

（3）配置要求：编辑禁止、显示全名、漂浮显示、居右、辅助录入按钮显示（只读界面不需要）、配置部门+人员显示的renderer、配置拷贝字段wcode

（4）示例：

\<c name="bcode" title="${b\_wcode}" rdonly="true" sqltype="12" noblankOnSubmit="true" width="${D.HR.CW.bwcode}" codedata="#FT-ORGZ.BWcode" disableed="true" showfname="true" tipIfOverflow="true" uiprops.textDir="rtl" aidInputerBtn="true" cmparams.sheetcode="${sheetcode}" cmparams.opids="C" disableDelIfAI="true" submitOnInput="true" uiprops.renderer="new snsoft.plat.bas.comm.render.BusiBWcodeNameRender({})" aiprops="copyMap:{wcode:'wcode'}"/>



# 2、FT-ORGZ.Bcode部门(产品标准)

（1）用于选择部门

（2）适用场景：报表 、工作台、入口列表、有特殊要求的界面

（3）配置要求：编辑禁止、显示全名、漂浮显示、居右、辅助录入按钮显示（只读界面不需要）

（4）示例：

\<c name="bcode" title="${RES.C}" width="${E.G.CW.bcode}" sqltype="12" codedata="#FT-ORGZ.Bcode"  disableed="true"  showfname="true" tipIfOverflow="true" uiprops.textDir="rtl" aidInputerBtn="true"/>

# 3、FT-ORGZ.Bcode 做为事业部（推荐）

（1）用途：同上

（2）适用场景：同上

（3）配置要求：编辑禁止、显示名、漂浮显示、辅助录入按钮显示（只读界面不需要）、增加部门类型等于'事业部'

（4）示例：

\<c name="bcode" title="${RES.C}" width="${E.G.CW.bcode}" sqltype="12" codedata="#FT-ORGZ.Bcode"  disableed="true"  showname="true" tipIfOverflow="true" aidInputerBtn="true" cmparams="status:'\~10',depttype:'GLZZLX004'"/>

# 4、FT-ORGZ.BuBcode 事业部(产品标准)

（1）用于选择事业部

（2）适用场景：资金、财务等某些单据需要只选事业部

（3）配置要求：编辑禁止、显示名、漂浮显示、辅助录入按钮显示（只读界面不需要）

（4）示例：

\<c name="bcode" title="${RES.C}" width="${E.G.CW.bcode}" sqltype="12" codedata="#FT-ORGZ.BuBcode"  disableed="true"  showname="true" tipIfOverflow="true" aidInputerBtn="true"/>



# 5、FT-ORGZ.MerBWcode  部门或部门人员（财务专用）