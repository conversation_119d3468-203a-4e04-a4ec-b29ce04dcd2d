### 一、权限原理

![](E:\Apple\TyporaMD\Img\1753406120094-7.png)



![](E:\Apple\TyporaMD\Img\1753406123781-10.png)



![](E:\Apple\TyporaMD\Img\1753406126795-13.png)

### 二、权限配置说明

#### 权限定义

1. 操作码配置：R码一般需要配置plusop=”C”，表示包含C操作码，若还需包含其他操作码，用逗号分隔，例如：plusop=”C,V”。

2. asnt权限字段配置，存在组织\人员相关权限字段需要配置，通用配置方式如下：

<**fld fldname="astn" fldtitle="${astn}" fldtype="12" fldumatch="#SN-PLAT.BWcodeFieldMatcher" codedata="#SN-PLAT.RBWUType" options1="32" tcprops="{editable:false}"&#x20;**/>

其中fldumatch属性可以不配置，新版权限不再通过该方式匹配权限，配置了也无需更改。

* 组织类权限字段配置，例如：

<**fld fldname="corpbcode" fldtitle="${corpbcode}" fldtype="12" btype="02" fldopts="1" codedata="#FT-ORGZ.CorpBcode" options1="32" options2="8" options4="2"&#x20;**/>

其中特别注意，btype必须指定为当前字段对应组织类型。

#### **单据类型与单据数据权限关系**

1. 用于定义单据类型使用哪个权限定义来分配权限，若不指定默认使用和单据类型相同的权限定义

2. 可以第一单据类型对应多个权限定义，多个权限定义之间用或的方式拼接

3. 可以多个单据类型对应同一个权限定义，表示这些单据共用同一个权限

### 三、穿透定义

* 穿透定义tac中，会加assertExists("表名",主键)方法断言记录是否存在，该方法也会判断用户是否有该数据的R权限。

* 穿透打开目标单据时，用户需要目标单据的打开权限（例如：发货单单据内穿透打开销售合同，销售合同设置了打开详情界面需要R数据权限，所以用户需要有销售合同的R数据权限才能穿透到销售合同）。

* 单据入口打开按钮，可点击条件配置需要用户有当前单据的R权限。

### 四、按钮注册

* 按钮注册中按钮的点击、显隐一般都有权限控制

  - 有操作权限和数据权限之分

    * 操作权限是用户是否有配置单据的操作权限（btnOpids）,即有分配该操作权限就认为有权限；

  
    * 数据权限是判断当前数据有没有配置单据的操作权限(dataOpids)，除了需要分配该操作权限还要匹配到对应权限字段所配置的范围条件；
  

  - 按钮有引用按钮和自定义按钮之分，

    * 引用的按钮一般需要指定单据类型，一般为当前单据；
  
  
    * 自定义按钮需要界面属性控制配置，根据需求配置权限及其他控制（详情见界面属性控制）。
  

![](E:\Apple\TyporaMD\Img\1753406131395-16.png)

![](E:\Apple\TyporaMD\Img\1753406134250-19.png)



### 五、界面属性控制

1. 界面属性控制权限与按钮注册类似，需要指定sheetcode以及操作码，此处需要注意，操作码支持两种方式，

- dataOpids：通过数据匹配当前操作码权限，注意必须存在业务数据（一般当前行数据）

- btnOpids：无需通过数据，分配操作码即认为匹配权限


### 六、辅助录入配置

* 没有特殊说明，权限要求按如下处理（单据详情/工作台同理）：若当前字段是本单据的权限字段，则按本单据权限过滤，若不是则用当前字段对应的辅助录入对象的单据权限过滤（例如：发货单入口选择“仓库”，若仓库是本单据权限字段则按发货单的权限进行过滤，若不是则按【仓库】单据的权限进行过滤），配置方式如下：

**cmparams.sheetcode="${sheetcode}" cmparams.opids="R,C"**；sheetcode为当前单据sheetcode

* 若“部门”及“人员”为本单据权限字段，则只可选择本单据“部门”及“人员”权限范围内的部门人员（不区分有效期），否则只可选择当前登录用户【部门】单据权限范围内的部门及人员；

* 若“公司”为本单据权限字段，则只可选择本单据“公司”权限范围内的公司（只显示有效期是已失效、已生效状态的数据，若本单据部门、公司都为权限字段时，不要通过部门的权限绕本公司的权限），否则只可选择当前登录用户【公司】权限范围内的数据（只显示有效期是已失效、已生效状态的数据）；

* 单据内使用当前单据创建人过滤权限配置方式

  1. 客户端继承的父类(下拉、标准弹框)：

     FTLimitComboAidInputer

     FTLimitSelectCodeDialog

     码表配置：

     ui.cmprops.loadCommand="st-snsoft.ui.aid.service.SheetCodeDataValues.new"

  2. 客户端继承的父类(自己搭建界面)：

     FTLimitSelectTableDataDialog

     同时辅助录入界面查询需要的UIListener:

     SheetCodeDataUIListener



### 七、服务注解（入口、详情）

* 无特殊说明情况下，使用如下规则进行权限配置。若当前字段为本单据权限字段，则只可选择本单据权限范围内的数据，否则只可选择当前登录用户单据权限范围内的数据；

#### 单据入口：

1. **查询参数权限**

例如：

① bcode、wcode 为本单据的权限字段：单据号sheetcode="FT-PRJ.Business"，权限操作码cmparams.opids="R,C"

```
 <c name="bcode" title="${b_wcode}" sqltype="12" codedata="#FT-ORGZ.BWcode" cmparams="status:'~10'" disableed="true" showname="true" selectMiddle="true" aidInputerBtn="true" cmparams.sheetcode="FT-PRJ.Business" cmparams.opids="R,C" nmpre="filter" mutiple="true"/>
 
<c name="wcode" title="${RES.C}" sqltype="12" codedata="#FT-ORGZ.Wcode" cmparams="status:'~10'" disableed="true" showname="true" cmparams.sheetcode="FT-PRJ.Business" cmparams.opids="R,C" nmpre="filter" mutiple="true"/>
```

② corpbcode 非本单据的权限字段：单据号sheetcode使用码表FT-ORGZ.CorpBcode单据号，权限操作码cmparams.opids="R,C"

```
<c name="corpbcode" title="${RES.C}" sqltype="12" mutiple="true" codedata="#FT-ORGZ.CorpBcode" cmparams="status:'~10'" cmparams.opids="R,C" disableed="true" showname="true" selectMiddle="true" aidInputerBtn="true" nmpre="filter"/>
```



1. **数据查询服务权限**

一般只在UI端构建权限过滤条件，需要添加如下权限注解：snsoft.commons.util.service.AuthParam,方法名要使用“UI”结尾。如下：

| @AuthParam(sheetCode = Business.***SheetCode***, opids = { LimitConst.***Opid\_C***, LimitConst.***Opid\_R&#x20;***})&#xA;**public&#x20;**&#x51;ueryResults\<Business> queryEntryUI(BusinessEntryParams params)&#xA;{&#xA;    **return service**.queryEntry(params);&#xA;} |      |
| ------------------------------------------------------------ | ---- |
|                                                              |      |

```
权限注解参数：
1.        >sheetCode:定义单据编号
2.        >opids:定义权限操作
3.        >alias:表关联后，拼接权限过滤条件的表别名
```



#### 单据详情：

1. 辅助录入字段数据权限

例如：

* bcode、wcode 为本单据的权限字段：单据号sheetcode="FT-PRJ.Business"，权限操作码cmparams.opids="C"

```
<c name="bcode" title="${b_wcode}" sqltype="12" noblankOnSubmit="true" width="${D.HR.CW.bwcode}" codedata="#FT-ORGZ.BWcode" disableed="true" showfname="true" tipIfOverflow="true" uiprops.textDir="rtl" aidInputerBtn="true" cmparams.sheetcode="FT-PRJ.Business" cmparams.opids="C" disableDelIfAI="true" submitOnInput="true" uiprops.renderer="new snsoft.plat.bas.comm.render.BusiBWcodeNameRender({})" aiprops="copyMap:{wcode:'wcode'}"/>&#x20;
```

* corpbcode 非本单据的权限字段：单据号sheetcode使用码表FT-ORGZ.CorpBcode单据号，权限操作码cmparams.opids="R"，limflag : 1使用码表源单据号进行权限过滤

```
<c name="corpbcode" title="${RES.C}" sqltype="12" codedata="#FT-ORGZ.CorpBcode" width="200" showname="true" cmparams="ai_limflag:1" cmparams.opids="R,C" cmparams.status="70" cmprops.pmFromPane="{fromBcode:'ft_prj_prj.bcode'}" cmparams.fromBtype=""01"" noblankOnSubmit="true" disableDelIfAI="true" disableed="true" selectMiddle="false"/>
```

**&#x20;**

* 数据查询服务权限

一般只在UI端构建权限过滤条件，需要添加如下权限注解：snsoft.commons.util.service.AuthParam,方法名要使用“UI”结尾，查询时的权限操作码opids =可查询数据的操作码，如R,C，数据操作时权限操作码opids =可操作数据的操作码,如C。如下：

&#x20;

| @AuthParam(sheetCode = Business.***SheetCode***, opids = { LimitConst.***Opid\_C***, LimitConst.***Opid\_R&#x20;***}, justCheck = **true**, joinColumn = **"prjicode"**)&#xA;**public&#x20;**&#x51;ueryResults\<Business> queryBusinessUI(BusinessPageNavParams params)&#xA;{&#xA;    **return service**.queryBusiness(params);&#xA;} @AuthParam(sheetCode = Business.***SheetCode***, opids = { LimitConst.***Opid\_C&#x20;***})&#xA;**public&#x20;**&#x53;aveResults saveBusinessUI(SaveParams\<Business> params)&#xA;{&#xA;    **return service**.saveBusiness(params);&#xA;} |      |
| ------------------------------------------------------------ | ---- |
|                                                              |      |

```
1. 权限注解参数：
2.        >sheetCode:定义单据编号
3.        >opids:定义权限操作
4.        >joinColumn:查询子表时使用：使用主表内码字段进行权限的判断
5.        >alias:表关联后，拼接权限过滤条件的表别名
6.        >justCheck: 仅校验权限
```



* 为了支持审批人在不给分配查询权限的情况下也能够查询到单据详情，现将单据详情查询数据权限按如下方式配置

  

  ```
  1.  @AuthParam(sheetCode = CcodeApply.SheetCode, opids = { LimitConst.Opid_C, LimitConst.Opid_R, "M" },justCheck = true , joinColumn = "apicode")
  2.  其中：justCheck = true 表示不使用sql的where条件方式添加权限条件，而是采用hashDataLimit方式判断，一但发现无权限则报错；
  3.       joinColumn = "apicode" 表示当前单据主表内码，从QueryParams中获取对应的主表内码参数。
  ```

  

### 八、工作台

1. **查询参数权限：**

使用拷贝工作台**目标单据**的sheetcode，权限操作码为可操作数据的权限码（如：C）进行设置。

2. **通用数据拷贝工作台**

数据查询服务权限：

需要添加如下权限注解：snsoft.commons.util.service.AuthParam，sheetcode=目标单据sheetcode,opids = { LimitConst.Opid\_C }。

3. **贸易流程编排拷贝工作台**

不需要添加如下权限注解，流程编排自动进行权限条件注入。

### 九、查询

使用查询目标数据的sheetcode，权限操作码为可查询数据的权限码（如：R）进行设置。

数据查询服务权限：

| @AuthParam(sheetCode = **"RPT-TSP.TspRealTimeQuery"**, opids = { LimitConst.***Opid\_R***}, alias=**"t"**)&#xA;**public&#x20;**&#x51;ueryResults\<TspRealTimeQuery> queryEntryUI(TspRealTimeQueryParams params) {&#xA;    **return service**.queryEntry(params);&#xA;} |
| ------------------------------------------------------------ |
|                                                              |

```
1. 权限注解参数：
2.        >sheetCode:定义单据编号
3.        >opids:定义权限操作
4.        >alias:表关联后，拼接权限过滤条件的表别名
```



### 十、程序主动调用

1. 无特殊说明时，在数据交互过程中，几乎所有数据的读写及相关方法的访问都需要进行权限控制，总体分为对数据的读权限和写权限。

* 读权限：一般使用过滤条件对数据进行过滤（AuthInfoUtils.buildFilter）；

| AuthInfo authInfo = **new&#x20;**&#x41;uthInfo(CcodeApply.***SheetCode***, **new&#x20;**&#x53;tring\[] { LimitConst.***Opid\_C***, LimitConst.***Opid\_R&#x20;***});&#xA;AuthInfoUtils utils = **new&#x20;**&#x41;uthInfoUtils(authInfo);&#xA;SqlExpr sqlExpr = utils.buildFilter(); |
| ------------------------------------------------------------ |

* 写权限：一般使用对已知的数据进行判断是否有指定的操作权限（AuthInfoUtils.hasLimit）；

| AuthInfoUtils utils = **new&#x20;**&#x41;uthInfoUtils(**new&#x20;**&#x41;uthInfo(SysDictDef.***SheetCode***, **new&#x20;**&#x53;tring\[]{LimitConst.***Opid\_C***}));&#xA;**if**(!utils.hasLimit()){&#xA;    **return new&#x20;**&#x51;ueryResults<>();&#xA;} |
| ------------------------------------------------------------ |

2. 一般只在UI端调用权限判断及构建过滤条件，需要添加权限注解：snsoft.commons.util.service.AuthParam来判断权限，所有判断权限的地方首先判断是否超级管理员，超级管理员拥有所有权限；同理如果一个功能仅允许超级管理员使用，直接配置此注解，不需要指定sheetCode和opids即可。如下：

| @AuthParam(sheetCode = DomPurShip.*SheetCode*, opids = { LimitConst.*Opid\_R*,LimitConst.*Opid\_C&#x20;*})&#xA;public QueryResults\<DomPurShip> queryEntryUI(PurShipBasEntryParams params){&#xA;    return service.queryEntry(params);&#xA;} |      |
| ------------------------------------------------------------ | ---- |
|                                                              |      |

```
1. 注解@AuthParam配置方式，对于单据参数sheetCode = "FT-PSP.DomPurShip"，需要单据号和权限id对应，当通过单据号找不到权限id的时候，默认认为单据号=权限id查询权限。 参数opids = { "R", "C" }对应检查R查、C操作权限。
2. 权限注解参数：
3.        >sheetCode:定义单据编号
4.        >opids:定义权限操作
5.        >joinColumn:查询子表时使用：使用主表内码字段进行权限的判断
6.        >alias:表关联后，拼接权限过滤条件的表别名
7. wAdmin:wAdmin = 2 是 管理员用户可以使用该服务,wAdmin = 0|1超级管理员可以使用此服务
```



3. 可以根据部门人员构建对应用户权限，示例如下：

| **public&#x20;**&#x53;qlExpr buildLimit(String bcode, String wcode, String cuicode)&#xA;{&#xA;    String\[] opids = LimitConst.***A\_RC***;&#xA;    **if&#x20;**(bcode != **null&#x20;**&& wcode != **null**)&#xA;    {&#xA;       UCodeService service = UCodeServiceFactory.***impl***.getUCodeService(cuicode);&#xA;       Set\<String> gwcodes = service.getGWCodesByBcodeAndWcode(bcode, wcode);&#xA;       String usercode = service.getUserCodeByWCode(wcode);&#xA;       UserSession us = AppContext.*getUserSession*(**true**);&#xA;       LoginInfo loginInfo = **new&#x20;**&#x4C;oginInfo(us.getWorkspcID());&#xA;       loginInfo.**userCode&#x20;**= usercode;&#xA;       loginInfo.**userCuicode&#x20;**= cuicode;&#xA;       loginInfo.**userName&#x20;**= usercode;&#xA;       **if&#x20;**(us.isAdmin(1 \| 4))&#xA;       {&#xA;          loginInfo.**wAdmin&#x20;**= us.getWAdmin();&#xA;       }&#xA;       **try&#x20;**(ReplaceUserSession replace = UserSessionManager.***impl***.replaceUserSession(loginInfo))&#xA;       {&#xA;          *//按参数过滤权限*&#xA;*&#x20;        &#x20;*&#x41;uthInfoLoginInfo authLoginInfo = **new&#x20;**&#x41;uthInfoLoginInfo(loginInfo);&#xA;          authLoginInfo.setUserBcode(bcode);&#xA;          authLoginInfo.setUserGWCode(StrUtils.*join*(gwcodes, **","**));&#xA;          DataLimits.LimitParams limitParams = **new&#x20;**&#x44;ataLimits.LimitParams(authLoginInfo, **null**, **null**, **null**, opids);&#xA;          AuthInfoUtils utils = **new&#x20;**&#x41;uthInfoUtils(Scode.***SheetCode***, limitParams);&#xA;          **return&#x20;**&#x75;tils.buildFilter();&#xA;       }&#xA;    } **else**&#xA;**&#x20;  &#x20;**{&#xA;       AuthInfo authInfo = **new&#x20;**&#x41;uthInfo(Scode.***SheetCode***, opids);&#xA;       authInfo.setNeedColumns(**null**);*//运维部门使用，bcode， 其他组织使用 limbcode*&#xA;*&#x20;     &#x20;*&#x41;uthInfoUtils utils = **new&#x20;**&#x41;uthInfoUtils(authInfo);&#xA;       *//按当前登录用户过滤权限*&#xA;*&#x20;     &#x20;***return&#x20;**&#x75;tils.buildFilter();&#xA;    }&#xA;} |
| ------------------------------------------------------------ |

### 十一、Excel导入

1. 可以在Excel导入定义中配置权限操作码，用来判断当前用户是否有对应单据的导入操作权限，配置如下：

   ```
   ...
   <SheetCode>FT-DERIV.FuturesQuotes</SheetCode>
    <Optids>C</Optids> 
   ...
   ```

2. 代码中判断权限的代码如下：

```
if(def!=null&&StrUtils.isNotEmpty(def.getSheetcode())&&StrUtils.isNotEmpty(def.getOptids()))
{
AuthInfoUtils.assertHasLimit(def.getSheetcode(),StrUtils.splitString(def.getOptids(), ','), null);
}
```



***

### 十二、需排查权限配置

https://docs.qq.com/sheet/DUnRPSWtoY1NHVXRO?tab=aqvnun

1. 详情界面注解缺少joinColumn的服务（能审就能打开）

2. 存在特殊权限码(不在如下范围内即为常规权限码：C,R,B,P1,P2,P3,SL)

3. 没有关系类型的权限字段

4. 没有权限字段的单据

***

1、工作台要用下级单据的权限