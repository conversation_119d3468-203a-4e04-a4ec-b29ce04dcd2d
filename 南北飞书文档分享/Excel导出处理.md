1、界面小数点问题：

缺少VOListener,参见业务： xprops="excludeValids:'NotNull,NotEmpty'

若自己用非VO方式实现需自行处理小数位如：snsoft.ft.comm.ui.FTBusiUnionColumnUIListener

2、列增加了客户端计算，如：Render

\<c name="bcode" title="${RES.C}" sqltype="12" width="${E.G.CW.bcode}" codedata="#FT-ORGZ.BWcode" cmparams.sheetcode="FT-PRJ.Business" cmparams.opids="R,C" disableed="true" showfname="true" tipIfOverflow="true" uiprops.textDir="rtl" aidInputerBtn="true" uiprops.renderer="new snsoft.plat.bas.comm.render.BusiBWcodeNameRender({})"/>

3、导出表名及标题

Table 都要配置Title并且用宏，

（1)  若当前界面带有父级标题（分组标题、TAB标题）的，则配置父级标题，如：

![](E:\Apple\TyporaMD\Img\1753405940284-1.png)

（2）对于一个界面中就一个Grid表的 使用其界面名称（入口、工作台、查询、配置界面等）

![](E:\Apple\TyporaMD\Img\1753405942483-4.png)

（3）列表二级标题导出时合并为一级标题已做了适配，可在 GridTable 增加 xprops="xlsExportOpts:1" 属性即可。